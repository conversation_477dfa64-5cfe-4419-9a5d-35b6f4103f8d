# Vibe Coder Environment Variables
# Copy this file to .env and fill in your actual API keys

# DeepSeek API (for reasoning-focused tasks)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# OpenRouter API (for accessing multiple models including Gemini)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Tavily API (for web search functionality)
TAVILY_API_KEY=your_tavily_api_key_here

# Optional: OpenAI API (if you want to use OpenAI models directly)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Anthropic API (if you want to use Claude models directly)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Custom base URLs (uncomment and modify if needed)
# DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
# OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
# OPENAI_BASE_URL=https://api.openai.com/v1
# ANTHROPIC_BASE_URL=https://api.anthropic.com
