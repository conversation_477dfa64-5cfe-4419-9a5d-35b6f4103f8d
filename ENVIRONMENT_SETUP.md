# Environment Setup Guide for Vibe Coder

This guide explains how to configure your environment variables and assign different LLM providers to different agents in Vibe Coder.

## 🔑 Environment Variables

Vibe Coder uses environment variables to securely store API keys. You have **two options** for setting these up:

### Option 1: System Environment Variables (Recommended)

Set these as system environment variables on your machine:

```bash
# Windows (Command Prompt)
set DEEPSEEK_API_KEY=your_deepseek_api_key_here
set OPENROUTER_API_KEY=your_openrouter_api_key_here
set TAVILY_API_KEY=your_tavily_api_key_here

# Windows (PowerShell)
$env:DEEPSEEK_API_KEY="your_deepseek_api_key_here"
$env:OPENROUTER_API_KEY="your_openrouter_api_key_here"
$env:TAVILY_API_KEY="your_tavily_api_key_here"

# Linux/macOS
export DEEPSEEK_API_KEY="your_deepseek_api_key_here"
export OPENROUTER_API_KEY="your_openrouter_api_key_here"
export TAVILY_API_KEY="your_tavily_api_key_here"
```

### Option 2: .env File

1. Copy the example file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your actual API keys:
   ```bash
   DEEPSEEK_API_KEY=your_deepseek_api_key_here
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   TAVILY_API_KEY=your_tavily_api_key_here
   ```

## 🤖 Agent-to-LLM Assignment

Vibe Coder allows you to assign different LLM providers to different agents. The default configuration is:

| Agent | Provider | Model | Purpose |
|-------|----------|-------|---------|
| **CoderAgent** | DeepSeek | `deepseek-reasoner` | Code generation with reasoning |
| **TaskDecomposer** | OpenRouter | `google/gemini-2.0-flash-exp` | Task planning and decomposition |
| **MetaAgent** | OpenRouter | `google/gemini-pro-1.5` | Self-reflection and error correction |
| **Web Search** | Tavily | N/A | Web search functionality |

## 📝 Configuration File

When you first run Vibe Coder, it creates a `config.yaml` file with the following structure:

```yaml
llm_providers:
  default_provider: "openrouter"
  
  deepseek:
    api_key: "${DEEPSEEK_API_KEY}"
    base_url: "https://api.deepseek.com/v1"
    coder_model_name: "deepseek-reasoner"
    supervisor_model_name: "deepseek-reasoner"
  
  openrouter:
    api_key: "${OPENROUTER_API_KEY}"
    base_url: "https://openrouter.ai/api/v1"
    coder_model_name: "google/gemini-2.0-flash-exp:free"
    supervisor_model_name: "google/gemini-pro-1.5"
  
  openai:
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    coder_model_name: "gpt-4-turbo"
    supervisor_model_name: "gpt-4o"
  
  anthropic:
    api_key: "${ANTHROPIC_API_KEY}"
    base_url: "https://api.anthropic.com"
    coder_model_name: "claude-3-sonnet-20240229"
    supervisor_model_name: "claude-3-opus-20240229"
  
  tavily:
    api_key: "${TAVILY_API_KEY}"

agent_assignments:
  coder_agent: "deepseek"
  task_decomposer: "openrouter"
  meta_agent: "openrouter"
  web_search: "tavily"
```

## 🔧 Customizing Agent Assignments

To change which LLM provider an agent uses, modify the `agent_assignments` section in `config.yaml`:

```yaml
agent_assignments:
  coder_agent: "openai"        # Use OpenAI for coding
  task_decomposer: "anthropic" # Use Claude for task decomposition
  meta_agent: "deepseek"       # Use DeepSeek for reflection
  web_search: "tavily"         # Keep Tavily for web search
```

## 🧪 Testing Your Configuration

Run the configuration test script to verify everything is set up correctly:

```bash
python test_env_config.py
```

This will check:
- ✅ Environment variables are loaded
- ✅ Configuration is valid
- ✅ Agents can initialize with their assigned LLMs

## 🔍 Available Models

### DeepSeek (via DeepSeek API)
- `deepseek-reasoner` - Advanced reasoning model

### OpenRouter (Multiple Providers)
- `google/gemini-2.0-flash-exp:free` - Fast Gemini model (free tier)
- `google/gemini-pro-1.5` - Advanced Gemini model
- `anthropic/claude-3-sonnet` - Claude Sonnet
- `openai/gpt-4-turbo` - GPT-4 Turbo
- Many more available at [OpenRouter](https://openrouter.ai/models)

### OpenAI (Direct)
- `gpt-4-turbo` - Latest GPT-4 Turbo
- `gpt-4o` - GPT-4 Omni
- `gpt-3.5-turbo` - GPT-3.5 Turbo

### Anthropic (Direct)
- `claude-3-opus-20240229` - Most capable Claude model
- `claude-3-sonnet-20240229` - Balanced Claude model
- `claude-3-haiku-20240307` - Fast Claude model

## 💡 Best Practices

1. **Use environment variables** instead of hardcoding API keys
2. **Test different model combinations** to find what works best for your use case
3. **Monitor API usage** and costs across different providers
4. **Keep backup providers** configured in case one service is down
5. **Use the free tier models** (like Gemini free) for development and testing

## 🚨 Security Notes

- Never commit API keys to version control
- The `.env` file is automatically ignored by git
- Environment variables are the most secure option
- Rotate your API keys regularly

## 🔧 Troubleshooting

### "API key not configured" error
- Check that your environment variables are set correctly
- Verify the variable names match exactly (case-sensitive)
- Restart your terminal/IDE after setting environment variables

### "Unsupported LLM provider" error
- Check that the provider name in `agent_assignments` matches a provider in `llm_providers`
- Verify the provider configuration is complete

### Model not found errors
- Check that the model name is correct for the provider
- Some models may require special access or billing setup

## 📞 Getting API Keys

- **DeepSeek**: [https://platform.deepseek.com/](https://platform.deepseek.com/)
- **OpenRouter**: [https://openrouter.ai/](https://openrouter.ai/)
- **Tavily**: [https://tavily.com/](https://tavily.com/)
- **OpenAI**: [https://platform.openai.com/](https://platform.openai.com/)
- **Anthropic**: [https://console.anthropic.com/](https://console.anthropic.com/)

---

**Ready to code with AI? Run `python test_env_config.py` to verify your setup!** 🚀
