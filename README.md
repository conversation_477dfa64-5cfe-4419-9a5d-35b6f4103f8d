# Vibe Coder - AI Coding Assistant

An intelligent, hypothesis-driven AI coding assistant that breaks down complex programming tasks into manageable steps and executes them with self-reflection and error correction.

## 🌟 Features

- **Hypothesis-Driven Cognition**: Operates like a scientist - hypothesize, test, learn
- **Task Decomposition**: Automatically breaks down complex goals into actionable tasks
- **Multi-Agent Architecture**: Specialized agents for different aspects of coding
- **Self-Reflection**: Meta-agent analyzes failures and generates corrective actions
- **🧠 Vector Database**: Semantic memory with continuous code indexing and intelligent search
- **🔄 Continuous Learning**: Real-time monitoring and re-indexing of code changes
- **Code Analysis**: Deep syntactic understanding using Tree-sitter
- **Tool Integration**: Comprehensive toolkit for file operations, testing, and validation
- **Memory System**: Persistent learning from successes and failures
- **🌐 Multi-LLM Support**: DeepSeek, OpenRouter, OpenAI, Anthropic, and Tavily integration

## 🏗️ Architecture

### Core Agents
- **CoderAgent**: Executes coding tasks using available tools
- **TaskDecomposerAgent**: Breaks down high-level goals into task graphs
- **MetaAgent (Cognoscente)**: Handles reflection and self-correction
- **LibrarianAgent**: Intelligent context retrieval and code understanding
- **VerifierAgent**: Validation and quality assurance

### Key Components
- **Engine**: Task execution and workflow management
- **🧠 Vector Database**: Semantic memory with continuous code indexing
- **Memory**: Action hypothesis ledger and episodic memory
- **Tools**: File operations, shell commands, code analysis, web search
- **UI**: PyQt5-based graphical interface

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- API keys for LLM providers (DeepSeek, OpenRouter, OpenAI, Anthropic)
- Optional: Tavily API key for web search
- Optional: C compiler for Tree-sitter (usually pre-installed)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd viber_plus
   ```

2. **Create virtual environment**
   ```bash
   python -m venv viberplus_env
   source viberplus_env/bin/activate  # On Windows: viberplus_env\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure Environment Variables**

   Set up your API keys as environment variables (recommended):

   ```bash
   # Required for default configuration
   export DEEPSEEK_API_KEY="your_deepseek_api_key"
   export OPENROUTER_API_KEY="your_openrouter_api_key"

   # Optional
   export TAVILY_API_KEY="your_tavily_api_key"
   export OPENAI_API_KEY="your_openai_api_key"
   export ANTHROPIC_API_KEY="your_anthropic_api_key"
   ```

   Or copy `.env.example` to `.env` and fill in your keys:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

5. **Test Configuration**
   ```bash
   python test_env_config.py
   ```

### Usage

#### GUI Mode (Recommended)
```bash
python -m vibe_coder.main --gui
```

#### Command Line Mode
```bash
python -m vibe_coder.main --goal "Create a simple web scraper"
```

#### Test the System
```bash
# Test core functionality
python test_vibe_coder.py

# Test vector database system
python test_vector_system.py

# Test environment configuration
python test_env_config.py
```

## 🛠️ Available Tools

The system includes a comprehensive toolkit:

- **File Operations**: `write_file`, `read_file`, `list_directory_contents`
- **System Commands**: `execute_shell_command`
- **Code Quality**: `run_linter`, `run_unit_tests`
- **Code Analysis**: `analyze_code_structure` (Tree-sitter powered)
- **🧠 Semantic Search**: `search_code_context` (Vector database powered)
- **Research**: `web_search` (Tavily powered)

## 🧠 Vector Database & Long-term Memory

Vibe Coder features a sophisticated **vector database system** that provides semantic memory and continuous learning:

### 🔄 **Continuous Indexing**
- **Real-time Monitoring**: Automatically detects file changes using file system watchers
- **Smart Re-indexing**: Only re-indexes files that have actually changed (MD5-based detection)
- **Multi-language Support**: Python, JavaScript, TypeScript, Java, C++, C#, Ruby, Go, Rust
- **Background Processing**: Non-blocking indexing that doesn't interrupt your workflow

### 🎯 **Semantic Search**
- **Natural Language Queries**: Search code using plain English descriptions
- **Context Retrieval**: Find relevant code patterns and solutions from your project
- **Pattern Recognition**: AI learns from your coding patterns and conventions
- **Intelligent Suggestions**: Agents can reference similar code from your codebase

### 📊 **Performance**
- **Fast Indexing**: ~270 files/second during initial scan
- **Instant Search**: Sub-second semantic search across entire codebase
- **Efficient Storage**: In-memory vectors with metadata persistence
- **Scalable**: Grows automatically with your project

### 🔧 **Configuration**
```bash
# Automatic setup - no configuration needed!
# Uses TF-IDF embeddings by default
# Upgrades to OpenAI embeddings when OPENAI_API_KEY is available
```

For detailed information, see [VECTOR_DATABASE_GUIDE.md](VECTOR_DATABASE_GUIDE.md)

## 🚀 How It Works

1. **Goal Submission**: User provides a high-level goal
2. **Context Retrieval**: Vector database finds relevant code patterns
3. **Task Decomposition**: TaskDecomposer breaks it into actionable tasks
4. **Execution**: Tasks are executed in dependency order with intelligent context
5. **Validation**: Each task result is validated
6. **Reflection**: Failures trigger meta-cognitive analysis
7. **Correction**: System applies corrective actions and retries
8. **Learning**: All actions and outcomes are stored for future reference
9. **Continuous Indexing**: Code changes are automatically indexed for future use

## 📁 Project Structure

```
vibe_coder/
├── agents/           # Agent implementations
├── core/            # Core reflection and validation logic
├── data_models/     # Pydantic data models
├── engine/          # Execution engine and workflow management
├── memory/          # Memory and persistence systems
│   ├── action_ledger.py      # Action history tracking
│   ├── vector_store.py       # 🧠 Vector database implementation
│   ├── continuous_indexer.py # 🔄 Real-time code indexing
│   └── simple_embeddings.py  # TF-IDF embeddings fallback
├── prompts/         # Agent persona definitions
├── tools/           # Tool implementations and registry
├── ui/              # User interface components
├── config.py        # Configuration management
└── main.py          # Main entry point

data/                # Runtime data storage
├── action_ledger.db # SQLite database for action history
├── vector_store/    # 🧠 Semantic memory storage
│   ├── file_hashes.json     # File change detection
│   └── metadata.json        # Index statistics
└── project_state.json # Current project state

vendor/              # External dependencies
└── tree-sitter-grammars/ # Language grammars for code analysis
```

## 🔧 Configuration

The system uses environment variables and YAML configuration:

### **Environment Variables** (Recommended)
```bash
DEEPSEEK_API_KEY=your_deepseek_key      # For advanced reasoning
OPENROUTER_API_KEY=your_openrouter_key  # For fast task planning
TAVILY_API_KEY=your_tavily_key          # For web search
OPENAI_API_KEY=your_openai_key          # Optional: for embeddings
ANTHROPIC_API_KEY=your_anthropic_key    # Optional: for Claude models
```

### **Agent Assignments** (Auto-configured)
- **CoderAgent** → DeepSeek (deepseek-reasoner) for advanced code reasoning
- **TaskDecomposer** → OpenRouter (Gemini 2.0 Flash) for fast planning
- **MetaAgent** → OpenRouter (Gemini Pro 1.5) for reflection
- **Vector Database** → TF-IDF embeddings (upgrades to OpenAI when available)

### **Configuration Sections**
- **llm_providers**: Multi-LLM API configurations with environment variable support
- **agent_assignments**: Which LLM each agent uses
- **paths**: File and directory paths including vector store
- **settings**: System behavior settings

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Test core system functionality
python test_vibe_coder.py

# Test vector database and continuous indexing
python test_vector_system.py

# Test environment configuration and LLM setup
python test_env_config.py
```

For development testing:
```bash
pytest tests/
```

### **Test Coverage**
- ✅ Multi-LLM provider configuration
- ✅ Vector database functionality
- ✅ Continuous code indexing
- ✅ Semantic search capabilities
- ✅ Agent initialization and communication
- ✅ File system monitoring
- ✅ Environment variable handling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built on the LangChain and LangGraph frameworks
- Uses Tree-sitter for advanced code analysis
- Vector database powered by scikit-learn and OpenAI embeddings
- File monitoring with Watchdog
- Inspired by cognitive science and hypothesis-driven development
- UI built with PyQt5
- Multi-LLM support: DeepSeek, OpenRouter, OpenAI, Anthropic, Tavily

## 🐛 Troubleshooting

### Common Issues

1. **Environment variable errors**: Run `python test_env_config.py` to verify API key setup
2. **Tree-sitter compilation errors**: Ensure you have a C compiler installed
3. **Vector database issues**: Run `python test_vector_system.py` to test indexing
4. **Import errors**: Make sure all dependencies are installed: `pip install -r requirements.txt`
5. **LLM provider errors**: Check that your API keys are valid and have sufficient credits

### Getting Help

- **Test your setup**: Run all test scripts to identify issues
- **Check logs**: Review `logs/vibe_coder.log` for detailed error information
- **Vector database**: See `VECTOR_DATABASE_GUIDE.md` for advanced configuration
- **Environment setup**: See `ENVIRONMENT_SETUP.md` for detailed API key configuration
- **Enable debug logging**: Set `log_level: "DEBUG"` in config.yaml

---

**Vibe Coder** - Where AI meets thoughtful software development 🚀

### 🎯 **Key Highlights**
- 🧠 **Intelligent Memory**: Vector database with continuous learning
- 🔄 **Real-time Adaptation**: Automatically learns from your codebase
- 🌐 **Multi-LLM Power**: Best-in-class models for different tasks
- 🎯 **Context-Aware**: AI understands your project patterns
- 🚀 **Production Ready**: Comprehensive testing and error handling

*Built for developers who want AI that truly understands their code.*
