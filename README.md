# Vibe Coder - Strategic AI Development System

A sophisticated multi-agent AI system that combines strategic thinking, creative problem-solving, and continuous learning to tackle complex software development challenges. Built on the architect's vision of strategic intelligence with Muse vs Oracle dialectic, procedural wisdom, and ethical governance.

## 🌟 Key Features

### 🎯 **Strategic Intelligence**
- **Strategic Debate**: Muse vs Oracle dialectic creates productive tension between creativity and risk assessment
- **Whiteboard Sessions**: Pre-execution strategic planning with lessons learned consultation
- **Wisdom Layer**: Ethical governance and strategic alignment for long-term project success
- **Socratic Supervision**: Critical analysis that challenges assumptions and reveals hidden flaws

### 🧠 **Three-Part Memory Architecture**
- **Semantic Memory**: Vector database with continuous code indexing and intelligent search
- **Episodic Memory**: Action-Hypothesis Ledger tracking experience and outcomes
- **Procedural Memory**: Lessons Learned system that synthesizes experience into reusable wisdom

### 🔄 **Advanced Workflows**
- **LangGraph Integration**: Sophisticated workflow orchestration with Think→Act→Verify→Reflect cycles
- **Task Graph Execution**: DAG-based parallel task execution with dependency management
- **Cognitive Loops**: Each task node follows a complete cognitive cycle for optimal results

### 🤖 **Multi-Agent Teams**
- **Strategic Team**: <PERSON> (creativity), <PERSON> (risk assessment), <PERSON> Layer (governance)
- **Planning Team**: Socratic Supervisor (critical analysis), Task Decomposer (planning)
- **Execution Team**: Coder (implementation), Librarian (context retrieval)
- **Reflection Team**: Meta-Agent (learning and adaptation)

## 🏗️ Architecture

### Strategic Agents
- **MuseAgent**: Unconstrained creative ideation and breakthrough thinking
- **OracleAgent**: Evidence-based risk assessment and failure pattern analysis
- **WisdomLayer**: Ethical governance, strategic alignment, and stakeholder impact assessment
- **StrategicDebate**: Orchestrates Muse vs Oracle dialectic for balanced decision-making

### Planning Agents
- **SocraticSupervisor**: Critical analysis, assumption challenging, and architectural review
- **TaskDecomposer**: Breaks down goals into atomic, verifiable, independent tasks

### Execution Agents
- **CoderAgent**: Executes coding tasks with dynamic tool selection
- **LibrarianAgent**: Intelligent context retrieval and code understanding

### Reflection Agents
- **MetaAgent**: Reflection, learning from failures, and corrective action generation

### Core Systems
- **WorkflowManager**: Orchestrates the complete strategic workflow sequence
- **WhiteboardSession**: Strategic planning phase with four-phase process
- **LessonsLearnedManager**: Procedural memory that synthesizes experience into wisdom
- **StrategicWorkflow & ExecutionWorkflow**: LangGraph-based workflow implementations
- **Vector Database**: Semantic memory with continuous code indexing

## 🚀 How It Works

### 🎯 **Integrated Workflow Sequence**
When you submit a goal, Vibe Coder executes a sophisticated three-phase sequence:

### **Phase 1: Strategic Planning** (WhiteboardSession via StrategicWorkflow)
1. **Lessons Consultation**: System consults procedural memory for relevant experience
2. **Strategic Debate**: Muse generates creative alternatives while Oracle assesses risks
3. **Socratic Review**: Critical analysis challenges assumptions and reveals flaws
4. **Strategy Synthesis**: Wisdom Layer synthesizes balanced approach with ethical governance

### **Phase 2: Task Decomposition** (TaskDecomposer with strategic context)
1. **Strategic Context Integration**: Decomposer receives strategic recommendations
2. **Task Graph Generation**: Goals decomposed into atomic, verifiable, independent tasks
3. **Dependency Analysis**: Tasks organized with proper dependency relationships
4. **Resource Allocation**: Tasks assigned to appropriate specialized agents

### **Phase 3: Task Execution** (ExecutionWorkflow with cognitive cycles)
1. **Cognitive Cycles**: Each task follows Think→Act→Verify→Reflect pattern
2. **Context Retrieval**: Librarian provides relevant code patterns from semantic memory
3. **Dynamic Adaptation**: Real-time learning and strategy adjustment
4. **Progress Monitoring**: Continuous tracking and status updates

### **Continuous Learning Loop**
1. **Experience Capture**: Action-Hypothesis Ledger records all decisions and outcomes
2. **Pattern Analysis**: Lessons Learned system identifies success patterns and failure modes
3. **Wisdom Synthesis**: Raw experience transformed into reusable best practices and anti-patterns
4. **Strategic Memory**: Future decisions informed by accumulated wisdom

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- API keys for LLM providers (DeepSeek, OpenRouter, OpenAI, Anthropic)
- Optional: Tavily API key for web search
- Optional: C compiler for Tree-sitter (usually pre-installed)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/robwise888/viberplus.git
   cd viberplus
   ```

2. **Create virtual environment**
   ```bash
   python -m venv viberplus_env
   source viberplus_env/bin/activate  # On Windows: viberplus_env\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure Environment Variables**

   Set up your API keys as environment variables (recommended):

   ```bash
   # Required for default configuration
   export DEEPSEEK_API_KEY="your_deepseek_api_key"
   export OPENROUTER_API_KEY="your_openrouter_api_key"

   # Optional
   export TAVILY_API_KEY="your_tavily_api_key"
   export OPENAI_API_KEY="your_openai_api_key"
   export ANTHROPIC_API_KEY="your_anthropic_api_key"
   ```

   Or copy `.env.example` to `.env` and fill in your keys:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

5. **Test Configuration**
   ```bash
   python test_env_config.py
   ```

### Usage

#### GUI Mode (Recommended)
```bash
python -m vibe_coder.main --gui
```

#### Command Line Mode
```bash
python -m vibe_coder.main --goal "Create a simple web scraper"
```

#### Test the System
```bash
# Test core functionality
python test_vibe_coder.py

# Test vector database system
python test_vector_system.py

# Test environment configuration
python test_env_config.py
```

## 🛠️ Available Tools

The system includes a comprehensive toolkit with dynamic tool selection:

- **File Operations**: `write_file`, `read_file`, `list_directory_contents`
- **System Commands**: `execute_shell_command`
- **Code Quality**: `run_linter`, `run_unit_tests`
- **Code Analysis**: `analyze_code_structure` (Tree-sitter powered)
- **🧠 Semantic Search**: `search_code_context` (Vector database powered)
- **Research**: `web_search` (Tavily powered)

## 🧠 Three-Part Memory Architecture

Vibe Coder features a sophisticated **three-part memory system** that enables true learning and wisdom accumulation:

### 🎯 **Semantic Memory (Vector Database)**
- **Real-time Monitoring**: Automatically detects file changes using file system watchers
- **Smart Re-indexing**: Only re-indexes files that have actually changed (MD5-based detection)
- **Multi-language Support**: Python, JavaScript, TypeScript, Java, C++, C#, Ruby, Go, Rust
- **Natural Language Queries**: Search code using plain English descriptions
- **Fast Performance**: ~270 files/second indexing, sub-second search

### 📚 **Episodic Memory (Action-Hypothesis Ledger)**
- **Experience Tracking**: Records every action taken and hypothesis tested
- **Outcome Correlation**: Links decisions to their actual results
- **Failure Analysis**: Detailed logging of what went wrong and why
- **Success Patterns**: Identifies approaches that consistently work
- **Reflection Foundation**: Provides data for meta-cognitive analysis

### 🎓 **Procedural Memory (Lessons Learned)**
- **Wisdom Synthesis**: Transforms raw experience into reusable patterns
- **Best Practice Identification**: Automatically identifies high-success approaches
- **Anti-Pattern Detection**: Recognizes and flags failure-prone patterns
- **Contextual Relevance**: Retrieves applicable lessons for current situations
- **Confidence Scoring**: Prioritizes lessons based on evidence strength

### 🔄 **Memory Integration**
- **Cross-Memory Learning**: Semantic patterns inform procedural wisdom
- **Experience-Driven Context**: Episodic memory guides semantic search
- **Adaptive Behavior**: System behavior evolves based on accumulated experience
- **Strategic Decision Making**: All three memory types inform strategic planning

For detailed information, see [VECTOR_DATABASE_GUIDE.md](VECTOR_DATABASE_GUIDE.md)

## 📁 Project Structure

```
vibe_coder/
├── core/                    # Functional team organization
│   ├── strategy/           # Strategic Team
│   │   ├── muse_agent.py          # Creative ideation agent
│   │   ├── oracle_agent.py        # Risk assessment agent
│   │   ├── wisdom_layer.py        # Ethical governance agent
│   │   └── strategic_debate.py    # Muse vs Oracle orchestration
│   ├── planning/           # Planning Team
│   │   └── socratic_supervisor.py # Critical analysis agent
│   ├── execution/          # Execution Team
│   │   ├── coder_agent.py         # Code implementation agent
│   │   └── librarian_agent.py     # Context retrieval agent
│   └── reflection/         # Reflection Team
│       └── meta_agent.py          # Learning and adaptation agent
├── memory/                 # Three-part memory architecture
│   ├── action_ledger.py           # 📚 Episodic memory
│   ├── vector_store.py            # 🎯 Semantic memory
│   ├── continuous_indexer.py      # 🔄 Real-time indexing
│   ├── lessons_learned.py         # 🎓 Procedural memory
│   └── simple_embeddings.py       # TF-IDF fallback
├── engine/                 # Workflow orchestration
│   ├── workflow_manager.py        # Strategic workflow orchestration
│   ├── graph_builder.py           # Task graph construction
│   ├── state.py                   # Enhanced agent state
│   └── whiteboard_session.py      # Strategic planning phase
├── workflows/              # LangGraph workflow definitions
│   ├── strategic_workflow.py      # Strategic planning workflow
│   └── execution_workflow.py      # Task execution workflow
├── agents/                 # Legacy agent implementations
├── data_models/            # Pydantic data models
├── prompts/                # Agent persona definitions
├── tools/                  # Tool implementations and registry
├── ui/                     # User interface components
├── config.py               # Configuration management
└── main.py                 # Main entry point

data/                       # Runtime data storage
├── action_ledger.db        # SQLite database for episodic memory
├── lessons_learned.json    # 🎓 Procedural memory storage
├── vector_store/           # 🎯 Semantic memory storage
│   ├── file_hashes.json           # File change detection
│   └── metadata.json              # Index statistics
└── project_state.json      # Current project state

vendor/                     # External dependencies
└── tree-sitter-grammars/   # Language grammars for code analysis
```

## 🔧 Configuration

The system uses environment variables and YAML configuration with intelligent agent assignments:

### **Environment Variables** (Recommended)
```bash
DEEPSEEK_API_KEY=your_deepseek_key      # For advanced reasoning and coding
OPENROUTER_API_KEY=your_openrouter_key  # For strategic planning and debate
TAVILY_API_KEY=your_tavily_key          # For web search capabilities
OPENAI_API_KEY=your_openai_key          # Optional: for embeddings and analysis
ANTHROPIC_API_KEY=your_anthropic_key    # Optional: for Claude models
```

### **Strategic Agent Assignments** (Auto-configured)
- **MuseAgent** → OpenRouter (Creative models) for unconstrained ideation
- **OracleAgent** → DeepSeek (Reasoning models) for risk assessment
- **WisdomLayer** → Anthropic (Claude) for ethical governance
- **SocraticSupervisor** → OpenRouter (Analytical models) for critical analysis
- **CoderAgent** → DeepSeek (deepseek-reasoner) for advanced code reasoning
- **TaskDecomposer** → OpenRouter (Gemini 2.0 Flash) for fast planning
- **MetaAgent** → OpenRouter (Gemini Pro 1.5) for reflection
- **Vector Database** → TF-IDF embeddings (upgrades to OpenAI when available)

### **Configuration Sections**
- **llm_providers**: Multi-LLM API configurations with environment variable support
- **agent_assignments**: Strategic assignment of specialized models to agents
- **paths**: File and directory paths including memory storage
- **settings**: System behavior and workflow settings
- **strategic_settings**: Configuration for debate rounds, confidence thresholds, lesson synthesis

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Test core system functionality
python test_vibe_coder.py

# Test vector database and continuous indexing
python test_vector_system.py

# Test environment configuration and LLM setup
python test_env_config.py

# Test strategic agents and workflows (NEW)
python test_strategic_system.py

# Test procedural memory system (NEW)
python test_lessons_learned.py
```

For development testing:
```bash
pytest tests/
```

### **Test Coverage**
- ✅ Strategic agent functionality (Muse, Oracle, Wisdom Layer)
- ✅ Strategic debate orchestration and synthesis
- ✅ Procedural memory and lessons learned system
- ✅ Whiteboard session and strategic planning
- ✅ Enhanced LangGraph workflows
- ✅ Three-part memory architecture integration
- ✅ Multi-LLM provider configuration
- ✅ Vector database functionality
- ✅ Continuous code indexing
- ✅ Semantic search capabilities
- ✅ Agent initialization and communication
- ✅ File system monitoring
- ✅ Environment variable handling

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built on the LangChain and LangGraph frameworks for sophisticated workflow orchestration
- Uses Tree-sitter for advanced code analysis and understanding
- Vector database powered by scikit-learn and OpenAI embeddings
- File monitoring with Watchdog for real-time code indexing
- Inspired by cognitive science, strategic thinking, and hypothesis-driven development
- UI built with PyQt5 for intuitive user interaction
- Multi-LLM support: DeepSeek, OpenRouter, OpenAI, Anthropic, Tavily
- Strategic architecture inspired by dialectical thinking and ethical governance principles

## 🐛 Troubleshooting

### Common Issues

1. **Environment variable errors**: Run `python test_env_config.py` to verify API key setup
2. **Strategic agent errors**: Run `python test_strategic_system.py` to test agent functionality
3. **Memory system issues**: Run `python test_lessons_learned.py` to test procedural memory
4. **Tree-sitter compilation errors**: Ensure you have a C compiler installed
5. **Vector database issues**: Run `python test_vector_system.py` to test indexing
6. **Import errors**: Make sure all dependencies are installed: `pip install -r requirements.txt`
7. **LLM provider errors**: Check that your API keys are valid and have sufficient credits

### Getting Help

- **Test your setup**: Run all test scripts to identify issues
- **Check logs**: Review `logs/vibe_coder.log` for detailed error information
- **Strategic system**: See `ARCHITECT_IMPLEMENTATION_SUMMARY.md` for architecture details
- **Vector database**: See `VECTOR_DATABASE_GUIDE.md` for advanced configuration
- **Environment setup**: See `ENVIRONMENT_SETUP.md` for detailed API key configuration
- **Enable debug logging**: Set `log_level: "DEBUG"` in config.yaml

---

**Vibe Coder** - Where Strategic AI meets Thoughtful Software Development 🚀

### 🎯 **Key Highlights**
- 🎯 **Strategic Intelligence**: Muse vs Oracle dialectic for balanced decision-making
- 🧠 **Three-Part Memory**: Semantic, Episodic, and Procedural memory architecture
- 🔄 **Continuous Learning**: System evolves and improves from every interaction
- 🤔 **Critical Thinking**: Socratic supervision challenges assumptions and reveals flaws
- 🏛️ **Ethical Governance**: Wisdom Layer ensures strategic alignment and responsible AI
- 🌐 **Multi-LLM Power**: Best-in-class models strategically assigned to specialized agents
- 🎯 **Context-Aware**: AI understands your project patterns and learns from experience
- 🔗 **Fully Integrated**: Complete workflow orchestration from strategic planning to execution
- 🚀 **Production Ready**: Comprehensive testing, error handling, and sophisticated workflows

*Built for developers who want AI that thinks strategically, learns continuously, and develops wisdom over time.*

## 🌟 **What Makes Vibe Coder Unique**

Unlike traditional coding assistants, Vibe Coder embodies **strategic intelligence**:

- **🎭 Creative vs Critical**: Muse agent generates breakthrough ideas while Oracle agent assesses risks
- **🧠 Memory that Learns**: Three-part memory system that accumulates wisdom from experience
- **🤔 Socratic Questioning**: Challenges assumptions and reveals hidden flaws in plans
- **🏛️ Ethical Governance**: Wisdom Layer ensures decisions align with long-term goals and values
- **🔄 Adaptive Workflows**: LangGraph-based system that evolves its approach based on results

**This isn't just code generation - it's strategic software development with AI that gets smarter over time.**
