import os

def generate_project_review(root_dir, output_file, exclude_dirs=None, exclude_files=None):
    """
    Walks through a project directory and writes its structure and file contents
    to a single output file, excluding specified directories and files.
    """
    if exclude_dirs is None:
        exclude_dirs = {'.git', '__pycache__', 'viberplus_env', 'vendor', 'build', 'data'}
    if exclude_files is None:
        exclude_files = {'.DS_Store', 'my-languages.so', 'my-languages.dll', 'create_review_file.py', 'project_review.txt'}

    with open(output_file, 'w', encoding='utf-8', errors='ignore') as f:
        for dirpath, dirnames, filenames in os.walk(root_dir):
            # --- Exclude specified directories ---
            dirnames[:] = [d for d in dirnames if d not in exclude_dirs]

            for filename in filenames:
                if filename in exclude_files:
                    continue

                file_path = os.path.join(dirpath, filename)
                relative_path = os.path.relpath(file_path, root_dir).replace('\\', '/')

                # --- Write file header ---
                f.write("=" * 80 + "\n")
                f.write(f"### FILE: {relative_path}\n")
                f.write("=" * 80 + "\n\n")

                # --- Write file content ---
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as content_file:
                        f.write(content_file.read())
                    f.write("\n\n")
                except Exception as e:
                    f.write(f"*** Could not read file: {e} ***\n\n")

if __name__ == '__main__':
    project_root = os.path.dirname(os.path.abspath(__file__))
    output_filename = 'project_review.txt'
    print(f"Generating project review for '{project_root}'...")
    generate_project_review(project_root, output_filename)
    print(f"Review file '{output_filename}' created successfully.")