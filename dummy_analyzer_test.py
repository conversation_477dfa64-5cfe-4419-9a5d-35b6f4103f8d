
import os
import sys

class MyClass:
    def __init__(self, value):
        self.value = value

    def get_value(self):
        # This is a method
        return self.value

def my_global_function(name: str, age: int) -> str:
    '''This is a global function.'''
    local_var = f"Hello, {name}! You are {age}."
    print(local_var)
    if age > 18:
        return "Adult"
    else:
        return "Minor"

def another_function():
    pass
