2025-06-15 16:17:29,266 - vibe_coder.main - INFO - Logging configured: level=INFO, file=./logs/vibe_coder.log
2025-06-15 16:17:29,266 - vibe_coder.main - INFO - AgentBackend initialized
2025-06-15 16:17:29,267 - vibe_coder.ui.main_window - INFO - Backend setup complete
2025-06-15 16:17:29,423 - vibe_coder.tools.code_analysis - WARNING - No language package available for 'ruby'. Supported languages: ['python', 'javascript']
2025-06-15 16:17:29,424 - vibe_coder.tools.code_analysis - WARNING - No language package available for 'typescript'. Supported languages: ['python', 'javascript']
2025-06-15 16:17:29,424 - vibe_coder.tools.code_analysis - WARNING - No language package available for 'cpp'. Supported languages: ['python', 'javascript']
2025-06-15 16:17:29,424 - vibe_coder.tools.code_analysis - WARNING - No language package available for 'c'. Supported languages: ['python', 'javascript']
2025-06-15 16:17:29,424 - vibe_coder.tools.code_analysis - WARNING - No language package available for 'php'. Supported languages: ['python', 'javascript']
2025-06-15 16:17:29,424 - vibe_coder.tools.code_analysis - WARNING - No language package available for 'rust'. Supported languages: ['python', 'javascript']
2025-06-15 16:17:29,425 - vibe_coder.tools.code_analysis - WARNING - No language package available for 'go'. Supported languages: ['python', 'javascript']
2025-06-15 16:17:29,434 - vibe_coder.tools.code_analysis - INFO - Successfully loaded tree-sitter language 'python' from package 'tree_sitter_python'.
2025-06-15 16:17:29,434 - vibe_coder.tools.code_analysis - WARNING - No language package available for 'java'. Supported languages: ['python', 'javascript']
2025-06-15 16:17:29,445 - vibe_coder.tools.code_analysis - INFO - Successfully loaded tree-sitter language 'javascript' from package 'tree_sitter_javascript'.
2025-06-15 16:17:29,446 - vibe_coder.memory.action_ledger - INFO - Database connection established to data\action_ledger.db
2025-06-15 16:17:29,450 - vibe_coder.memory.action_ledger - INFO - Database setup complete. 'hypothesis_log' table ensured.
2025-06-15 16:17:29,450 - vibe_coder.memory.action_ledger - INFO - Database setup complete. 'hypothesis_log' table ensured.
2025-06-15 16:17:29,631 - vibe_coder.main - INFO - All agents initialized successfully
2025-06-15 16:17:29,632 - vibe_coder.engine.simple_driver - INFO - SimpleDriver initialized
2025-06-15 16:17:29,632 - vibe_coder.main - INFO - Execution engine ready
