#!/usr/bin/env python3
"""
Test script to verify environment variable configuration for Vibe Coder.
"""

import os
import sys
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from vibe_coder.config import ConfigManager

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_environment_variables():
    """Test that environment variables are properly loaded."""
    logger.info("Testing environment variable configuration...")
    
    # Check if environment variables are set
    env_vars = {
        "DEEPSEEK_API_KEY": os.getenv("DEEPSEEK_API_KEY"),
        "OPENROUTER_API_KEY": os.getenv("OPENROUTER_API_KEY"),
        "TAVILY_API_KEY": os.getenv("TAVILY_API_KEY"),
        "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
        "ANTHROPIC_API_KEY": os.getenv("ANTHROPIC_API_KEY")
    }
    
    logger.info("Environment variables status:")
    for var_name, var_value in env_vars.items():
        status = "✅ SET" if var_value else "❌ NOT SET"
        masked_value = f"{var_value[:8]}..." if var_value else "None"
        logger.info(f"  {var_name}: {status} ({masked_value})")
    
    return env_vars


def test_config_loading():
    """Test configuration loading with environment variables."""
    logger.info("Testing configuration loading...")
    
    try:
        # Create config manager
        config = ConfigManager()
        
        # Test provider configurations
        providers = ["deepseek", "openrouter", "openai", "anthropic"]
        
        logger.info("Provider configurations:")
        for provider in providers:
            try:
                provider_config = config.get_llm_config(provider)
                api_key = provider_config.get("api_key", "")
                model = provider_config.get("coder_model_name", "")
                base_url = provider_config.get("base_url", "")
                
                # Mask API key for security
                masked_key = f"{api_key[:8]}..." if api_key and len(api_key) > 8 else "NOT SET"
                
                logger.info(f"  {provider}:")
                logger.info(f"    API Key: {masked_key}")
                logger.info(f"    Model: {model}")
                logger.info(f"    Base URL: {base_url}")
                
            except Exception as e:
                logger.error(f"  {provider}: ERROR - {e}")
        
        # Test agent assignments
        logger.info("Agent assignments:")
        agents = ["coder_agent", "task_decomposer", "meta_agent"]
        
        for agent in agents:
            try:
                agent_config = config.get_agent_llm_config(agent)
                assigned_provider = config.get(f"agent_assignments.{agent}", "default")
                model = agent_config.get("coder_model_name", "")
                
                logger.info(f"  {agent}: {assigned_provider} ({model})")
                
            except Exception as e:
                logger.error(f"  {agent}: ERROR - {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Configuration loading failed: {e}")
        return False


def test_agent_initialization():
    """Test that agents can be initialized with the new configuration."""
    logger.info("Testing agent initialization...")
    
    try:
        from vibe_coder.agents.coder_agent import CoderAgent
        from vibe_coder.tools.tool_registry import ToolRegistry
        
        # Create config and tool registry
        config = ConfigManager()
        tool_registry = ToolRegistry()
        
        # Test CoderAgent initialization
        coder_agent = CoderAgent(config.config, tool_registry)
        coder_agent.agent_name = "coder_agent"
        
        # Test that the LLM client was created successfully
        if hasattr(coder_agent, 'llm') and coder_agent.llm:
            logger.info("✅ CoderAgent initialized successfully with LLM client")
            
            # Try to get model info
            try:
                model_name = getattr(coder_agent.llm, 'model_name', 'Unknown')
                logger.info(f"  Model: {model_name}")
            except:
                logger.info("  Model info not available")
                
            return True
        else:
            logger.error("❌ CoderAgent LLM client not initialized")
            return False
            
    except Exception as e:
        logger.error(f"Agent initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("=" * 60)
    print("Vibe Coder Environment Configuration Test")
    print("=" * 60)
    
    # Test 1: Environment variables
    env_vars = test_environment_variables()
    
    print("\n" + "=" * 60)
    
    # Test 2: Configuration loading
    config_success = test_config_loading()
    
    print("\n" + "=" * 60)
    
    # Test 3: Agent initialization (only if config loaded successfully)
    agent_success = False
    if config_success:
        agent_success = test_agent_initialization()
    else:
        logger.warning("Skipping agent initialization test due to config failure")
    
    print("\n" + "=" * 60)
    
    # Summary
    logger.info("Test Summary:")
    logger.info(f"  Environment Variables: {'✅ PASS' if any(env_vars.values()) else '❌ FAIL'}")
    logger.info(f"  Configuration Loading: {'✅ PASS' if config_success else '❌ FAIL'}")
    logger.info(f"  Agent Initialization: {'✅ PASS' if agent_success else '❌ FAIL'}")
    
    if config_success and agent_success:
        logger.info("🎉 All tests passed! Your environment is configured correctly.")
    else:
        logger.warning("⚠️  Some tests failed. Check your environment variables and configuration.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
