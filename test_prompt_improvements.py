#!/usr/bin/env python3
"""
Test script to validate the improved agent prompts based on architect's suggestions.
"""

import os
import sys
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_prompt_imports():
    """Test that all prompts can be imported successfully."""
    logger.info("Testing prompt imports...")
    
    try:
        from vibe_coder.prompts.core_prompts import (
            CODER_SYSTEM_PROMPT,
            SOCRATIC_SUPERVISOR_PROMPT,
            TASK_DECOMPOSER_PROMPT,
            MUSE_PROMPT,
            ORACLE_PROMPT,
            WISDOM_LAYER_PROMPT,
            LIBRARIAN_PROMPT,
            META_AGENT_PROMPT
        )
        
        logger.info("✅ All prompts imported successfully")
        return True
        
    except ImportError as e:
        logger.error(f"❌ Failed to import prompts: {e}")
        return False


def test_verifier_prompt_removed():
    """Test that VERIFIER_PROMPT has been removed as recommended."""
    logger.info("Testing VERIFIER_PROMPT removal...")
    
    try:
        from vibe_coder.prompts.core_prompts import VERIFIER_PROMPT
        logger.error("❌ VERIFIER_PROMPT still exists - should have been removed")
        return False
    except ImportError:
        logger.info("✅ VERIFIER_PROMPT successfully removed")
        return True


def test_coder_prompt_improvements():
    """Test improvements to CODER_SYSTEM_PROMPT."""
    logger.info("Testing CODER_SYSTEM_PROMPT improvements...")
    
    from vibe_coder.prompts.core_prompts import CODER_SYSTEM_PROMPT
    
    # Check for dynamic tool list instruction
    if "You will be provided with a list of available tools" in CODER_SYSTEM_PROMPT:
        logger.info("✅ Dynamic tool list instruction present")
        dynamic_tools = True
    else:
        logger.error("❌ Missing dynamic tool list instruction")
        dynamic_tools = False
    
    # Check that hard-coded tool list is removed
    if "Available tools include:" not in CODER_SYSTEM_PROMPT:
        logger.info("✅ Hard-coded tool list removed")
        no_hardcoded = True
    else:
        logger.error("❌ Hard-coded tool list still present")
        no_hardcoded = False
    
    # Check for ToolCallRequest schema reference
    if "ToolCallRequest" in CODER_SYSTEM_PROMPT:
        logger.info("✅ ToolCallRequest schema reference present")
        schema_ref = True
    else:
        logger.error("❌ Missing ToolCallRequest schema reference")
        schema_ref = False
    
    return dynamic_tools and no_hardcoded and schema_ref


def test_task_decomposer_improvements():
    """Test improvements to TASK_DECOMPOSER_PROMPT."""
    logger.info("Testing TASK_DECOMPOSER_PROMPT improvements...")
    
    from vibe_coder.prompts.core_prompts import TASK_DECOMPOSER_PROMPT
    
    # Check for task quality principles
    principles = ["Atomic:", "Verifiable:", "Independent:"]
    found_principles = []
    
    for principle in principles:
        if principle in TASK_DECOMPOSER_PROMPT:
            found_principles.append(principle)
            logger.info(f"✅ Found principle: {principle}")
        else:
            logger.error(f"❌ Missing principle: {principle}")
    
    return len(found_principles) == len(principles)


def test_oracle_muse_dialectic():
    """Test Oracle/Muse dialectic improvements."""
    logger.info("Testing Oracle/Muse dialectic improvements...")
    
    from vibe_coder.prompts.core_prompts import ORACLE_PROMPT, MUSE_PROMPT
    
    # Check Oracle as risk assessment agent
    oracle_checks = [
        "risk assessment" in ORACLE_PROMPT,
        "why might this fail" in ORACLE_PROMPT,
        "evidence" in ORACLE_PROMPT
    ]
    
    # Check Muse as unconstrained ideation agent
    muse_checks = [
        "unconstrained" in MUSE_PROMPT,
        "blue sky" in MUSE_PROMPT,
        "completely different way" in MUSE_PROMPT
    ]
    
    oracle_success = all(oracle_checks)
    muse_success = all(muse_checks)
    
    if oracle_success:
        logger.info("✅ Oracle prompt properly focused on risk assessment")
    else:
        logger.error("❌ Oracle prompt missing risk assessment focus")
    
    if muse_success:
        logger.info("✅ Muse prompt properly focused on unconstrained ideation")
    else:
        logger.error("❌ Muse prompt missing unconstrained ideation focus")
    
    return oracle_success and muse_success


def test_wisdom_layer_improvements():
    """Test Wisdom Layer prompt improvements."""
    logger.info("Testing WISDOM_LAYER_PROMPT improvements...")
    
    from vibe_coder.prompts.core_prompts import WISDOM_LAYER_PROMPT
    
    # Check for strategic and ethical focus
    checks = [
        "ethical governance" in WISDOM_LAYER_PROMPT,
        "strategic alignment" in WISDOM_LAYER_PROMPT,
        "long-term project goals" in WISDOM_LAYER_PROMPT,
        "unintended negative consequences" in WISDOM_LAYER_PROMPT
    ]
    
    success = all(checks)
    
    if success:
        logger.info("✅ Wisdom Layer properly focused on strategic/ethical governance")
    else:
        logger.error("❌ Wisdom Layer missing strategic/ethical focus")
        for i, check in enumerate(checks):
            if not check:
                logger.error(f"  Missing element {i+1}")
    
    return success


def test_meta_agent_improvements():
    """Test Meta Agent prompt improvements."""
    logger.info("Testing META_AGENT_PROMPT improvements...")
    
    from vibe_coder.prompts.core_prompts import META_AGENT_PROMPT
    
    # Check for Action-Hypothesis Ledger reference
    if "Action-Hypothesis Ledger" in META_AGENT_PROMPT:
        logger.info("✅ Action-Hypothesis Ledger reference present")
        ledger_ref = True
    else:
        logger.error("❌ Missing Action-Hypothesis Ledger reference")
        ledger_ref = False
    
    # Check for memory system grounding
    if "memory system" in META_AGENT_PROMPT:
        logger.info("✅ Memory system grounding present")
        memory_ref = True
    else:
        logger.error("❌ Missing memory system grounding")
        memory_ref = False
    
    return ledger_ref and memory_ref


def test_socratic_supervisor_unchanged():
    """Test that Socratic Supervisor prompt remains unchanged (as recommended)."""
    logger.info("Testing SOCRATIC_SUPERVISOR_PROMPT (should be unchanged)...")
    
    from vibe_coder.prompts.core_prompts import SOCRATIC_SUPERVISOR_PROMPT
    
    # Check for key elements that should remain
    key_elements = [
        "Socratic Supervisor",
        "master software architect",
        "critique and challenge",
        "not to write code",
        "hidden assumptions"
    ]
    
    found_elements = []
    for element in key_elements:
        if element in SOCRATIC_SUPERVISOR_PROMPT:
            found_elements.append(element)
    
    success = len(found_elements) == len(key_elements)
    
    if success:
        logger.info("✅ Socratic Supervisor prompt maintains its excellent structure")
    else:
        logger.error(f"❌ Socratic Supervisor prompt missing elements: {set(key_elements) - set(found_elements)}")
    
    return success


def test_librarian_unchanged():
    """Test that Librarian prompt remains unchanged (as recommended)."""
    logger.info("Testing LIBRARIAN_PROMPT (should be unchanged)...")
    
    from vibe_coder.prompts.core_prompts import LIBRARIAN_PROMPT
    
    # Check for key elements
    key_elements = [
        "Librarian Agent",
        "context retrieval",
        "code understanding",
        "precise, relevant context"
    ]
    
    found_elements = []
    for element in key_elements:
        if element in LIBRARIAN_PROMPT:
            found_elements.append(element)
    
    success = len(found_elements) == len(key_elements)
    
    if success:
        logger.info("✅ Librarian prompt maintains its excellent structure")
    else:
        logger.error(f"❌ Librarian prompt missing elements: {set(key_elements) - set(found_elements)}")
    
    return success


def main():
    """Main test function."""
    print("=" * 60)
    print("Vibe Coder Prompt Improvements Validation")
    print("=" * 60)
    
    tests = [
        ("Prompt Imports", test_prompt_imports),
        ("VERIFIER_PROMPT Removal", test_verifier_prompt_removed),
        ("CODER_SYSTEM_PROMPT Improvements", test_coder_prompt_improvements),
        ("TASK_DECOMPOSER_PROMPT Improvements", test_task_decomposer_improvements),
        ("Oracle/Muse Dialectic", test_oracle_muse_dialectic),
        ("WISDOM_LAYER_PROMPT Improvements", test_wisdom_layer_improvements),
        ("META_AGENT_PROMPT Improvements", test_meta_agent_improvements),
        ("SOCRATIC_SUPERVISOR_PROMPT (Unchanged)", test_socratic_supervisor_unchanged),
        ("LIBRARIAN_PROMPT (Unchanged)", test_librarian_unchanged),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
        print("-" * 40)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All prompt improvements validated successfully!")
        print("\n📋 Architect's Suggestions Successfully Implemented:")
        print("  ✅ CODER_SYSTEM_PROMPT: Dynamic tool list, schema reference")
        print("  ✅ TASK_DECOMPOSER_PROMPT: Atomic, verifiable, independent principles")
        print("  ✅ ORACLE_PROMPT: Risk assessment and evidence-based analysis")
        print("  ✅ MUSE_PROMPT: Unconstrained 'blue sky' ideation")
        print("  ✅ WISDOM_LAYER_PROMPT: Strategic and ethical governance")
        print("  ✅ META_AGENT_PROMPT: Action-Hypothesis Ledger integration")
        print("  ✅ VERIFIER_PROMPT: Removed (deterministic component)")
        print("  ✅ SOCRATIC_SUPERVISOR_PROMPT: Preserved (excellent as-is)")
        print("  ✅ LIBRARIAN_PROMPT: Preserved (excellent as-is)")
    else:
        print("❌ Some prompt improvements failed validation.")
        print("\n💡 Check the specific test failures above for details.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
