import pytest
from pydantic import ValidationError
import datetime
import uuid

# Import all models from Module 1
from vibe_coder.data_models.project_models import Project<PERSON>harter
from vibe_coder.data_models.task_models import TaskIntent, TaskStatus, TaskNode, TaskGraphDefinition
from vibe_coder.data_models.cognitive_models import (
    HypothesisLogEntry,
    ClarificationQuestion,
    ClarificationBundle,
    UserResponse,
    UserResponseBundle
)
from vibe_coder.data_models.api_contracts import (
    CorrectiveAction,
    ToolCallRequest,
    AgentFailureReport
)

# --- Tests for project_models.py ---

def test_project_charter_valid():
    charter = ProjectCharter(goal="Build a cool app", project_scope="A very detailed scope.")
    assert charter.goal == "Build a cool app"
    assert charter.project_scope == "A very detailed scope."

def test_project_charter_goal_required():
    with pytest.raises(ValidationError):
        ProjectCharter(project_scope="A scope without a goal.") # goal is required

# --- Tests for task_models.py ---

def test_task_node_valid():
    node = TaskNode(
        id="task_001",
        description="Write a file",
        intent=TaskIntent.EXECUTE_TOOL,
        assigned_agent="CoderAgent",
        tool_name="write_file",
        tool_arguments={"path": "test.txt", "content": "hello"}
    )
    assert node.id == "task_001"
    assert node.status == TaskStatus.PENDING # Default value
    assert node.dependencies == [] # Default factory
    assert node.intent == TaskIntent.EXECUTE_TOOL

def test_task_node_missing_required_fields():
    with pytest.raises(ValidationError):
        TaskNode(id="task_002") # description, intent, assigned_agent are missing

def test_task_graph_definition_valid():
    node1 = TaskNode(id="t1", description="d1", intent=TaskIntent.EXECUTE_TOOL, assigned_agent="A1")
    node2 = TaskNode(id="t2", description="d2", intent=TaskIntent.VERIFY_STATE, assigned_agent="A2", dependencies=["t1"])
    graph_def = TaskGraphDefinition(start_node_id="t1", nodes=[node1, node2])
    assert graph_def.start_node_id == "t1"
    assert len(graph_def.nodes) == 2
    assert graph_def.get_node_by_id("t1") == node1
    assert graph_def.get_node_by_id("t3") is None

# --- Tests for cognitive_models.py ---

def test_hypothesis_log_entry_valid():
    entry_id = str(uuid.uuid4())
    entry = HypothesisLogEntry(
        entry_id=entry_id,
        task_id="task_abc",
        agent_name="ReflectorAgent",
        hypothesis_text="The previous attempt failed due to X.",
        action_taken="Retrying with Y modification."
    )
    assert entry.entry_id == entry_id
    assert isinstance(entry.timestamp, datetime.datetime) # Default factory

def test_clarification_question_valid():
    cq = ClarificationQuestion(question_id="q1", question_text="What is the target OS?")
    assert cq.question_text == "What is the target OS?"

def test_clarification_bundle_valid():
    q1 = ClarificationQuestion(question_id="q1", question_text="Q1")
    bundle = ClarificationBundle(bundle_id="b1", questions=[q1])
    assert len(bundle.questions) == 1

# --- Tests for api_contracts.py ---

def test_corrective_action_valid():
    action = CorrectiveAction(
        tool_name="execute_shell_command",
        arguments={"command": "pip install -r requirements.txt"},
        reasoning="The dependency was missing."
    )
    assert action.tool_name == "execute_shell_command"

def test_tool_call_request_valid():
    request = ToolCallRequest(
        thought="I need to write this content to a file.",
        tool_name="write_file",
        arguments={"file_path": "output.txt", "content": "Hello"}
    )
    assert request.tool_name == "write_file"
    assert request.arguments["content"] == "Hello"

def test_agent_failure_report_valid():
    report = AgentFailureReport(
        failed_agent_name="CoderAgent",
        task_id="task_xyz",
        error_message="Could not resolve dependencies."
    )
    assert report.failed_agent_name == "CoderAgent"

# Example of testing serialization and deserialization (optional for now, but good to know)
def test_task_node_serialization_deserialization():
    original_node_data = {
        "id": "task_srlz",
        "description": "Test serialization",
        "intent": "EXECUTE_TOOL",
        "assigned_agent": "Serializer",
        "status": "PENDING",
        "dependencies": [],
        "tool_name": "test_tool",
        "tool_arguments": {"arg1": "val1"}
    }
    node = TaskNode(**original_node_data)
    
    # Serialize to JSON
    node_json = node.model_dump_json()
    
    # Deserialize from JSON
    deserialized_node = TaskNode.model_validate_json(node_json)
    
    assert deserialized_node.id == original_node_data["id"]
    assert deserialized_node.description == original_node_data["description"]
    assert deserialized_node.intent == TaskIntent(original_node_data["intent"])
    assert deserialized_node.tool_arguments == original_node_data["tool_arguments"]

