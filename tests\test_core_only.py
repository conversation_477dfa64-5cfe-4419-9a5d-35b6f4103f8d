#!/usr/bin/env python3
"""
Simple test script for the Vibe Coder core functionality without GUI.
Tests the core components without PyQt5 dependencies.
"""

import logging
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_imports():
    """Test that all core modules can be imported."""
    logger.info("Testing core imports...")
    
    try:
        # Test data models
        from vibe_coder.data_models.project_models import ProjectCharter
        from vibe_coder.data_models.task_models import TaskGraph, TaskNode
        from vibe_coder.data_models.cognitive_models import HypothesisLogEntry
        from vibe_coder.data_models.api_contracts import ToolCallRequest, CorrectiveAction
        logger.info("✅ Data models imported successfully")
        
        # Test configuration
        from vibe_coder.config import ConfigManager
        logger.info("✅ Configuration module imported successfully")
        
        # Test tools
        from vibe_coder.tools.tool_registry import ToolRegistry
        from vibe_coder.tools.code_analysis import CodeAnalysisTool
        logger.info("✅ Tools imported successfully")
        
        # Test memory
        from vibe_coder.memory.action_ledger import ActionHypothesisLedger
        logger.info("✅ Memory system imported successfully")
        
        # Test agents
        from vibe_coder.agents.base_agent import BaseAgent
        from vibe_coder.agents.coder_agent import CoderAgent
        from vibe_coder.agents.task_decomposer import TaskDecomposerAgent
        from vibe_coder.agents.meta_agent import MetaAgentCognoscente
        logger.info("✅ Agents imported successfully")
        
        # Test engine
        from vibe_coder.engine.simple_driver import SimpleDriver
        logger.info("✅ Engine imported successfully")
        
        # Test prompts
        from vibe_coder.prompts.core_prompts import CODER_SYSTEM_PROMPT
        logger.info("✅ Prompts imported successfully")
        
        logger.info("🎉 All core imports successful!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_configuration():
    """Test configuration management."""
    logger.info("Testing configuration...")
    
    try:
        from vibe_coder.config import ConfigManager
        
        # Create config manager (will create default config)
        config = ConfigManager()
        
        # Test basic access
        default_provider = config.get("llm_providers.default_provider", "openai")
        logger.info(f"Default LLM provider: {default_provider}")
        
        # Test directory creation
        config.ensure_directories()
        
        logger.info("✅ Configuration test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_tool_registry():
    """Test tool registry functionality."""
    logger.info("Testing tool registry...")
    
    try:
        from vibe_coder.tools.tool_registry import ToolRegistry
        
        # Create tool registry
        registry = ToolRegistry()
        
        # Test tool listing
        tool_names = registry.list_tool_names()
        logger.info(f"Available tools: {tool_names}")
        
        # Test tool retrieval
        if tool_names:
            first_tool = registry.get_tool(tool_names[0])
            logger.info(f"Retrieved tool: {first_tool}")
        
        logger.info("✅ Tool registry test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Tool registry test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_models():
    """Test data model creation and validation."""
    logger.info("Testing data models...")
    
    try:
        from vibe_coder.data_models.project_models import ProjectCharter
        from vibe_coder.data_models.task_models import TaskGraph, TaskNode, TaskEdge
        from vibe_coder.data_models.cognitive_models import HypothesisLogEntry
        
        # Test ProjectCharter
        charter = ProjectCharter(
            goal="Test goal",
            description="Test description",
            requirements=["req1", "req2"],
            constraints=["constraint1"],
            deliverables=["deliverable1"]
        )
        logger.info(f"Created charter: {charter.goal}")
        
        # Test TaskNode
        task_node = TaskNode(
            id="test_task",
            description="Test task description",
            intent="EXECUTE_TOOL",
            assigned_agent="Coder"
        )
        logger.info(f"Created task node: {task_node.id}")
        
        # Test TaskGraph
        task_graph = TaskGraph(
            startNode="test_task",
            nodes=[task_node],
            edges=[]
        )
        logger.info(f"Created task graph with {len(task_graph.nodes)} nodes")
        
        # Test HypothesisLogEntry
        log_entry = HypothesisLogEntry(
            task_id="test_task",
            hypothesis_text="Test hypothesis",
            action_taken="test_action",
            is_success=True,
            result_data="test_result"
        )
        logger.info(f"Created log entry: {log_entry.task_id}")
        
        logger.info("✅ Data models test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Data models test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_system():
    """Test memory system functionality."""
    logger.info("Testing memory system...")
    
    try:
        from vibe_coder.memory.action_ledger import ActionHypothesisLedger
        from vibe_coder.data_models.cognitive_models import HypothesisLogEntry
        
        # Create ledger
        ledger = ActionHypothesisLedger(":memory:")  # Use in-memory database
        ledger.setup_database()
        
        # Test adding entry
        entry = HypothesisLogEntry(
            task_id="test_task",
            hypothesis_text="Test hypothesis",
            action_taken="test_action",
            is_success=True,
            result_data="test_result"
        )
        
        ledger.add_entry(entry)
        logger.info("Added entry to ledger")
        
        # Test retrieving history
        history = ledger.get_history_for_task("test_task")
        logger.info(f"Retrieved {len(history)} entries from history")
        
        logger.info("✅ Memory system test passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Memory system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("Vibe Coder Core System Test")
    print("=" * 60)
    
    tests = [
        ("Core Imports", test_imports),
        ("Configuration", test_configuration),
        ("Tool Registry", test_tool_registry),
        ("Data Models", test_data_models),
        ("Memory System", test_memory_system),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        if test_func():
            passed += 1
        print("-" * 40)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Core system is working correctly.")
    else:
        print("❌ Some tests failed. Check the logs above for details.")
    
    print("=" * 60)
