#!/usr/bin/env python3
"""
Simple test script for the Vibe Coder system.
Tests the core functionality without the GUI.
"""

import logging
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from vibe_coder.main import AgentBackend
from vibe_coder.data_models.project_models import ProjectCharter

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_basic_functionality():
    """Test basic Vibe Coder functionality."""
    logger.info("Starting Vibe Coder test...")
    
    try:
        # Create backend
        backend = AgentBackend()
        
        # Initialize system
        backend.initialize_system()
        
        # Test simple goal
        goal = "Create a simple Python script that prints 'Hello, Vibe Coder!'"
        description = "This is a test to verify the system works"
        
        logger.info(f"Submitting test goal: {goal}")
        
        # Submit goal (this will be async in real usage)
        backend.submit_goal(goal, description)
        
        logger.info("Test completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()


def test_system_status():
    """Test system status reporting."""
    logger.info("Testing system status...")
    
    try:
        backend = AgentBackend()
        backend.initialize_system()
        
        status = backend.get_system_status()
        logger.info(f"System status: {status}")
        
        # Verify expected components
        assert status["config_loaded"], "Config should be loaded"
        assert status["agents_count"] > 0, "Should have agents"
        assert status["tools_count"] > 0, "Should have tools"
        
        logger.info("System status test passed!")
        
    except Exception as e:
        logger.error(f"System status test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("=" * 60)
    print("Vibe Coder System Test")
    print("=" * 60)
    
    # Test 1: System status
    test_system_status()
    
    print("\n" + "=" * 60)
    
    # Test 2: Basic functionality
    test_basic_functionality()
    
    print("\n" + "=" * 60)
    print("Test completed!")
