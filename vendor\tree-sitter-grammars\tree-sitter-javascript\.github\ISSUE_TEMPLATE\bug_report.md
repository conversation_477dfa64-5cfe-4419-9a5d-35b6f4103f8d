---
name: Bug report
about: Report unexpected parsing results
title: ''
labels: 'bug'
assignees: ''

---

The following piece of code is valid but it is parsed incorrectly:

```javascript

```

Here's a link to the TypeScript Playground showing that the snippet above is valid JavaScript or TypeScript:
<!-- Please check your code at https://www.typescriptlang.org/play
     and paste the URL below. -->

<!-- Please run `tree-sitter parse YOUR_FILE` and show us the output. -->
The output of `tree-sitter parse` is the following:

```

```

<!-- If there is no `ERROR` or `MISSING` node in the output above,
     explain what you were expecting: -->

<!-- Name of the broken/missing feature, link to official
     documentation, and any other relevant info is appreciated: -->
