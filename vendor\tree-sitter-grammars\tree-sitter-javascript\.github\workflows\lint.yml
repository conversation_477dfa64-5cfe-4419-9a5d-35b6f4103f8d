name: Lint

on:
  push:
    branches: [master]
    paths:
      - grammar.js
  pull_request:
    paths:
      - grammar.js

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          cache: npm
          node-version: ${{vars.NODE_VERSION}}
      - name: Install modules
        run: npm ci --legacy-peer-deps
      - name: Run ESLint
        run: npm run lint
