[{"type": "declaration", "named": true, "subtypes": [{"type": "class_declaration", "named": true}, {"type": "function_declaration", "named": true}, {"type": "generator_function_declaration", "named": true}, {"type": "lexical_declaration", "named": true}, {"type": "variable_declaration", "named": true}]}, {"type": "expression", "named": true, "subtypes": [{"type": "assignment_expression", "named": true}, {"type": "augmented_assignment_expression", "named": true}, {"type": "await_expression", "named": true}, {"type": "binary_expression", "named": true}, {"type": "jsx_element", "named": true}, {"type": "jsx_self_closing_element", "named": true}, {"type": "new_expression", "named": true}, {"type": "primary_expression", "named": true}, {"type": "ternary_expression", "named": true}, {"type": "unary_expression", "named": true}, {"type": "update_expression", "named": true}, {"type": "yield_expression", "named": true}]}, {"type": "pattern", "named": true, "subtypes": [{"type": "array_pattern", "named": true}, {"type": "identifier", "named": true}, {"type": "member_expression", "named": true}, {"type": "object_pattern", "named": true}, {"type": "rest_pattern", "named": true}, {"type": "subscript_expression", "named": true}, {"type": "undefined", "named": true}]}, {"type": "primary_expression", "named": true, "subtypes": [{"type": "array", "named": true}, {"type": "arrow_function", "named": true}, {"type": "call_expression", "named": true}, {"type": "class", "named": true}, {"type": "false", "named": true}, {"type": "function_expression", "named": true}, {"type": "generator_function", "named": true}, {"type": "identifier", "named": true}, {"type": "member_expression", "named": true}, {"type": "meta_property", "named": true}, {"type": "null", "named": true}, {"type": "number", "named": true}, {"type": "object", "named": true}, {"type": "parenthesized_expression", "named": true}, {"type": "regex", "named": true}, {"type": "string", "named": true}, {"type": "subscript_expression", "named": true}, {"type": "super", "named": true}, {"type": "template_string", "named": true}, {"type": "this", "named": true}, {"type": "true", "named": true}, {"type": "undefined", "named": true}]}, {"type": "statement", "named": true, "subtypes": [{"type": "break_statement", "named": true}, {"type": "continue_statement", "named": true}, {"type": "debugger_statement", "named": true}, {"type": "declaration", "named": true}, {"type": "do_statement", "named": true}, {"type": "empty_statement", "named": true}, {"type": "export_statement", "named": true}, {"type": "expression_statement", "named": true}, {"type": "for_in_statement", "named": true}, {"type": "for_statement", "named": true}, {"type": "if_statement", "named": true}, {"type": "import_statement", "named": true}, {"type": "labeled_statement", "named": true}, {"type": "return_statement", "named": true}, {"type": "statement_block", "named": true}, {"type": "switch_statement", "named": true}, {"type": "throw_statement", "named": true}, {"type": "try_statement", "named": true}, {"type": "while_statement", "named": true}, {"type": "with_statement", "named": true}]}, {"type": "arguments", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "expression", "named": true}, {"type": "spread_element", "named": true}]}}, {"type": "array", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "expression", "named": true}, {"type": "spread_element", "named": true}]}}, {"type": "array_pattern", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "assignment_pattern", "named": true}, {"type": "pattern", "named": true}]}}, {"type": "arrow_function", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "statement_block", "named": true}]}, "parameter": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}, "parameters": {"multiple": false, "required": false, "types": [{"type": "formal_parameters", "named": true}]}}}, {"type": "assignment_expression", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "array_pattern", "named": true}, {"type": "identifier", "named": true}, {"type": "member_expression", "named": true}, {"type": "object_pattern", "named": true}, {"type": "parenthesized_expression", "named": true}, {"type": "subscript_expression", "named": true}, {"type": "undefined", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "assignment_pattern", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "pattern", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "augmented_assignment_expression", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "member_expression", "named": true}, {"type": "parenthesized_expression", "named": true}, {"type": "subscript_expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "%=", "named": false}, {"type": "&&=", "named": false}, {"type": "&=", "named": false}, {"type": "**=", "named": false}, {"type": "*=", "named": false}, {"type": "+=", "named": false}, {"type": "-=", "named": false}, {"type": "/=", "named": false}, {"type": "<<=", "named": false}, {"type": ">>=", "named": false}, {"type": ">>>=", "named": false}, {"type": "??=", "named": false}, {"type": "^=", "named": false}, {"type": "|=", "named": false}, {"type": "||=", "named": false}]}, "right": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "await_expression", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "binary_expression", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "private_property_identifier", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "!=", "named": false}, {"type": "!==", "named": false}, {"type": "%", "named": false}, {"type": "&", "named": false}, {"type": "&&", "named": false}, {"type": "*", "named": false}, {"type": "**", "named": false}, {"type": "+", "named": false}, {"type": "-", "named": false}, {"type": "/", "named": false}, {"type": "<", "named": false}, {"type": "<<", "named": false}, {"type": "<=", "named": false}, {"type": "==", "named": false}, {"type": "===", "named": false}, {"type": ">", "named": false}, {"type": ">=", "named": false}, {"type": ">>", "named": false}, {"type": ">>>", "named": false}, {"type": "??", "named": false}, {"type": "^", "named": false}, {"type": "in", "named": false}, {"type": "instanceof", "named": false}, {"type": "|", "named": false}, {"type": "||", "named": false}]}, "right": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "break_statement", "named": true, "fields": {"label": {"multiple": false, "required": false, "types": [{"type": "statement_identifier", "named": true}]}}}, {"type": "call_expression", "named": true, "fields": {"arguments": {"multiple": false, "required": true, "types": [{"type": "arguments", "named": true}, {"type": "template_string", "named": true}]}, "function": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "import", "named": true}]}, "optional_chain": {"multiple": false, "required": false, "types": [{"type": "optional_chain", "named": true}]}}}, {"type": "catch_clause", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}, "parameter": {"multiple": false, "required": false, "types": [{"type": "array_pattern", "named": true}, {"type": "identifier", "named": true}, {"type": "object_pattern", "named": true}]}}}, {"type": "class", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "class_body", "named": true}]}, "decorator": {"multiple": true, "required": false, "types": [{"type": "decorator", "named": true}]}, "name": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": false, "required": false, "types": [{"type": "class_heritage", "named": true}]}}, {"type": "class_body", "named": true, "fields": {"member": {"multiple": true, "required": false, "types": [{"type": "class_static_block", "named": true}, {"type": "field_definition", "named": true}, {"type": "method_definition", "named": true}]}}}, {"type": "class_declaration", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "class_body", "named": true}]}, "decorator": {"multiple": true, "required": false, "types": [{"type": "decorator", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": false, "required": false, "types": [{"type": "class_heritage", "named": true}]}}, {"type": "class_heritage", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "class_static_block", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}}}, {"type": "computed_property_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "continue_statement", "named": true, "fields": {"label": {"multiple": false, "required": false, "types": [{"type": "statement_identifier", "named": true}]}}}, {"type": "debugger_statement", "named": true, "fields": {}}, {"type": "decorator", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "call_expression", "named": true}, {"type": "identifier", "named": true}, {"type": "member_expression", "named": true}]}}, {"type": "do_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement", "named": true}]}, "condition": {"multiple": false, "required": true, "types": [{"type": "parenthesized_expression", "named": true}]}}}, {"type": "else_clause", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "statement", "named": true}]}}, {"type": "empty_statement", "named": true, "fields": {}}, {"type": "export_clause", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "export_specifier", "named": true}]}}, {"type": "export_specifier", "named": true, "fields": {"alias": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}, {"type": "string", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "string", "named": true}]}}}, {"type": "export_statement", "named": true, "fields": {"declaration": {"multiple": false, "required": false, "types": [{"type": "declaration", "named": true}]}, "decorator": {"multiple": true, "required": false, "types": [{"type": "decorator", "named": true}]}, "source": {"multiple": false, "required": false, "types": [{"type": "string", "named": true}]}, "value": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}]}}, "children": {"multiple": false, "required": false, "types": [{"type": "export_clause", "named": true}, {"type": "namespace_export", "named": true}]}}, {"type": "expression_statement", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}}, {"type": "field_definition", "named": true, "fields": {"decorator": {"multiple": true, "required": false, "types": [{"type": "decorator", "named": true}]}, "property": {"multiple": false, "required": true, "types": [{"type": "computed_property_name", "named": true}, {"type": "number", "named": true}, {"type": "private_property_identifier", "named": true}, {"type": "property_identifier", "named": true}, {"type": "string", "named": true}]}, "value": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}]}}}, {"type": "finally_clause", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}}}, {"type": "for_in_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement", "named": true}]}, "kind": {"multiple": false, "required": false, "types": [{"type": "const", "named": false}, {"type": "let", "named": false}, {"type": "var", "named": false}]}, "left": {"multiple": false, "required": true, "types": [{"type": "array_pattern", "named": true}, {"type": "identifier", "named": true}, {"type": "member_expression", "named": true}, {"type": "object_pattern", "named": true}, {"type": "parenthesized_expression", "named": true}, {"type": "subscript_expression", "named": true}, {"type": "undefined", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "in", "named": false}, {"type": "of", "named": false}]}, "right": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}, "value": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}]}}}, {"type": "for_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement", "named": true}]}, "condition": {"multiple": true, "required": true, "types": [{"type": ";", "named": false}, {"type": "empty_statement", "named": true}, {"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}, "increment": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}, "initializer": {"multiple": false, "required": true, "types": [{"type": "empty_statement", "named": true}, {"type": "expression", "named": true}, {"type": "lexical_declaration", "named": true}, {"type": "sequence_expression", "named": true}, {"type": "variable_declaration", "named": true}]}}}, {"type": "formal_parameters", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "assignment_pattern", "named": true}, {"type": "pattern", "named": true}]}}, {"type": "function_declaration", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "parameters": {"multiple": false, "required": true, "types": [{"type": "formal_parameters", "named": true}]}}}, {"type": "function_expression", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}, "name": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}, "parameters": {"multiple": false, "required": true, "types": [{"type": "formal_parameters", "named": true}]}}}, {"type": "generator_function", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}, "name": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}, "parameters": {"multiple": false, "required": true, "types": [{"type": "formal_parameters", "named": true}]}}}, {"type": "generator_function_declaration", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "parameters": {"multiple": false, "required": true, "types": [{"type": "formal_parameters", "named": true}]}}}, {"type": "if_statement", "named": true, "fields": {"alternative": {"multiple": false, "required": false, "types": [{"type": "else_clause", "named": true}]}, "condition": {"multiple": false, "required": true, "types": [{"type": "parenthesized_expression", "named": true}]}, "consequence": {"multiple": false, "required": true, "types": [{"type": "statement", "named": true}]}}}, {"type": "import", "named": true, "fields": {}}, {"type": "import_attribute", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "object", "named": true}]}}, {"type": "import_clause", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "named_imports", "named": true}, {"type": "namespace_import", "named": true}]}}, {"type": "import_specifier", "named": true, "fields": {"alias": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "string", "named": true}]}}}, {"type": "import_statement", "named": true, "fields": {"source": {"multiple": false, "required": true, "types": [{"type": "string", "named": true}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "import_attribute", "named": true}, {"type": "import_clause", "named": true}]}}, {"type": "jsx_attribute", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "jsx_element", "named": true}, {"type": "jsx_expression", "named": true}, {"type": "jsx_namespace_name", "named": true}, {"type": "jsx_self_closing_element", "named": true}, {"type": "property_identifier", "named": true}, {"type": "string", "named": true}]}}, {"type": "jsx_closing_element", "named": true, "fields": {"name": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}, {"type": "jsx_namespace_name", "named": true}, {"type": "member_expression", "named": true}]}}}, {"type": "jsx_element", "named": true, "fields": {"close_tag": {"multiple": false, "required": true, "types": [{"type": "jsx_closing_element", "named": true}]}, "open_tag": {"multiple": false, "required": true, "types": [{"type": "jsx_opening_element", "named": true}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "html_character_reference", "named": true}, {"type": "jsx_element", "named": true}, {"type": "jsx_expression", "named": true}, {"type": "jsx_self_closing_element", "named": true}, {"type": "jsx_text", "named": true}]}}, {"type": "jsx_expression", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}, {"type": "spread_element", "named": true}]}}, {"type": "jsx_namespace_name", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "jsx_opening_element", "named": true, "fields": {"attribute": {"multiple": true, "required": false, "types": [{"type": "jsx_attribute", "named": true}, {"type": "jsx_expression", "named": true}]}, "name": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}, {"type": "jsx_namespace_name", "named": true}, {"type": "member_expression", "named": true}]}}}, {"type": "jsx_self_closing_element", "named": true, "fields": {"attribute": {"multiple": true, "required": false, "types": [{"type": "jsx_attribute", "named": true}, {"type": "jsx_expression", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "jsx_namespace_name", "named": true}, {"type": "member_expression", "named": true}]}}}, {"type": "labeled_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement", "named": true}]}, "label": {"multiple": false, "required": true, "types": [{"type": "statement_identifier", "named": true}]}}}, {"type": "lexical_declaration", "named": true, "fields": {"kind": {"multiple": false, "required": true, "types": [{"type": "const", "named": false}, {"type": "let", "named": false}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "variable_declarator", "named": true}]}}, {"type": "member_expression", "named": true, "fields": {"object": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "import", "named": true}]}, "optional_chain": {"multiple": false, "required": false, "types": [{"type": "optional_chain", "named": true}]}, "property": {"multiple": false, "required": true, "types": [{"type": "private_property_identifier", "named": true}, {"type": "property_identifier", "named": true}]}}}, {"type": "meta_property", "named": true, "fields": {}}, {"type": "method_definition", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}, "decorator": {"multiple": true, "required": false, "types": [{"type": "decorator", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "computed_property_name", "named": true}, {"type": "number", "named": true}, {"type": "private_property_identifier", "named": true}, {"type": "property_identifier", "named": true}, {"type": "string", "named": true}]}, "parameters": {"multiple": false, "required": true, "types": [{"type": "formal_parameters", "named": true}]}}}, {"type": "named_imports", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "import_specifier", "named": true}]}}, {"type": "namespace_export", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "string", "named": true}]}}, {"type": "namespace_import", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "new_expression", "named": true, "fields": {"arguments": {"multiple": false, "required": false, "types": [{"type": "arguments", "named": true}]}, "constructor": {"multiple": false, "required": true, "types": [{"type": "new_expression", "named": true}, {"type": "primary_expression", "named": true}]}}}, {"type": "object", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "method_definition", "named": true}, {"type": "pair", "named": true}, {"type": "shorthand_property_identifier", "named": true}, {"type": "spread_element", "named": true}]}}, {"type": "object_assignment_pattern", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "array_pattern", "named": true}, {"type": "object_pattern", "named": true}, {"type": "shorthand_property_identifier_pattern", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "object_pattern", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "object_assignment_pattern", "named": true}, {"type": "pair_pattern", "named": true}, {"type": "rest_pattern", "named": true}, {"type": "shorthand_property_identifier_pattern", "named": true}]}}, {"type": "pair", "named": true, "fields": {"key": {"multiple": false, "required": true, "types": [{"type": "computed_property_name", "named": true}, {"type": "number", "named": true}, {"type": "private_property_identifier", "named": true}, {"type": "property_identifier", "named": true}, {"type": "string", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "pair_pattern", "named": true, "fields": {"key": {"multiple": false, "required": true, "types": [{"type": "computed_property_name", "named": true}, {"type": "number", "named": true}, {"type": "private_property_identifier", "named": true}, {"type": "property_identifier", "named": true}, {"type": "string", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "assignment_pattern", "named": true}, {"type": "pattern", "named": true}]}}}, {"type": "parenthesized_expression", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}}, {"type": "program", "named": true, "root": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "hash_bang_line", "named": true}, {"type": "statement", "named": true}]}}, {"type": "regex", "named": true, "fields": {"flags": {"multiple": false, "required": false, "types": [{"type": "regex_flags", "named": true}]}, "pattern": {"multiple": false, "required": true, "types": [{"type": "regex_pattern", "named": true}]}}}, {"type": "rest_pattern", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "array_pattern", "named": true}, {"type": "identifier", "named": true}, {"type": "member_expression", "named": true}, {"type": "object_pattern", "named": true}, {"type": "subscript_expression", "named": true}, {"type": "undefined", "named": true}]}}, {"type": "return_statement", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}}, {"type": "sequence_expression", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "spread_element", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}, {"type": "statement_block", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "statement", "named": true}]}}, {"type": "string", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "escape_sequence", "named": true}, {"type": "html_character_reference", "named": true}, {"type": "string_fragment", "named": true}]}}, {"type": "subscript_expression", "named": true, "fields": {"index": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}, "object": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "optional_chain": {"multiple": false, "required": false, "types": [{"type": "optional_chain", "named": true}]}}}, {"type": "switch_body", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "switch_case", "named": true}, {"type": "switch_default", "named": true}]}}, {"type": "switch_case", "named": true, "fields": {"body": {"multiple": true, "required": false, "types": [{"type": "statement", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}}}, {"type": "switch_default", "named": true, "fields": {"body": {"multiple": true, "required": false, "types": [{"type": "statement", "named": true}]}}}, {"type": "switch_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "switch_body", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "parenthesized_expression", "named": true}]}}}, {"type": "template_string", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "escape_sequence", "named": true}, {"type": "string_fragment", "named": true}, {"type": "template_substitution", "named": true}]}}, {"type": "template_substitution", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}}, {"type": "ternary_expression", "named": true, "fields": {"alternative": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "condition": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "consequence": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}}}, {"type": "throw_statement", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}, {"type": "sequence_expression", "named": true}]}}, {"type": "try_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement_block", "named": true}]}, "finalizer": {"multiple": false, "required": false, "types": [{"type": "finally_clause", "named": true}]}, "handler": {"multiple": false, "required": false, "types": [{"type": "catch_clause", "named": true}]}}}, {"type": "unary_expression", "named": true, "fields": {"argument": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "!", "named": false}, {"type": "+", "named": false}, {"type": "-", "named": false}, {"type": "delete", "named": false}, {"type": "typeof", "named": false}, {"type": "void", "named": false}, {"type": "~", "named": false}]}}}, {"type": "update_expression", "named": true, "fields": {"argument": {"multiple": false, "required": true, "types": [{"type": "expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "++", "named": false}, {"type": "--", "named": false}]}}}, {"type": "variable_declaration", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "variable_declarator", "named": true}]}}, {"type": "variable_declarator", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "array_pattern", "named": true}, {"type": "identifier", "named": true}, {"type": "object_pattern", "named": true}]}, "value": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}]}}}, {"type": "while_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement", "named": true}]}, "condition": {"multiple": false, "required": true, "types": [{"type": "parenthesized_expression", "named": true}]}}}, {"type": "with_statement", "named": true, "fields": {"body": {"multiple": false, "required": true, "types": [{"type": "statement", "named": true}]}, "object": {"multiple": false, "required": true, "types": [{"type": "parenthesized_expression", "named": true}]}}}, {"type": "yield_expression", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "expression", "named": true}]}}, {"type": "!", "named": false}, {"type": "!=", "named": false}, {"type": "!==", "named": false}, {"type": "\"", "named": false}, {"type": "${", "named": false}, {"type": "%", "named": false}, {"type": "%=", "named": false}, {"type": "&", "named": false}, {"type": "&&", "named": false}, {"type": "&&=", "named": false}, {"type": "&=", "named": false}, {"type": "'", "named": false}, {"type": "(", "named": false}, {"type": ")", "named": false}, {"type": "*", "named": false}, {"type": "**", "named": false}, {"type": "**=", "named": false}, {"type": "*=", "named": false}, {"type": "+", "named": false}, {"type": "++", "named": false}, {"type": "+=", "named": false}, {"type": ",", "named": false}, {"type": "-", "named": false}, {"type": "--", "named": false}, {"type": "-=", "named": false}, {"type": ".", "named": false}, {"type": "...", "named": false}, {"type": "/", "named": false}, {"type": "/=", "named": false}, {"type": "/>", "named": false}, {"type": ":", "named": false}, {"type": ";", "named": false}, {"type": "<", "named": false}, {"type": "</", "named": false}, {"type": "<<", "named": false}, {"type": "<<=", "named": false}, {"type": "<=", "named": false}, {"type": "=", "named": false}, {"type": "==", "named": false}, {"type": "===", "named": false}, {"type": "=>", "named": false}, {"type": ">", "named": false}, {"type": ">=", "named": false}, {"type": ">>", "named": false}, {"type": ">>=", "named": false}, {"type": ">>>", "named": false}, {"type": ">>>=", "named": false}, {"type": "?", "named": false}, {"type": "??", "named": false}, {"type": "??=", "named": false}, {"type": "@", "named": false}, {"type": "[", "named": false}, {"type": "]", "named": false}, {"type": "^", "named": false}, {"type": "^=", "named": false}, {"type": "`", "named": false}, {"type": "as", "named": false}, {"type": "async", "named": false}, {"type": "await", "named": false}, {"type": "break", "named": false}, {"type": "case", "named": false}, {"type": "catch", "named": false}, {"type": "class", "named": false}, {"type": "comment", "named": true}, {"type": "const", "named": false}, {"type": "continue", "named": false}, {"type": "debugger", "named": false}, {"type": "default", "named": false}, {"type": "delete", "named": false}, {"type": "do", "named": false}, {"type": "else", "named": false}, {"type": "escape_sequence", "named": true}, {"type": "export", "named": false}, {"type": "extends", "named": false}, {"type": "false", "named": true}, {"type": "finally", "named": false}, {"type": "for", "named": false}, {"type": "from", "named": false}, {"type": "function", "named": false}, {"type": "get", "named": false}, {"type": "hash_bang_line", "named": true}, {"type": "html_character_reference", "named": true}, {"type": "html_comment", "named": true}, {"type": "identifier", "named": true}, {"type": "if", "named": false}, {"type": "import", "named": false}, {"type": "in", "named": false}, {"type": "instanceof", "named": false}, {"type": "jsx_text", "named": true}, {"type": "let", "named": false}, {"type": "meta", "named": false}, {"type": "new", "named": false}, {"type": "null", "named": true}, {"type": "number", "named": true}, {"type": "of", "named": false}, {"type": "optional_chain", "named": true}, {"type": "private_property_identifier", "named": true}, {"type": "property_identifier", "named": true}, {"type": "regex_flags", "named": true}, {"type": "regex_pattern", "named": true}, {"type": "return", "named": false}, {"type": "set", "named": false}, {"type": "shorthand_property_identifier", "named": true}, {"type": "shorthand_property_identifier_pattern", "named": true}, {"type": "statement_identifier", "named": true}, {"type": "static", "named": false}, {"type": "static get", "named": false}, {"type": "string_fragment", "named": true}, {"type": "super", "named": true}, {"type": "switch", "named": false}, {"type": "target", "named": false}, {"type": "this", "named": true}, {"type": "throw", "named": false}, {"type": "true", "named": true}, {"type": "try", "named": false}, {"type": "typeof", "named": false}, {"type": "undefined", "named": true}, {"type": "var", "named": false}, {"type": "void", "named": false}, {"type": "while", "named": false}, {"type": "with", "named": false}, {"type": "yield", "named": false}, {"type": "{", "named": false}, {"type": "|", "named": false}, {"type": "|=", "named": false}, {"type": "||", "named": false}, {"type": "||=", "named": false}, {"type": "}", "named": false}, {"type": "~", "named": false}]