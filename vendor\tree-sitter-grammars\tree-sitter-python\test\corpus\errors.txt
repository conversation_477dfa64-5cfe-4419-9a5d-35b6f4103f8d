====================================
An error before a string literal
:error
====================================

def a(b):
    c.

    """
    d
    """

    e

---

(module
  (function_definition
    (identifier)
    (parameters
      (identifier))
    (ERROR
      (identifier))
    (block
      (expression_statement
        (string
          (string_start)
          (string_content)
          (string_end)))
      (expression_statement
        (identifier)))))
