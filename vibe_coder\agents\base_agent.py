"""
Base agent class providing common functionality for all Vibe Coder agents.
"""

import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic

from vibe_coder.data_models.api_contracts import ToolCallRequest

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """
    Abstract base class for all Vibe Coder agents.
    Provides common LLM interaction patterns and utilities.
    """
    
    def __init__(self, config: Dict[str, Any], system_prompt: str):
        """
        Initialize the base agent.
        
        Args:
            config: Configuration dictionary containing LLM settings
            system_prompt: The system prompt defining this agent's persona
        """
        self.config = config
        self.system_prompt = system_prompt
        self.llm = self._create_llm_client()
        
    def _create_llm_client(self):
        """Create the appropriate LLM client based on configuration."""
        # Determine which provider to use for this agent
        agent_name = getattr(self, 'agent_name', self.__class__.__name__.lower().replace('agent', '_agent'))

        # Check if there's a specific assignment for this agent
        provider = self.config.get('agent_assignments', {}).get(agent_name)
        if not provider:
            provider = self.config.get('llm_providers', {}).get('default_provider', 'openrouter')

        provider_config = self.config.get('llm_providers', {}).get(provider, {})
        if not provider_config:
            raise ValueError(f"No configuration found for LLM provider: {provider}")

        api_key = provider_config.get('api_key')
        base_url = provider_config.get('base_url')
        model_name = provider_config.get('coder_model_name', 'gpt-4-turbo')

        if not api_key:
            raise ValueError(f"API key not configured for provider: {provider}")

        if provider in ['openai', 'deepseek', 'openrouter']:
            # These providers use OpenAI-compatible API
            return ChatOpenAI(
                api_key=api_key,
                base_url=base_url,
                model=model_name,
                temperature=0.1
            )
        elif provider == 'anthropic':
            return ChatAnthropic(
                api_key=api_key,
                model=model_name,
                temperature=0.1
            )
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")
    
    def _create_structured_prompt(self, user_query: str, output_model=None):
        """
        Create a structured prompt with optional Pydantic output parsing.
        
        Args:
            user_query: The user's input/task
            output_model: Optional Pydantic model for structured output
            
        Returns:
            Tuple of (prompt_template, parser) or just prompt_template if no output_model
        """
        if output_model:
            parser = PydanticOutputParser(pydantic_object=output_model)
            prompt = ChatPromptTemplate.from_messages([
                ("system", self.system_prompt + "\n{format_instructions}"),
                ("human", "{user_query}")
            ])
            return prompt, parser
        else:
            prompt = ChatPromptTemplate.from_messages([
                ("system", self.system_prompt),
                ("human", "{user_query}")
            ])
            return prompt
    
    def _invoke_llm(self, user_query: str, output_model=None, **kwargs):
        """
        Invoke the LLM with structured prompting and optional parsing.
        
        Args:
            user_query: The user's input/task
            output_model: Optional Pydantic model for structured output
            **kwargs: Additional variables for prompt formatting
            
        Returns:
            Parsed object if output_model provided, otherwise raw text
        """
        try:
            if output_model:
                prompt, parser = self._create_structured_prompt(user_query, output_model)
                chain = prompt | self.llm | parser
                
                invoke_params = {
                    "user_query": user_query,
                    "format_instructions": parser.get_format_instructions(),
                    **kwargs
                }
                
                result = chain.invoke(invoke_params)
                logger.debug(f"LLM returned structured result: {type(result)}")
                return result
            else:
                prompt = self._create_structured_prompt(user_query)
                chain = prompt | self.llm
                
                invoke_params = {
                    "user_query": user_query,
                    **kwargs
                }
                
                result = chain.invoke(invoke_params)
                logger.debug(f"LLM returned text result: {len(result.content)} chars")
                return result.content
                
        except Exception as e:
            logger.error(f"LLM invocation failed: {e}")
            raise
    
    @abstractmethod
    def execute_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task assigned to this agent.
        
        Args:
            task_data: Dictionary containing task information
            
        Returns:
            Dictionary containing the result of task execution
        """
        pass
    
    def get_agent_info(self) -> Dict[str, str]:
        """Return information about this agent."""
        return {
            "name": self.__class__.__name__,
            "prompt": self.system_prompt[:100] + "..." if len(self.system_prompt) > 100 else self.system_prompt
        }
