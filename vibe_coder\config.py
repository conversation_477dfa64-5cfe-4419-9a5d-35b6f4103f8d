"""
Configuration management for Vibe Coder.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfigManager:
    """
    Manages configuration loading and access for the Vibe Coder system.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Optional path to config file. If None, searches for default locations.
        """
        self.config_path = config_path or self._find_config_file()
        self.config = self._load_config()
        
    def _find_config_file(self) -> str:
        """Find the configuration file in default locations."""
        possible_paths = [
            "config.yaml",
            "config.yml", 
            "vibe_coder/config.yaml",
            os.path.expanduser("~/.vibe_coder/config.yaml")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found config file at: {path}")
                return path
                
        # If no config file found, create a default one
        logger.warning("No config file found, creating default config.yaml")
        self._create_default_config("config.yaml")
        return "config.yaml"
    
    def _create_default_config(self, path: str):
        """Create a default configuration file."""
        default_config = {
            "llm_providers": {
                "default_provider": "openrouter",
                "openai": {
                    "api_key": "${OPENAI_API_KEY}",
                    "base_url": "https://api.openai.com/v1",
                    "coder_model_name": "gpt-4-turbo",
                    "supervisor_model_name": "gpt-4o"
                },
                "anthropic": {
                    "api_key": "${ANTHROPIC_API_KEY}",
                    "base_url": "https://api.anthropic.com",
                    "coder_model_name": "claude-3-sonnet-20240229",
                    "supervisor_model_name": "claude-3-opus-20240229"
                },
                "deepseek": {
                    "api_key": "${DEEPSEEK_API_KEY}",
                    "base_url": "https://api.deepseek.com/v1",
                    "coder_model_name": "deepseek-reasoner",
                    "supervisor_model_name": "deepseek-reasoner"
                },
                "openrouter": {
                    "api_key": "${OPENROUTER_API_KEY}",
                    "base_url": "https://openrouter.ai/api/v1",
                    "coder_model_name": "google/gemini-2.0-flash-exp:free",
                    "supervisor_model_name": "google/gemini-pro-1.5"
                },
                "tavily": {
                    "api_key": "${TAVILY_API_KEY}"
                }
            },
            "agent_assignments": {
                "coder_agent": "deepseek",
                "task_decomposer": "openrouter",
                "meta_agent": "openrouter",
                "web_search": "tavily"
            },
            "paths": {
                "vector_store_path": "./data/vector_store",
                "action_ledger_db": "./data/action_ledger.db",
                "lessons_learned_db": "./data/lessons_learned.json",
                "log_file": "./logs/vibe_coder.log",
                "tree_sitter_library": "./build/my-languages.dll",
                "tree_sitter_grammars": "./vendor/tree-sitter-grammars"
            },
            "settings": {
                "log_level": "INFO",
                "max_task_retries": 3,
                "llm_timeout": 60,
                "enable_reflection": True,
                "enable_validation": True
            }
        }
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path) if os.path.dirname(path) else ".", exist_ok=True)
        
        with open(path, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False, indent=2)
            
        logger.info(f"Created default config file at: {path}")
        logger.warning("Please update the API keys in the config file before running the system")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
                
            # Validate required sections
            self._validate_config(config)
            
            # Expand environment variables
            config = self._expand_env_vars(config)
            
            logger.info(f"Configuration loaded from: {self.config_path}")
            return config
            
        except Exception as e:
            logger.error(f"Failed to load config from {self.config_path}: {e}")
            raise
    
    def _validate_config(self, config: Dict[str, Any]):
        """Validate that required configuration sections exist."""
        required_sections = ["llm_providers", "paths", "settings"]
        
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required config section: {section}")
        
        # Check for API keys
        provider = config["llm_providers"].get("default_provider", "openai")
        if provider in config["llm_providers"]:
            api_key = config["llm_providers"][provider].get("api_key", "")
            if not api_key or "REPLACE_WITH_YOUR" in api_key:
                logger.warning(f"API key for {provider} not configured properly")
    
    def _expand_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Expand environment variables in configuration values."""
        def expand_value(value):
            if isinstance(value, str):
                return os.path.expandvars(value)
            elif isinstance(value, dict):
                return {k: expand_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [expand_value(item) for item in value]
            else:
                return value
        
        return expand_value(config)
    
    def get(self, key_path: str, default=None):
        """
        Get a configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the config value (e.g., "llm_providers.openai.api_key")
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        Set a configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the config value
            value: Value to set
        """
        keys = key_path.split('.')
        config = self.config
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the final value
        config[keys[-1]] = value
    
    def save(self):
        """Save the current configuration back to file."""
        try:
            with open(self.config_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            logger.info(f"Configuration saved to: {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
            raise
    
    def get_llm_config(self, provider: Optional[str] = None) -> Dict[str, Any]:
        """
        Get LLM configuration for a specific provider.

        Args:
            provider: LLM provider name. If None, uses default provider.

        Returns:
            Dictionary containing LLM configuration
        """
        if provider is None:
            provider = self.get("llm_providers.default_provider", "openrouter")

        provider_config = self.get(f"llm_providers.{provider}", {})
        if not provider_config:
            raise ValueError(f"No configuration found for LLM provider: {provider}")

        return provider_config

    def get_agent_llm_config(self, agent_name: str) -> Dict[str, Any]:
        """
        Get LLM configuration for a specific agent.

        Args:
            agent_name: Name of the agent (e.g., "coder_agent", "task_decomposer")

        Returns:
            Dictionary containing LLM configuration for the agent
        """
        # Get the assigned provider for this agent
        assigned_provider = self.get(f"agent_assignments.{agent_name}")

        if not assigned_provider:
            # Fall back to default provider
            assigned_provider = self.get("llm_providers.default_provider", "openrouter")
            logger.warning(f"No specific provider assigned to {agent_name}, using default: {assigned_provider}")

        return self.get_llm_config(assigned_provider)
    
    def ensure_directories(self):
        """Ensure all configured directories exist."""
        paths_to_create = [
            self.get("paths.vector_store_path"),
            os.path.dirname(self.get("paths.action_ledger_db")),
            os.path.dirname(self.get("paths.lessons_learned_db")),
            os.path.dirname(self.get("paths.log_file")),
            os.path.dirname(self.get("paths.tree_sitter_library")),
            self.get("paths.tree_sitter_grammars")
        ]
        
        for path in paths_to_create:
            if path and not os.path.exists(path):
                os.makedirs(path, exist_ok=True)
                logger.debug(f"Created directory: {path}")


# Global configuration instance
_config_manager = None


def get_config() -> ConfigManager:
    """Get the global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def init_config(config_path: Optional[str] = None) -> ConfigManager:
    """Initialize the global configuration manager."""
    global _config_manager
    _config_manager = ConfigManager(config_path)
    return _config_manager
