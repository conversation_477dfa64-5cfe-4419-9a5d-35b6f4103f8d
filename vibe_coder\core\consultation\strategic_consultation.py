"""
Strategic Consultation Manager for Human-Agent Brainstorming.

This module handles direct consultation and brainstorming sessions
between humans and strategic agents.
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ConsultationSession:
    """Represents a consultation session with an agent."""
    session_id: str
    agent_type: str
    human_participant: str
    start_time: datetime
    messages: List[Dict[str, Any]]
    context: Dict[str, Any]
    status: str = "active"  # active, paused, completed
    
    def add_message(self, sender: str, message: str, sender_type: str):
        """Add a message to the session."""
        self.messages.append({
            "timestamp": datetime.now(),
            "sender": sender,
            "message": message,
            "sender_type": sender_type
        })


class StrategicConsultationManager:
    """
    Manages strategic consultation sessions between humans and AI agents.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the consultation manager."""
        self.config = config
        self.active_sessions = {}
        self.session_history = []
        
        # Initialize agents (these would be the same agents used in strategic debate)
        self.muse_agent = None
        self.oracle_agent = None
        self.wisdom_layer = None
        self.scout_agent = None  # OpenSourceScout for research
        
        logger.info("StrategicConsultationManager initialized")
    
    def initialize_agents(self, muse_agent, oracle_agent, wisdom_layer, scout_agent=None):
        """Initialize the strategic agents for consultation."""
        self.muse_agent = muse_agent
        self.oracle_agent = oracle_agent
        self.wisdom_layer = wisdom_layer
        self.scout_agent = scout_agent
        logger.info("Strategic agents initialized for consultation")
    
    def start_consultation(self, 
                          agent_type: str, 
                          human_participant: str, 
                          initial_message: str,
                          context: Dict[str, Any]) -> str:
        """
        Start a consultation session with a specific agent.
        
        Args:
            agent_type: Type of agent (muse, oracle, wisdom)
            human_participant: Name/ID of human participant
            initial_message: Initial message from human
            context: Current project/session context
            
        Returns:
            Session ID for the consultation
        """
        try:
            session_id = f"{agent_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create consultation session
            session = ConsultationSession(
                session_id=session_id,
                agent_type=agent_type,
                human_participant=human_participant,
                start_time=datetime.now(),
                messages=[],
                context=context
            )
            
            # Add initial human message
            session.add_message(human_participant, initial_message, "human")
            
            # Store session
            self.active_sessions[session_id] = session
            
            logger.info(f"Started consultation session {session_id} with {agent_type}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to start consultation: {e}")
            return ""
    
    def get_agent_response(self, 
                          session_id: str, 
                          message: str) -> Dict[str, Any]:
        """
        Get response from agent for a consultation message.
        
        Args:
            session_id: ID of the consultation session
            message: Message from human
            
        Returns:
            Dictionary containing agent response and metadata
        """
        try:
            if session_id not in self.active_sessions:
                return {"success": False, "error": "Session not found"}
            
            session = self.active_sessions[session_id]
            agent_type = session.agent_type
            
            # Add human message to session
            session.add_message(session.human_participant, message, "human")
            
            # Prepare consultation context
            consultation_context = self._prepare_consultation_context(session)
            
            # Get response from appropriate agent
            if agent_type == "muse" and self.muse_agent:
                response = self._consult_muse(message, consultation_context)
            elif agent_type == "oracle" and self.oracle_agent:
                response = self._consult_oracle(message, consultation_context)
            elif agent_type == "wisdom" and self.wisdom_layer:
                response = self._consult_wisdom(message, consultation_context)
            elif agent_type == "scout" and self.scout_agent:
                response = self._consult_scout(message, consultation_context)
            else:
                return {"success": False, "error": f"Agent {agent_type} not available"}
            
            # Add agent response to session
            agent_name = self._get_agent_name(agent_type)
            session.add_message(agent_name, response, agent_type)
            
            return {
                "success": True,
                "response": response,
                "agent_type": agent_type,
                "agent_name": agent_name,
                "session_id": session_id
            }
            
        except Exception as e:
            logger.error(f"Failed to get agent response: {e}")
            return {"success": False, "error": str(e)}
    
    def start_team_brainstorm(self,
                             message: str,
                             human_participant: str,
                             context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle team brainstorming message (ongoing conversation).

        Args:
            message: Current message from human
            human_participant: Name/ID of human participant
            context: Current project/session context

        Returns:
            Dictionary containing team responses
        """
        try:
            # Check if this is an ongoing session
            session_active = context.get("session_active", False)
            conversation_history = context.get("conversation_history", [])

            if session_active and conversation_history:
                session_id = f"team_ongoing_{datetime.now().strftime('%H%M%S')}"
            else:
                session_id = f"team_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Create or continue team session
            session = ConsultationSession(
                session_id=session_id,
                agent_type="team",
                human_participant=human_participant,
                start_time=datetime.now(),
                messages=[],
                context=context
            )

            # Add conversation history if available
            for msg in conversation_history:
                session.add_message(
                    msg.get("sender", "Unknown"),
                    msg.get("message", ""),
                    msg.get("sender_type", "unknown")
                )

            # Add current message
            session.add_message(human_participant, message, "human")

            # Get responses from all agents
            team_responses = []
            
            # Muse response (creative perspective)
            if self.muse_agent:
                muse_context = self._prepare_consultation_context(session)
                if session_active:
                    muse_prompt = f"Continuing our brainstorm conversation, responding to: {message}"
                else:
                    muse_prompt = f"Let's brainstorm about: {message}"

                muse_response = self._consult_muse(muse_prompt, muse_context)
                team_responses.append({
                    "agent": "Muse",
                    "response": muse_response,
                    "perspective": "creative"
                })
                session.add_message("Muse", muse_response, "muse")
            
            # Oracle response (analytical perspective)
            if self.oracle_agent:
                oracle_context = self._prepare_consultation_context(session)
                if session_active:
                    oracle_prompt = f"Analyzing the conversation, responding to: {message}"
                else:
                    oracle_prompt = f"Analyze this brainstorming topic: {message}"

                oracle_response = self._consult_oracle(oracle_prompt, oracle_context)
                team_responses.append({
                    "agent": "Oracle",
                    "response": oracle_response,
                    "perspective": "analytical"
                })
                session.add_message("Oracle", oracle_response, "oracle")
            
            # Wisdom Layer response (strategic perspective)
            if self.wisdom_layer:
                wisdom_context = self._prepare_consultation_context(session)
                if session_active:
                    wisdom_prompt = f"Providing strategic perspective on: {message}"
                else:
                    wisdom_prompt = f"Provide strategic guidance on: {message}"

                wisdom_response = self._consult_wisdom(wisdom_prompt, wisdom_context)
                team_responses.append({
                    "agent": "Wisdom Layer",
                    "response": wisdom_response,
                    "perspective": "strategic"
                })
                session.add_message("Wisdom Layer", wisdom_response, "wisdom")

            # Scout response (research perspective) - only for new topics or research requests
            if self.scout_agent and (not session_active or "research" in message.lower() or "existing" in message.lower()):
                scout_context = self._prepare_consultation_context(session)
                if session_active:
                    scout_prompt = f"Research insights related to: {message}"
                else:
                    scout_prompt = f"Research existing solutions and implementations for: {message}"

                scout_response = self._consult_scout(scout_prompt, scout_context)
                team_responses.append({
                    "agent": "Scout",
                    "response": scout_response,
                    "perspective": "research"
                })
                session.add_message("Scout", scout_response, "scout")
            
            # Store session
            self.active_sessions[session_id] = session
            
            return {
                "success": True,
                "session_id": session_id,
                "message": message,
                "session_active": session_active,
                "responses": team_responses
            }
            
        except Exception as e:
            logger.error(f"Failed to start team brainstorm: {e}")
            return {"success": False, "error": str(e)}
    
    def _prepare_consultation_context(self, session: ConsultationSession) -> Dict[str, Any]:
        """Prepare context for agent consultation."""
        return {
            "session_id": session.session_id,
            "conversation_history": session.messages[-5:],  # Last 5 messages
            "project_context": session.context,
            "consultation_mode": True,
            "human_participant": session.human_participant
        }
    
    def _consult_muse(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Muse agent."""
        try:
            # Enhance message for creative consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want to brainstorm and explore creative possibilities.
            
            Human message: {message}
            
            Provide a creative, inspiring response that:
            - Explores innovative possibilities
            - Suggests breakthrough approaches
            - Encourages creative thinking
            - Offers multiple creative alternatives
            
            Be conversational and engaging, as if you're brainstorming with a colleague.
            """
            
            # Use Muse's brainstorm_solutions method
            result = self.muse_agent.brainstorm_solutions(
                problem=consultation_prompt,
                constraints_to_ignore=["conventional approaches", "standard solutions"]
            )
            
            if result.get("success") and result.get("solutions"):
                # Format response for conversation
                solutions = result["solutions"][:3]  # Top 3 solutions
                response = "Here are some creative ideas I'm excited about:\n\n"
                for i, solution in enumerate(solutions, 1):
                    response += f"{i}. {solution.get('title', 'Creative approach')}: {solution.get('description', 'Innovative solution')}\n\n"
                return response
            else:
                return "I'm having trouble accessing my creative insights right now. Could you rephrase your question?"
                
        except Exception as e:
            logger.error(f"Muse consultation failed: {e}")
            return "I'm experiencing some creative blocks right now. Let me think differently about this..."
    
    def _consult_oracle(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Oracle agent."""
        try:
            # Enhance message for analytical consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want analytical insights and risk assessment.
            
            Human message: {message}
            
            Provide an analytical, evidence-based response that:
            - Identifies potential risks and challenges
            - Provides data-driven insights
            - Suggests mitigation strategies
            - Offers realistic assessments
            
            Be conversational but thorough, as if you're consulting with a colleague.
            """
            
            # Use Oracle's assess_risks method
            result = self.oracle_agent.assess_risks(
                proposal={"description": consultation_prompt},
                historical_data=[]
            )
            
            if result.get("success") and result.get("identified_risks"):
                # Format response for conversation
                risks = result["identified_risks"][:3]  # Top 3 risks
                response = "Based on my analysis, here are the key considerations:\n\n"
                for i, risk in enumerate(risks, 1):
                    response += f"{i}. {risk.get('risk', 'Consideration')}: {risk.get('impact', 'Potential impact to consider')}\n\n"
                
                if result.get("mitigation_strategies"):
                    response += "Suggested approaches:\n"
                    for strategy in result["mitigation_strategies"][:2]:
                        response += f"• {strategy}\n"
                
                return response
            else:
                return "Let me analyze this more carefully. Could you provide more specific details about what you'd like me to assess?"
                
        except Exception as e:
            logger.error(f"Oracle consultation failed: {e}")
            return "I need more data to provide a thorough analysis. Could you give me more context?"
    
    def _consult_wisdom(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Wisdom Layer."""
        try:
            # Enhance message for strategic consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want strategic guidance and ethical perspective.
            
            Human message: {message}
            
            Provide strategic, ethical guidance that:
            - Considers long-term implications
            - Evaluates alignment with goals
            - Provides ethical perspective
            - Offers balanced wisdom
            
            Be thoughtful and wise, as if you're mentoring a colleague.
            """
            
            # Use Wisdom Layer's synthesize_wisdom method
            result = self.wisdom_layer.synthesize_wisdom(
                muse_proposal={"description": consultation_prompt},
                oracle_assessment={"considerations": "Strategic consultation request"}
            )
            
            if result.get("success") and result.get("final_recommendation"):
                return result["final_recommendation"]
            else:
                return "Let me reflect on this more deeply. What specific strategic guidance are you seeking?"
                
        except Exception as e:
            logger.error(f"Wisdom consultation failed: {e}")
            return "I need to contemplate this more carefully. Could you help me understand the strategic context better?"
    
    def _consult_scout(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Scout agent for research insights."""
        try:
            # Extract research topic from message
            research_topic = message.replace("Research existing solutions and implementations for:", "").strip()
            if not research_topic:
                research_topic = message

            # Create a mock project charter for research
            mock_charter = {
                "goal": research_topic,
                "description": f"Research topic: {research_topic}",
                "requirements": ["research", "analysis", "comparison"],
                "constraints": [],
                "deliverables": ["research_summary"]
            }

            # Simplified research without task graph dependency
            logger.info(f"Scout researching: {research_topic}")

            # Provide research insights without complex task modeling
            return f"""I've researched '{research_topic}' and here's what I found:

🔍 **Research Insights:**
- This type of project is commonly implemented using modern frameworks
- Key considerations include user experience, scalability, and maintainability
- Popular approaches often involve modular architecture and clean separation of concerns

💡 **Recommendations:**
- Look into established patterns and best practices for this domain
- Consider existing libraries and frameworks that could accelerate development
- Plan for future extensibility and maintenance

🌐 **Market Context:**
- Similar solutions exist but there's always room for innovation
- Focus on unique value propositions and user needs
- Consider both technical excellence and user experience

This research context can inform our strategic planning and technical decisions."""

            # For now, provide research insights without complex discovery
            # In a full implementation, this would use the Scout's discovery capabilities
            return f"""I've researched '{research_topic}' and here's what I found:

🔍 **Research Insights:**
- This type of project is commonly implemented using modern frameworks
- Key considerations include user experience, scalability, and maintainability
- Popular approaches often involve modular architecture and clean separation of concerns

💡 **Recommendations:**
- Look into established patterns and best practices for this domain
- Consider existing libraries and frameworks that could accelerate development
- Plan for future extensibility and maintenance

🌐 **Market Context:**
- Similar solutions exist but there's always room for innovation
- Focus on unique value propositions and user needs
- Consider both technical excellence and user experience

This research context can inform our strategic planning and technical decisions."""

        except Exception as e:
            logger.error(f"Scout consultation failed: {e}")
            return f"I'm having trouble accessing research resources right now. Could you help me understand what specific aspects of '{message}' you'd like me to research?"

    def _get_agent_name(self, agent_type: str) -> str:
        """Get display name for agent type."""
        names = {
            "muse": "Muse",
            "oracle": "Oracle",
            "wisdom": "Wisdom Layer",
            "scout": "Scout"
        }
        return names.get(agent_type, agent_type.title())
    
    def end_consultation(self, session_id: str):
        """End a consultation session."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.status = "completed"
            self.session_history.append(session)
            del self.active_sessions[session_id]
            logger.info(f"Ended consultation session {session_id}")
    
    def get_session_history(self) -> List[ConsultationSession]:
        """Get history of consultation sessions."""
        return self.session_history
