"""
Strategic Consultation Manager for Human-Agent Brainstorming.

This module handles direct consultation and brainstorming sessions
between humans and strategic agents.
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class ConsultationSession:
    """Represents a consultation session with an agent."""
    session_id: str
    agent_type: str
    human_participant: str
    start_time: datetime
    messages: List[Dict[str, Any]]
    context: Dict[str, Any]
    status: str = "active"  # active, paused, completed
    
    def add_message(self, sender: str, message: str, sender_type: str):
        """Add a message to the session."""
        self.messages.append({
            "timestamp": datetime.now(),
            "sender": sender,
            "message": message,
            "sender_type": sender_type
        })


class StrategicConsultationManager:
    """
    Manages strategic consultation sessions between humans and AI agents.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the consultation manager."""
        self.config = config
        self.active_sessions = {}
        self.session_history = []
        
        # Initialize agents (these would be the same agents used in strategic debate)
        self.muse_agent = None
        self.oracle_agent = None
        self.wisdom_layer = None
        
        logger.info("StrategicConsultationManager initialized")
    
    def initialize_agents(self, muse_agent, oracle_agent, wisdom_layer):
        """Initialize the strategic agents for consultation."""
        self.muse_agent = muse_agent
        self.oracle_agent = oracle_agent
        self.wisdom_layer = wisdom_layer
        logger.info("Strategic agents initialized for consultation")
    
    def start_consultation(self, 
                          agent_type: str, 
                          human_participant: str, 
                          initial_message: str,
                          context: Dict[str, Any]) -> str:
        """
        Start a consultation session with a specific agent.
        
        Args:
            agent_type: Type of agent (muse, oracle, wisdom)
            human_participant: Name/ID of human participant
            initial_message: Initial message from human
            context: Current project/session context
            
        Returns:
            Session ID for the consultation
        """
        try:
            session_id = f"{agent_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create consultation session
            session = ConsultationSession(
                session_id=session_id,
                agent_type=agent_type,
                human_participant=human_participant,
                start_time=datetime.now(),
                messages=[],
                context=context
            )
            
            # Add initial human message
            session.add_message(human_participant, initial_message, "human")
            
            # Store session
            self.active_sessions[session_id] = session
            
            logger.info(f"Started consultation session {session_id} with {agent_type}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to start consultation: {e}")
            return ""
    
    def get_agent_response(self, 
                          session_id: str, 
                          message: str) -> Dict[str, Any]:
        """
        Get response from agent for a consultation message.
        
        Args:
            session_id: ID of the consultation session
            message: Message from human
            
        Returns:
            Dictionary containing agent response and metadata
        """
        try:
            if session_id not in self.active_sessions:
                return {"success": False, "error": "Session not found"}
            
            session = self.active_sessions[session_id]
            agent_type = session.agent_type
            
            # Add human message to session
            session.add_message(session.human_participant, message, "human")
            
            # Prepare consultation context
            consultation_context = self._prepare_consultation_context(session)
            
            # Get response from appropriate agent
            if agent_type == "muse" and self.muse_agent:
                response = self._consult_muse(message, consultation_context)
            elif agent_type == "oracle" and self.oracle_agent:
                response = self._consult_oracle(message, consultation_context)
            elif agent_type == "wisdom" and self.wisdom_layer:
                response = self._consult_wisdom(message, consultation_context)
            else:
                return {"success": False, "error": f"Agent {agent_type} not available"}
            
            # Add agent response to session
            agent_name = self._get_agent_name(agent_type)
            session.add_message(agent_name, response, agent_type)
            
            return {
                "success": True,
                "response": response,
                "agent_type": agent_type,
                "agent_name": agent_name,
                "session_id": session_id
            }
            
        except Exception as e:
            logger.error(f"Failed to get agent response: {e}")
            return {"success": False, "error": str(e)}
    
    def start_team_brainstorm(self, 
                             topic: str, 
                             human_participant: str,
                             context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Start a team brainstorming session with all agents.
        
        Args:
            topic: Brainstorming topic
            human_participant: Name/ID of human participant
            context: Current project/session context
            
        Returns:
            Dictionary containing team responses
        """
        try:
            session_id = f"team_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Create team session
            session = ConsultationSession(
                session_id=session_id,
                agent_type="team",
                human_participant=human_participant,
                start_time=datetime.now(),
                messages=[],
                context=context
            )
            
            # Add initial topic
            session.add_message(human_participant, f"Team brainstorm topic: {topic}", "human")
            
            # Get responses from all agents
            team_responses = []
            
            # Muse response (creative perspective)
            if self.muse_agent:
                muse_response = self._consult_muse(
                    f"Let's brainstorm about: {topic}", 
                    self._prepare_consultation_context(session)
                )
                team_responses.append({
                    "agent": "Muse",
                    "response": muse_response,
                    "perspective": "creative"
                })
                session.add_message("Muse", muse_response, "muse")
            
            # Oracle response (analytical perspective)
            if self.oracle_agent:
                oracle_response = self._consult_oracle(
                    f"Analyze this brainstorming topic: {topic}", 
                    self._prepare_consultation_context(session)
                )
                team_responses.append({
                    "agent": "Oracle",
                    "response": oracle_response,
                    "perspective": "analytical"
                })
                session.add_message("Oracle", oracle_response, "oracle")
            
            # Wisdom Layer response (strategic perspective)
            if self.wisdom_layer:
                wisdom_response = self._consult_wisdom(
                    f"Provide strategic guidance on: {topic}", 
                    self._prepare_consultation_context(session)
                )
                team_responses.append({
                    "agent": "Wisdom Layer",
                    "response": wisdom_response,
                    "perspective": "strategic"
                })
                session.add_message("Wisdom Layer", wisdom_response, "wisdom")
            
            # Store session
            self.active_sessions[session_id] = session
            
            return {
                "success": True,
                "session_id": session_id,
                "topic": topic,
                "responses": team_responses
            }
            
        except Exception as e:
            logger.error(f"Failed to start team brainstorm: {e}")
            return {"success": False, "error": str(e)}
    
    def _prepare_consultation_context(self, session: ConsultationSession) -> Dict[str, Any]:
        """Prepare context for agent consultation."""
        return {
            "session_id": session.session_id,
            "conversation_history": session.messages[-5:],  # Last 5 messages
            "project_context": session.context,
            "consultation_mode": True,
            "human_participant": session.human_participant
        }
    
    def _consult_muse(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Muse agent."""
        try:
            # Enhance message for creative consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want to brainstorm and explore creative possibilities.
            
            Human message: {message}
            
            Provide a creative, inspiring response that:
            - Explores innovative possibilities
            - Suggests breakthrough approaches
            - Encourages creative thinking
            - Offers multiple creative alternatives
            
            Be conversational and engaging, as if you're brainstorming with a colleague.
            """
            
            # Use Muse's brainstorm_solutions method
            result = self.muse_agent.brainstorm_solutions(
                problem=consultation_prompt,
                constraints_to_ignore=["conventional approaches", "standard solutions"]
            )
            
            if result.get("success") and result.get("solutions"):
                # Format response for conversation
                solutions = result["solutions"][:3]  # Top 3 solutions
                response = "Here are some creative ideas I'm excited about:\n\n"
                for i, solution in enumerate(solutions, 1):
                    response += f"{i}. {solution.get('title', 'Creative approach')}: {solution.get('description', 'Innovative solution')}\n\n"
                return response
            else:
                return "I'm having trouble accessing my creative insights right now. Could you rephrase your question?"
                
        except Exception as e:
            logger.error(f"Muse consultation failed: {e}")
            return "I'm experiencing some creative blocks right now. Let me think differently about this..."
    
    def _consult_oracle(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Oracle agent."""
        try:
            # Enhance message for analytical consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want analytical insights and risk assessment.
            
            Human message: {message}
            
            Provide an analytical, evidence-based response that:
            - Identifies potential risks and challenges
            - Provides data-driven insights
            - Suggests mitigation strategies
            - Offers realistic assessments
            
            Be conversational but thorough, as if you're consulting with a colleague.
            """
            
            # Use Oracle's assess_risks method
            result = self.oracle_agent.assess_risks(
                proposal={"description": consultation_prompt},
                historical_data=[]
            )
            
            if result.get("success") and result.get("identified_risks"):
                # Format response for conversation
                risks = result["identified_risks"][:3]  # Top 3 risks
                response = "Based on my analysis, here are the key considerations:\n\n"
                for i, risk in enumerate(risks, 1):
                    response += f"{i}. {risk.get('risk', 'Consideration')}: {risk.get('impact', 'Potential impact to consider')}\n\n"
                
                if result.get("mitigation_strategies"):
                    response += "Suggested approaches:\n"
                    for strategy in result["mitigation_strategies"][:2]:
                        response += f"• {strategy}\n"
                
                return response
            else:
                return "Let me analyze this more carefully. Could you provide more specific details about what you'd like me to assess?"
                
        except Exception as e:
            logger.error(f"Oracle consultation failed: {e}")
            return "I need more data to provide a thorough analysis. Could you give me more context?"
    
    def _consult_wisdom(self, message: str, context: Dict[str, Any]) -> str:
        """Consult with Wisdom Layer."""
        try:
            # Enhance message for strategic consultation
            consultation_prompt = f"""
            You are in consultation mode with a human architect. They want strategic guidance and ethical perspective.
            
            Human message: {message}
            
            Provide strategic, ethical guidance that:
            - Considers long-term implications
            - Evaluates alignment with goals
            - Provides ethical perspective
            - Offers balanced wisdom
            
            Be thoughtful and wise, as if you're mentoring a colleague.
            """
            
            # Use Wisdom Layer's synthesize_wisdom method
            result = self.wisdom_layer.synthesize_wisdom(
                muse_proposal={"description": consultation_prompt},
                oracle_assessment={"considerations": "Strategic consultation request"}
            )
            
            if result.get("success") and result.get("final_recommendation"):
                return result["final_recommendation"]
            else:
                return "Let me reflect on this more deeply. What specific strategic guidance are you seeking?"
                
        except Exception as e:
            logger.error(f"Wisdom consultation failed: {e}")
            return "I need to contemplate this more carefully. Could you help me understand the strategic context better?"
    
    def _get_agent_name(self, agent_type: str) -> str:
        """Get display name for agent type."""
        names = {
            "muse": "Muse",
            "oracle": "Oracle", 
            "wisdom": "Wisdom Layer"
        }
        return names.get(agent_type, agent_type.title())
    
    def end_consultation(self, session_id: str):
        """End a consultation session."""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.status = "completed"
            self.session_history.append(session)
            del self.active_sessions[session_id]
            logger.info(f"Ended consultation session {session_id}")
    
    def get_session_history(self) -> List[ConsultationSession]:
        """Get history of consultation sessions."""
        return self.session_history
