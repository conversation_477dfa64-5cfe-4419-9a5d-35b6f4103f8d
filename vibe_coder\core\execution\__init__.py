"""
Execution Team Module: Houses the execution agents for code generation and context retrieval.

This module contains agents moved from the agents/ directory:
- CoderAgent: Code generation and implementation (moved from agents/)
- LibrarianAgent: Context retrieval and code understanding (moved from agents/)
"""

# Note: These will be moved from agents/ directory in the next phase
# from .coder_agent import CoderAgent
# from .librarian_agent import LibrarianAgent

__all__ = [
    # 'CoderAgent',
    # 'LibrarianAgent'
]
