"""
Open-Source Scout Agent for discovering existing solutions.

This agent searches for high-quality open-source projects that could
accelerate development by providing a foundation to build upon.
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from urllib.parse import urlparse

from vibe_coder.agents.base_agent import BaseAgent
from vibe_coder.data_models.task_models import TaskGraphDefinition
from vibe_coder.prompts.discovery_prompts import SCOUT_TECHNICAL_PROMPT

logger = logging.getLogger(__name__)


@dataclass
class OpenSourceCandidate:
    """Represents a potential open-source project candidate."""
    name: str
    url: str
    description: str
    stars: Optional[int] = None
    last_updated: Optional[str] = None
    license: Optional[str] = None
    language: Optional[str] = None
    coverage_percentage: float = 0.0
    covered_tasks: List[str] = None
    analysis: str = ""
    recommendation_score: float = 0.0
    
    def __post_init__(self):
        if self.covered_tasks is None:
            self.covered_tasks = []


class OpenSourceScout(BaseAgent):
    """
    Agent responsible for discovering relevant open-source projects
    that could accelerate development.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the Open-Source Scout agent."""
        super().__init__(config, SCOUT_TECHNICAL_PROMPT)
        self.agent_name = "open_source_scout"
        
        # Search strategy configuration
        self.max_search_results = 10
        self.min_stars_threshold = 50
        self.min_coverage_threshold = 0.4  # 40% task coverage minimum
        self.preferred_licenses = ["MIT", "Apache-2.0", "BSD-3-Clause", "GPL-3.0"]
        
        logger.info("OpenSourceScout initialized for project discovery")

    def execute_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task assigned to this agent.

        Args:
            task_data: Dictionary containing task information

        Returns:
            Dictionary containing the result of task execution
        """
        try:
            task_type = task_data.get("type", "discover")

            if task_type == "discover":
                # Main discovery task
                project_charter = task_data.get("project_charter", {})
                task_graph = task_data.get("task_graph")

                if not task_graph:
                    return {
                        "success": False,
                        "error": "Task graph required for discovery",
                        "agent": "open_source_scout"
                    }

                candidates = self.discover_candidates(project_charter, task_graph)

                return {
                    "success": True,
                    "candidates": [
                        {
                            "name": c.name,
                            "url": c.url,
                            "description": c.description,
                            "coverage_percentage": c.coverage_percentage,
                            "recommendation_score": c.recommendation_score,
                            "analysis": c.analysis
                        }
                        for c in candidates
                    ],
                    "total_found": len(candidates),
                    "agent": "open_source_scout"
                }

            elif task_type == "research":
                # Research task for brainstorming
                query = task_data.get("query", "")
                context = task_data.get("context", {})

                # Use simplified research for brainstorming
                research_results = self._execute_web_search(query)

                return {
                    "success": True,
                    "research_results": research_results[:3],  # Top 3 results
                    "query": query,
                    "agent": "open_source_scout"
                }

            else:
                return {
                    "success": False,
                    "error": f"Unknown task type: {task_type}",
                    "agent": "open_source_scout"
                }

        except Exception as e:
            logger.error(f"Scout task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent": "open_source_scout"
            }

    def discover_candidates(self,
                          project_charter: Dict[str, Any], 
                          task_graph: TaskGraphDefinition) -> List[OpenSourceCandidate]:
        """
        Discover open-source candidates for the given project.
        
        Args:
            project_charter: Project charter with goals and requirements
            task_graph: Generated task graph definition
            
        Returns:
            List of potential open-source candidates
        """
        try:
            logger.info("Starting open-source discovery process")
            
            # Generate search queries
            search_queries = self._generate_search_queries(project_charter, task_graph)
            
            # Execute searches and collect results
            all_candidates = []
            for query in search_queries:
                candidates = self._search_and_analyze(query, project_charter, task_graph)
                all_candidates.extend(candidates)
            
            # Deduplicate and rank candidates
            unique_candidates = self._deduplicate_candidates(all_candidates)
            ranked_candidates = self._rank_candidates(unique_candidates, task_graph)
            
            # Filter by quality thresholds
            qualified_candidates = [
                candidate for candidate in ranked_candidates
                if candidate.coverage_percentage >= self.min_coverage_threshold
                and candidate.recommendation_score >= 0.6
            ]
            
            logger.info(f"Discovery complete: {len(qualified_candidates)} qualified candidates found")
            return qualified_candidates[:5]  # Return top 5
            
        except Exception as e:
            logger.error(f"Open-source discovery failed: {e}")
            return []
    
    def _generate_search_queries(self, 
                               project_charter: Dict[str, Any], 
                               task_graph: TaskGraphDefinition) -> List[str]:
        """Generate effective search queries based on project requirements."""
        goal = project_charter.get("goal", "")
        description = project_charter.get("description", "")
        requirements = project_charter.get("requirements", [])
        
        # Extract key technologies and domains
        tech_keywords = self._extract_tech_keywords(goal, description, requirements)
        domain_keywords = self._extract_domain_keywords(goal, description)
        
        # Generate diverse search queries
        queries = []
        
        # GitHub-specific searches
        for tech in tech_keywords[:3]:
            queries.append(f"GitHub {tech} {domain_keywords[0]} open source")
            queries.append(f"site:github.com {tech} {domain_keywords[0]} stars:>100")
        
        # PyPI/package searches (if Python project)
        if "python" in tech_keywords:
            queries.append(f"PyPI {domain_keywords[0]} python package")
            
        # General open source searches
        queries.append(f"open source {domain_keywords[0]} {tech_keywords[0]}")
        queries.append(f"free {domain_keywords[0]} software {tech_keywords[0]}")
        
        return queries[:6]  # Limit to 6 queries
    
    def _extract_tech_keywords(self, goal: str, description: str, requirements: List[str]) -> List[str]:
        """Extract technology keywords from project description."""
        text = f"{goal} {description} {' '.join(requirements)}".lower()
        
        tech_keywords = []
        
        # Programming languages
        languages = ["python", "javascript", "java", "c++", "c#", "go", "rust", "typescript"]
        for lang in languages:
            if lang in text:
                tech_keywords.append(lang)
        
        # Frameworks and libraries
        frameworks = ["react", "vue", "angular", "django", "flask", "fastapi", "express", "spring"]
        for framework in frameworks:
            if framework in text:
                tech_keywords.append(framework)
        
        # Technologies
        technologies = ["cli", "gui", "web", "api", "database", "sqlite", "postgresql", "mongodb"]
        for tech in technologies:
            if tech in text:
                tech_keywords.append(tech)
        
        return tech_keywords[:5]
    
    def _extract_domain_keywords(self, goal: str, description: str) -> List[str]:
        """Extract domain/application keywords."""
        text = f"{goal} {description}".lower()
        
        domains = []
        
        # Application types
        app_types = ["task manager", "file manager", "text editor", "calculator", "game", 
                    "utility", "tool", "organizer", "tracker", "monitor", "analyzer"]
        for app_type in app_types:
            if app_type in text:
                domains.append(app_type.replace(" ", "-"))
        
        # Business domains
        business_domains = ["productivity", "finance", "education", "health", "social", 
                          "communication", "media", "security", "development"]
        for domain in business_domains:
            if domain in text:
                domains.append(domain)
        
        return domains[:3] if domains else ["application"]
    
    def _search_and_analyze(self, 
                          query: str, 
                          project_charter: Dict[str, Any], 
                          task_graph: TaskGraphDefinition) -> List[OpenSourceCandidate]:
        """Execute search and analyze results."""
        try:
            # Use web search tool
            search_results = self._execute_web_search(query)
            
            candidates = []
            for result in search_results[:self.max_search_results]:
                candidate = self._analyze_search_result(result, project_charter, task_graph)
                if candidate:
                    candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            logger.error(f"Search and analysis failed for query '{query}': {e}")
            return []
    
    def _execute_web_search(self, query: str) -> List[Dict[str, Any]]:
        """Execute web search using the web search tool."""
        try:
            from vibe_coder.tools.web_search import WebSearchTool

            # Initialize web search tool
            web_search = WebSearchTool()

            # Execute search
            search_results = web_search.search(query, num_results=self.max_search_results)

            if search_results.get("success"):
                results = search_results.get("results", [])
                logger.info(f"Web search for '{query}' returned {len(results)} results")
                return results
            else:
                logger.warning(f"Web search failed for query '{query}': {search_results.get('error', 'Unknown error')}")
                return []

        except ImportError:
            logger.warning("Web search tool not available, using fallback")
            return self._fallback_search_results(query)
        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return []

    def _fallback_search_results(self, query: str) -> List[Dict[str, Any]]:
        """Provide fallback search results when web search is unavailable."""
        # Generate some realistic-looking GitHub project results based on query
        fallback_results = []

        # Extract key terms from query
        terms = query.lower().split()
        tech_terms = [t for t in terms if t in ["python", "javascript", "react", "cli", "gui", "api"]]
        domain_terms = [t for t in terms if t in ["task", "file", "manager", "tool", "utility"]]

        if tech_terms and domain_terms:
            # Generate plausible project names
            projects = [
                f"{domain_terms[0]}-{tech_terms[0]}",
                f"{tech_terms[0]}-{domain_terms[0]}-tool",
                f"awesome-{domain_terms[0]}-{tech_terms[0]}"
            ]

            for i, project in enumerate(projects):
                fallback_results.append({
                    "title": f"{project} - GitHub",
                    "url": f"https://github.com/example/{project}",
                    "snippet": f"A {tech_terms[0]} {domain_terms[0]} application with modern features and clean architecture. Includes CLI interface, configuration management, and extensible plugin system."
                })

        return fallback_results[:3]
    
    def _analyze_search_result(self,
                             result: Dict[str, Any],
                             project_charter: Dict[str, Any],
                             task_graph: TaskGraphDefinition) -> Optional[OpenSourceCandidate]:
        """Analyze a single search result to create a candidate."""
        try:
            url = result.get("url", "")
            title = result.get("title", "")
            snippet = result.get("snippet", "")

            # Skip non-GitHub results for now
            if "github.com" not in url:
                return None

            # Extract project info
            project_name = self._extract_project_name(url, title)

            # Get GitHub metrics
            github_metrics = self._get_github_metrics(url)

            # Analyze coverage
            coverage_percentage = self._estimate_coverage(snippet, task_graph)
            covered_tasks = self._identify_covered_tasks(snippet, task_graph)

            # Enhanced analysis with GitHub data
            analysis = self._create_detailed_analysis(snippet, github_metrics, coverage_percentage)

            # Create candidate with GitHub metrics
            candidate = OpenSourceCandidate(
                name=project_name,
                url=url,
                description=snippet,
                stars=github_metrics.get("stars"),
                last_updated=github_metrics.get("last_updated"),
                license=github_metrics.get("license"),
                language=github_metrics.get("language"),
                coverage_percentage=coverage_percentage,
                covered_tasks=covered_tasks,
                analysis=analysis
            )

            return candidate

        except Exception as e:
            logger.error(f"Result analysis failed: {e}")
            return None

    def _get_github_metrics(self, github_url: str) -> Dict[str, Any]:
        """Get GitHub repository metrics."""
        try:
            import requests
            import re

            # Extract owner/repo from URL
            match = re.search(r'github\.com/([^/]+)/([^/]+)', github_url)
            if not match:
                return {}

            owner, repo = match.groups()

            # GitHub API endpoint
            api_url = f"https://api.github.com/repos/{owner}/{repo}"

            # Make request (no auth needed for public repos)
            response = requests.get(api_url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                return {
                    "stars": data.get("stargazers_count", 0),
                    "forks": data.get("forks_count", 0),
                    "last_updated": data.get("updated_at", ""),
                    "license": data.get("license", {}).get("name", "Unknown") if data.get("license") else "Unknown",
                    "language": data.get("language", "Unknown"),
                    "description": data.get("description", ""),
                    "open_issues": data.get("open_issues_count", 0)
                }
            else:
                logger.warning(f"GitHub API request failed: {response.status_code}")
                return {}

        except ImportError:
            logger.warning("requests library not available for GitHub API")
            return {}
        except Exception as e:
            logger.error(f"GitHub metrics retrieval failed: {e}")
            return {}

    def _create_detailed_analysis(self, description: str, github_metrics: Dict[str, Any], coverage: float) -> str:
        """Create detailed analysis combining description and GitHub metrics."""
        analysis_parts = []

        # Coverage analysis
        analysis_parts.append(f"Estimated {coverage:.0%} task coverage based on description analysis.")

        # GitHub metrics analysis
        if github_metrics:
            stars = github_metrics.get("stars", 0)
            if stars > 1000:
                analysis_parts.append(f"High community adoption with {stars:,} stars.")
            elif stars > 100:
                analysis_parts.append(f"Good community interest with {stars} stars.")
            else:
                analysis_parts.append(f"Smaller project with {stars} stars.")

            license_name = github_metrics.get("license", "Unknown")
            if license_name in self.preferred_licenses:
                analysis_parts.append(f"Permissive {license_name} license.")
            elif license_name != "Unknown":
                analysis_parts.append(f"Uses {license_name} license (review terms).")

            language = github_metrics.get("language", "Unknown")
            if language != "Unknown":
                analysis_parts.append(f"Written in {language}.")

        return " ".join(analysis_parts)
    
    def _extract_project_name(self, url: str, title: str) -> str:
        """Extract project name from URL or title."""
        try:
            parsed = urlparse(url)
            if "github.com" in parsed.netloc:
                path_parts = parsed.path.strip("/").split("/")
                if len(path_parts) >= 2:
                    return f"{path_parts[0]}/{path_parts[1]}"
            return title.split(" - ")[0] if " - " in title else title
        except:
            return title
    
    def _estimate_coverage(self, description: str, task_graph: TaskGraphDefinition) -> float:
        """Estimate what percentage of tasks this project might cover."""
        if not task_graph.nodes:
            return 0.0
        
        description_lower = description.lower()
        covered_count = 0
        
        for task in task_graph.nodes:
            task_desc_lower = task.description.lower()
            # Simple keyword matching - could be enhanced with NLP
            if any(word in description_lower for word in task_desc_lower.split()[:3]):
                covered_count += 1
        
        return covered_count / len(task_graph.nodes)
    
    def _identify_covered_tasks(self, description: str, task_graph: TaskGraphDefinition) -> List[str]:
        """Identify which specific tasks might be covered."""
        description_lower = description.lower()
        covered_tasks = []
        
        for task in task_graph.nodes:
            task_desc_lower = task.description.lower()
            if any(word in description_lower for word in task_desc_lower.split()[:3]):
                covered_tasks.append(task.id)
        
        return covered_tasks
    
    def _deduplicate_candidates(self, candidates: List[OpenSourceCandidate]) -> List[OpenSourceCandidate]:
        """Remove duplicate candidates based on URL."""
        seen_urls = set()
        unique_candidates = []
        
        for candidate in candidates:
            if candidate.url not in seen_urls:
                seen_urls.add(candidate.url)
                unique_candidates.append(candidate)
        
        return unique_candidates
    
    def _rank_candidates(self, 
                        candidates: List[OpenSourceCandidate], 
                        task_graph: TaskGraphDefinition) -> List[OpenSourceCandidate]:
        """Rank candidates by recommendation score."""
        for candidate in candidates:
            score = self._calculate_recommendation_score(candidate)
            candidate.recommendation_score = score
        
        return sorted(candidates, key=lambda c: c.recommendation_score, reverse=True)
    
    def _calculate_recommendation_score(self, candidate: OpenSourceCandidate) -> float:
        """Calculate overall recommendation score for a candidate."""
        score = 0.0
        
        # Coverage weight (50%)
        score += candidate.coverage_percentage * 0.5
        
        # GitHub indicators (30%)
        if "github.com" in candidate.url:
            score += 0.3
        
        # Description quality (20%)
        if len(candidate.description) > 100:
            score += 0.2
        
        return min(score, 1.0)
