"""
Socratic Supervisor: Critical analysis and challenge of plans.
Acts as a master software architect who critiques and challenges plans.
"""

import logging
from typing import Dict, Any, List, Optional
from vibe_coder.agents.base_agent import BaseAgent
from vibe_coder.prompts.core_prompts import SOCRATIC_SUPERVISOR_PROMPT

logger = logging.getLogger(__name__)


class SocraticSupervisor(BaseAgent):
    """
    Agent for critical analysis and challenging of plans.
    
    The Socratic Supervisor acts as a master software architect whose role
    is to critique and challenge plans, not to write code. It finds hidden
    assumptions, edge cases, and logical flaws.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Socratic Supervisor.
        
        Args:
            config: Configuration dictionary containing LLM settings
        """
        super().__init__(config, SOCRATIC_SUPERVISOR_PROMPT)
        self.agent_name = "socratic_supervisor"
        logger.info("SocraticSupervisor initialized for critical analysis")
    
    def critique_plan(self, plan: Dict[str, Any], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Critique a proposed plan with pointed questions and suggestions.
        
        Args:
            plan: Dictionary containing plan details
            context: Optional context information
            
        Returns:
            Dictionary containing critique and questions
        """
        try:
            context = context or {}
            
            user_query = self._build_critique_query(plan, context)
            
            # Get critique from LLM
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "critique_questions": self._parse_critique_questions(response),
                "hidden_assumptions": self._extract_hidden_assumptions(response),
                "edge_cases": self._extract_edge_cases(response),
                "logical_flaws": self._extract_logical_flaws(response),
                "improvement_suggestions": self._extract_improvement_suggestions(response),
                "architectural_concerns": self._extract_architectural_concerns(response),
                "critique_reasoning": response,
                "agent": "socratic_supervisor"
            }
            
            logger.info(f"Generated {len(result['critique_questions'])} critique questions")
            return result
            
        except Exception as e:
            logger.error(f"Plan critique failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "critique_questions": [],
                "agent": "socratic_supervisor"
            }
    
    def challenge_assumptions(self, assumptions: List[str], domain_context: str = "") -> Dict[str, Any]:
        """
        Challenge a list of assumptions with Socratic questioning.
        
        Args:
            assumptions: List of assumptions to challenge
            domain_context: Context about the domain or problem space
            
        Returns:
            Dictionary containing challenges to each assumption
        """
        try:
            user_query = f"""
            Domain context: {domain_context}
            
            Assumptions to challenge:
            {chr(10).join([f"- {assumption}" for assumption in assumptions])}
            
            For each assumption, ask pointed questions that challenge its validity.
            What evidence supports these assumptions? What could invalidate them?
            What are the hidden dependencies? What happens if these assumptions are wrong?
            
            Provide Socratic questions that expose potential flaws.
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "assumption_challenges": self._parse_assumption_challenges(response, assumptions),
                "critical_questions": self._parse_critique_questions(response),
                "evidence_requirements": self._extract_evidence_requirements(response),
                "invalidation_scenarios": self._extract_invalidation_scenarios(response),
                "challenge_reasoning": response,
                "agent": "socratic_supervisor"
            }
            
            logger.info(f"Challenged {len(assumptions)} assumptions")
            return result
            
        except Exception as e:
            logger.error(f"Assumption challenging failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "assumption_challenges": [],
                "agent": "socratic_supervisor"
            }
    
    def analyze_architecture(self, architectural_proposal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze an architectural proposal for potential issues.
        
        Args:
            architectural_proposal: Dictionary containing architectural details
            
        Returns:
            Dictionary containing architectural analysis
        """
        try:
            user_query = f"""
            Architectural proposal:
            Components: {architectural_proposal.get('components', [])}
            Interactions: {architectural_proposal.get('interactions', [])}
            Data flow: {architectural_proposal.get('data_flow', '')}
            Scalability approach: {architectural_proposal.get('scalability', '')}
            
            Analyze this architecture critically:
            - What are the potential bottlenecks?
            - Where are the single points of failure?
            - What assumptions about scale are being made?
            - How will this handle edge cases?
            - What are the maintenance implications?
            - Where might this break under stress?
            
            Provide pointed architectural critique.
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "architectural_concerns": self._extract_architectural_concerns(response),
                "bottlenecks": self._extract_bottlenecks(response),
                "failure_points": self._extract_failure_points(response),
                "scalability_issues": self._extract_scalability_issues(response),
                "maintenance_concerns": self._extract_maintenance_concerns(response),
                "stress_vulnerabilities": self._extract_stress_vulnerabilities(response),
                "architectural_reasoning": response,
                "agent": "socratic_supervisor"
            }
            
            logger.info("Completed architectural analysis")
            return result
            
        except Exception as e:
            logger.error(f"Architectural analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "architectural_concerns": [],
                "agent": "socratic_supervisor"
            }
    
    def evaluate_task_decomposition(self, task_graph: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate a task decomposition for completeness and logic.
        
        Args:
            task_graph: Dictionary containing task graph structure
            
        Returns:
            Dictionary containing evaluation results
        """
        try:
            user_query = f"""
            Task decomposition to evaluate:
            Tasks: {len(task_graph.get('nodes', []))} tasks
            Dependencies: {len(task_graph.get('edges', []))} dependencies
            
            Task details:
            {chr(10).join([f"- {task.get('description', '')}" for task in task_graph.get('nodes', [])])}
            
            Critically evaluate this decomposition:
            - Are all necessary tasks included?
            - Are the dependencies logical and complete?
            - Are any tasks too large or too small?
            - What edge cases might be missed?
            - Where could this decomposition fail?
            - What assumptions are being made about task execution?
            
            Provide critical questions and concerns.
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "decomposition_concerns": self._extract_decomposition_concerns(response),
                "missing_tasks": self._extract_missing_tasks(response),
                "dependency_issues": self._extract_dependency_issues(response),
                "sizing_concerns": self._extract_sizing_concerns(response),
                "execution_assumptions": self._extract_execution_assumptions(response),
                "evaluation_reasoning": response,
                "agent": "socratic_supervisor"
            }
            
            logger.info("Completed task decomposition evaluation")
            return result
            
        except Exception as e:
            logger.error(f"Task decomposition evaluation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "decomposition_concerns": [],
                "agent": "socratic_supervisor"
            }
    
    def _build_critique_query(self, plan: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Build query for plan critique."""
        plan_desc = plan.get('description', '')
        plan_steps = plan.get('steps', [])
        plan_assumptions = plan.get('assumptions', [])
        
        context_info = ""
        if context:
            context_info = f"\nContext: {context.get('description', '')}"
            if context.get('constraints'):
                context_info += f"\nConstraints: {', '.join(context['constraints'])}"
        
        query = f"""
        Plan to critique: {plan_desc}
        {context_info}
        
        Plan steps:
        {chr(10).join([f"- {step}" for step in plan_steps])}
        
        Stated assumptions:
        {chr(10).join([f"- {assumption}" for assumption in plan_assumptions])}
        
        Critique this plan with pointed questions. What hidden assumptions exist?
        What edge cases haven't been considered? Where might this plan fail?
        What logical flaws do you see? Provide specific, challenging questions.
        """
        
        return query
    
    def _parse_critique_questions(self, response: str) -> List[str]:
        """Parse critique questions from response."""
        questions = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.endswith('?') or 'question' in line.lower():
                # Clean up the question
                if line.startswith(('-', '*', '•')):
                    line = line[1:].strip()
                if line.startswith(('1.', '2.', '3.', '4.', '5.')):
                    line = line.split('.', 1)[1].strip()
                questions.append(line)
        
        return questions[:10]  # Limit to top 10 questions
    
    def _extract_hidden_assumptions(self, response: str) -> List[str]:
        """Extract hidden assumptions from response."""
        assumptions = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['assumption', 'assumes', 'assuming']):
                assumptions.append(line)
        
        return assumptions[:5]

    def _extract_edge_cases(self, response: str) -> List[str]:
        """Extract edge cases from response."""
        edge_cases = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['edge case', 'edge', 'corner case', 'boundary']):
                edge_cases.append(line)

        return edge_cases[:5]

    def _extract_logical_flaws(self, response: str) -> List[str]:
        """Extract logical flaws from response."""
        flaws = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['flaw', 'logical', 'inconsistent', 'contradiction']):
                flaws.append(line)

        return flaws[:5]

    def _extract_improvement_suggestions(self, response: str) -> List[str]:
        """Extract improvement suggestions from response."""
        suggestions = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['suggest', 'improve', 'better', 'consider', 'recommend']):
                suggestions.append(line)

        return suggestions[:5]

    def _extract_architectural_concerns(self, response: str) -> List[str]:
        """Extract architectural concerns from response."""
        concerns = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['architecture', 'design', 'structure', 'component']):
                concerns.append(line)

        return concerns[:5]

    def _parse_assumption_challenges(self, response: str, original_assumptions: List[str]) -> List[Dict[str, Any]]:
        """Parse challenges to specific assumptions."""
        challenges = []

        for assumption in original_assumptions:
            # Find challenges related to this assumption
            assumption_keywords = assumption.lower().split()[:3]  # First 3 words

            related_challenges = []
            lines = response.split('\n')

            for line in lines:
                line = line.strip()
                if any(keyword in line.lower() for keyword in assumption_keywords):
                    related_challenges.append(line)

            challenges.append({
                "assumption": assumption,
                "challenges": related_challenges[:3],  # Top 3 challenges per assumption
                "challenge_count": len(related_challenges)
            })

        return challenges

    def _extract_evidence_requirements(self, response: str) -> List[str]:
        """Extract evidence requirements from response."""
        requirements = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['evidence', 'proof', 'validate', 'verify']):
                requirements.append(line)

        return requirements[:5]

    def _extract_invalidation_scenarios(self, response: str) -> List[str]:
        """Extract scenarios that could invalidate assumptions."""
        scenarios = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['if', 'when', 'scenario', 'what if', 'suppose']):
                scenarios.append(line)

        return scenarios[:5]

    def _extract_bottlenecks(self, response: str) -> List[str]:
        """Extract potential bottlenecks from response."""
        bottlenecks = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['bottleneck', 'slow', 'performance', 'limit']):
                bottlenecks.append(line)

        return bottlenecks[:3]

    def _extract_failure_points(self, response: str) -> List[str]:
        """Extract single points of failure from response."""
        failure_points = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['failure', 'fail', 'single point', 'spof']):
                failure_points.append(line)

        return failure_points[:3]

    def _extract_scalability_issues(self, response: str) -> List[str]:
        """Extract scalability issues from response."""
        issues = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['scale', 'scalability', 'growth', 'load']):
                issues.append(line)

        return issues[:3]

    def _extract_maintenance_concerns(self, response: str) -> List[str]:
        """Extract maintenance concerns from response."""
        concerns = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['maintenance', 'maintain', 'update', 'modify']):
                concerns.append(line)

        return concerns[:3]

    def _extract_stress_vulnerabilities(self, response: str) -> List[str]:
        """Extract stress vulnerabilities from response."""
        vulnerabilities = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['stress', 'pressure', 'overload', 'break']):
                vulnerabilities.append(line)

        return vulnerabilities[:3]

    def _extract_decomposition_concerns(self, response: str) -> List[str]:
        """Extract task decomposition concerns from response."""
        concerns = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['decomposition', 'task', 'concern', 'issue']):
                concerns.append(line)

        return concerns[:5]

    def _extract_missing_tasks(self, response: str) -> List[str]:
        """Extract potentially missing tasks from response."""
        missing = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['missing', 'forgot', 'overlooked', 'need']):
                missing.append(line)

        return missing[:3]

    def _extract_dependency_issues(self, response: str) -> List[str]:
        """Extract dependency issues from response."""
        issues = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['dependency', 'depend', 'order', 'sequence']):
                issues.append(line)

        return issues[:3]

    def _extract_sizing_concerns(self, response: str) -> List[str]:
        """Extract task sizing concerns from response."""
        concerns = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['too large', 'too small', 'size', 'granular']):
                concerns.append(line)

        return concerns[:3]

    def _extract_execution_assumptions(self, response: str) -> List[str]:
        """Extract execution assumptions from response."""
        assumptions = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['execution', 'assume', 'expect', 'parallel']):
                assumptions.append(line)

        return assumptions[:3]

    def execute_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task assigned to the Socratic Supervisor.

        Args:
            task_data: Dictionary containing task information

        Returns:
            Dictionary containing the result of task execution
        """
        try:
            task_type = task_data.get("type", "critique_plan")

            if task_type == "critique_plan":
                return self.critique_plan(
                    task_data.get("plan", {}),
                    task_data.get("context", {})
                )
            elif task_type == "challenge_assumptions":
                return self.challenge_assumptions(
                    task_data.get("assumptions", []),
                    task_data.get("domain_context", "")
                )
            elif task_type == "analyze_architecture":
                return self.analyze_architecture(task_data.get("architectural_proposal", {}))
            elif task_type == "evaluate_decomposition":
                return self.evaluate_task_decomposition(task_data.get("task_graph", {}))
            else:
                # Default to plan critique
                return self.critique_plan(
                    task_data.get("plan", {}),
                    task_data.get("context", {})
                )

        except Exception as e:
            logger.error(f"Socratic Supervisor task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent": "socratic_supervisor"
            }
