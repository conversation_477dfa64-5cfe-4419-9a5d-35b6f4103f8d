import logging
from typing import List, Dict, Any, Optional

# Data models
from vibe_coder.data_models.cognitive_models import HypothesisLogEntry
from vibe_coder.data_models.api_contracts import CorrectiveAction, ToolCallRequest

# Memory and LLM imports
from vibe_coder.memory.action_ledger import ActionHypothesisLedger
from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import PydanticOutputParser

logger = logging.getLogger(__name__)

# Reflection prompt templates
REFLECTION_TIER1_PROMPT_TEMPLATE = """
You are a debugging assistant. You are given the history of a failed task,
specifically the last hypothesis, action taken, and the error or failure result.
Your goal is to propose a simple, direct corrective action using a tool call.

Task History (last entry):
Hypothesis: {hypothesis_text}
Action Taken: {action_taken}
Result/Error: {action_result_summary}

Based on this, provide a JSON object for a corrective tool call.
The JSON object must conform to the following Pydantic model:
{format_instructions}

Reason step-by-step about the error and the correction before outputting the JSON.
Your reasoning should be enclosed in <thought></thought> tags.
Output only the JSON object.
"""

REFLECTION_TIER2_PROMPT_TEMPLATE = """
You are a senior software architect. A task has failed multiple times or in a complex way.
You are given the recent history of attempts for this task.
Your goal is to rethink the strategy. Do NOT propose a direct code fix or tool call.
Instead, suggest a high-level strategic change, ask clarifying questions to the supervisor/user,
or identify a fundamental misunderstanding.

Task History:
{formatted_task_history}

Based on this history, provide your strategic advice or questions.
Your response should be a dictionary with a 'suggestion' key.
Example: {{"suggestion": "The current approach of X seems to be hitting a rate limit. Consider Y instead, or investigate Z."}}
"""


class MetaAgentCognoscente:
    """
    Embodies the self-correction and reflection logic for the Vibe Coder.
    It analyzes failures and proposes corrective actions or strategic shifts.
    """
    def __init__(self,
                 ledger: ActionHypothesisLedger,
                 llm: Optional[BaseChatModel] = None
                ):
        """
        Initializes the MetaAgentCognoscente.

        Args:
            ledger: An instance of ActionHypothesisLedger to access task history.
            llm: A LangChain compatible chat model for generating reflective responses.
        """
        self.ledger = ledger
        self.llm = llm
        logger.info("MetaAgentCognoscente initialized.")

    def _format_task_history(self, task_history: List[HypothesisLogEntry]) -> str:
        """Helper to format task history for prompts."""
        formatted_entries = []
        for entry in task_history:
            formatted_entries.append(
                f"- Hypothesis: {entry.hypothesis_text}\n"
                f"  Action: {entry.action_taken}\n"
                f"  Result: {entry.action_result_summary or 'N/A'}\n"
                f"  Success: {entry.is_success}"
            )
        return "\n".join(formatted_entries)

    def _run_tier1_correction(self, last_entry: HypothesisLogEntry) -> CorrectiveAction:
        """
        Forms a prompt for simple error correction (e.g., typo, minor logic error)
        and returns a CorrectiveAction dictionary.
        This tier aims for a direct, tool-based fix.
        """
        logger.info(f"Running Tier 1 correction for task: {last_entry.task_id}, entry: {last_entry.entry_id}")

        if self.llm:
            # Use LLM for intelligent correction
            try:
                parser = PydanticOutputParser(pydantic_object=CorrectiveAction)
                prompt_template = ChatPromptTemplate.from_messages([
                    ("system", REFLECTION_TIER1_PROMPT_TEMPLATE),
                    ("human", "Analyze the following failed attempt and provide a corrective action.")
                ])
                chain = prompt_template | self.llm | parser
                corrective_action = chain.invoke({
                    "hypothesis_text": last_entry.hypothesis_text,
                    "action_taken": last_entry.action_taken,
                    "action_result_summary": last_entry.action_result_summary or "No specific error message provided.",
                    "format_instructions": parser.get_format_instructions(),
                })
                return corrective_action
            except Exception as e:
                logger.error(f"LLM-based correction failed: {e}")
                # Fall back to simple correction

        # Simple fallback correction logic
        reasoning = f"Simple correction attempt for error: {last_entry.action_result_summary}"

        # Try to parse the original action
        original_tool_name = "unknown_tool"
        original_arguments: Dict[str, Any] = {}

        if isinstance(last_entry.action_taken, str) and ":" in last_entry.action_taken:
            parts = last_entry.action_taken.split(":", 1)
            original_tool_name = parts[0].strip()
            if len(parts) > 1:
                # Simple retry with original arguments
                original_arguments = {"retry": True}

        return CorrectiveAction(
            tool_name=original_tool_name,
            arguments=original_arguments,
            reasoning=reasoning
        )

    def _run_tier2_rethink(self, task_history: List[HypothesisLogEntry]) -> Dict[str, Any]:
        """
        Forms a prompt for the Supervisor to suggest a different strategy or
        identify a deeper issue. This tier is for more complex failures.
        Returns a dictionary, e.g., {"suggestion": "Consider X instead."}
        """
        logger.info(f"Running Tier 2 rethink for task: {task_history[0].task_id if task_history else 'Unknown'}")

        if self.llm:
            # Use LLM for strategic rethinking
            try:
                from langchain_core.output_parsers import StrOutputParser
                prompt_template = ChatPromptTemplate.from_template(REFLECTION_TIER2_PROMPT_TEMPLATE)
                chain = prompt_template | self.llm | StrOutputParser()
                response = chain.invoke({
                    "formatted_task_history": self._format_task_history(task_history)
                })
                return {"suggestion": response, "tier": 2}
            except Exception as e:
                logger.error(f"LLM-based rethinking failed: {e}")
                # Fall back to simple suggestion

        # Simple fallback suggestion
        suggestion = (
            "The repeated failures suggest a fundamental issue with the current approach. "
            "Consider verifying external dependencies or simplifying the immediate goal. "
            "Perhaps the task needs to be broken down further."
        )
        return {"suggestion": suggestion, "tier": 2}

    def reflect_on_failure(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        The main entry point for reflection.
        Examines the history for a given task_id, determines the failure tier,
        and calls a private helper for that tier.

        Args:
            task_id: The ID of the task that failed.

        Returns:
            A dictionary representing the outcome of reflection.
            For Tier 1: A CorrectiveAction model (as dict).
            For Tier 2: A dictionary with a 'suggestion' string.
            Returns None if no history is found or reflection is not applicable.
        """
        logger.info(f"Reflecting on failure for task_id: {task_id}")
        if not self.ledger:
            logger.error("ActionHypothesisLedger not available for reflection.")
            return None
            
        task_history = self.ledger.get_history_for_task(task_id)
        if not task_history:
            logger.warning(f"No history found for task_id: {task_id}. Cannot reflect.")
            return None

        # Filter for only failed entries to determine reflection strategy
        failed_entries = [entry for entry in task_history if entry.is_success is False]

        if not failed_entries:
            logger.info(f"No failed entries found for task_id: {task_id}. No reflection needed.")
            return None
        
        last_failure = failed_entries[-1]

        # Simple tier determination logic:
        # If only 1 failure, or last failure was simple, try Tier 1.
        # If multiple failures, or Tier 1 was already tried and failed again, try Tier 2.
        # This logic will need to be more sophisticated.
        
        # For now, let's default to Tier 1 if there's any failure.
        # A more robust system would track if Tier 1 was already attempted for this specific failure signature.
        
        # Let's assume for now: if it's the first or second failure for the task, try Tier 1.
        # Otherwise, Tier 2. This is a very basic heuristic.
        if len(failed_entries) <= 2: # Arbitrary threshold for Tier 1 vs Tier 2
            logger.info(f"Attempting Tier 1 correction for task {task_id}.")
            corrective_action = self._run_tier1_correction(last_failure)
            # The CorrectiveAction Pydantic model should be returned,
            # it will be serialized to dict by the caller if needed (e.g. LangGraph state)
            return corrective_action.model_dump() # Convert to dict for AgentState
        else:
            logger.info(f"Attempting Tier 2 rethink for task {task_id} due to multiple failures.")
            rethink_suggestion = self._run_tier2_rethink(failed_entries) # Pass only failed history
            return rethink_suggestion


if __name__ == '__main__':
    import uuid
    import datetime
    from pathlib import Path
    from vibe_coder.memory.action_ledger import ActionHypothesisLedger # Actual import for example

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
    
    # Setup a dummy ledger for the example
    example_db_path = Path("./data/reflection_test_ledger.db")
    example_db_path.parent.mkdir(parents=True, exist_ok=True)
    if example_db_path.exists():
        example_db_path.unlink()

    ledger = ActionHypothesisLedger(db_path=example_db_path)
    
    # Initialize MetaAgentCognoscente (no LLM for this mock example)
    cognoscente = MetaAgentCognoscente(ledger=ledger)

    task_id_tier1 = "task_reflect_001"
    task_id_tier2 = "task_reflect_002"

    # --- Simulate history for Tier 1 reflection ---
    failure_entry_tier1 = HypothesisLogEntry(
        entry_id=str(uuid.uuid4()),
        task_id=task_id_tier1,
        timestamp=datetime.datetime.now(datetime.timezone.utc),
        agent_name="CoderAgent",
        hypothesis_text="The file 'data.txt' should contain 'initial data'.",
        action_taken="tool:write_file,path:data.txt,content:initial data",
        action_result_summary="ERROR: File path 'data.txt' does not exist or is not writable.",
        is_success=False
    )
    ledger.add_entry(failure_entry_tier1)
    
    logger.info(f"\n--- Reflecting on failure for Task ID: {task_id_tier1} (Tier 1 expected) ---")
    reflection_result_t1 = cognoscente.reflect_on_failure(task_id_tier1)
    print("Tier 1 Reflection Result:")
    if reflection_result_t1:
        # Should be a CorrectiveAction dict
        print(f"  Tool Name: {reflection_result_t1.get('tool_name')}")
        print(f"  Arguments: {reflection_result_t1.get('arguments')}")
        print(f"  Reasoning: {reflection_result_t1.get('reasoning')}")
        assert "tool_name" in reflection_result_t1

    # --- Simulate history for Tier 2 reflection ---
    # Add multiple failures for task_id_tier2
    for i in range(3): # Simulate 3 failures
        failure_entry_tier2 = HypothesisLogEntry(
            entry_id=str(uuid.uuid4()),
            task_id=task_id_tier2,
            timestamp=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=i),
            agent_name="VerifierAgent",
            hypothesis_text=f"Attempt {i+1}: Verification of component X should pass.",
            action_taken=f"tool:run_unit_tests,project_path:/src/component_x",
            action_result_summary=f"FAILURE: Test 'test_critical_feature_{i}' failed. Assertion Error.",
            is_success=False
        )
        ledger.add_entry(failure_entry_tier2)

    logger.info(f"\n--- Reflecting on failure for Task ID: {task_id_tier2} (Tier 2 expected) ---")
    reflection_result_t2 = cognoscente.reflect_on_failure(task_id_tier2)
    print("Tier 2 Reflection Result:")
    if reflection_result_t2:
        # Should be a dict with 'suggestion'
        print(f"  Suggestion: {reflection_result_t2.get('suggestion')}")
        assert "suggestion" in reflection_result_t2

    # --- Test case: No failures ---
    task_id_no_failure = "task_success_003"
    success_entry = HypothesisLogEntry(
        entry_id=str(uuid.uuid4()),
        task_id=task_id_no_failure,
        timestamp=datetime.datetime.now(datetime.timezone.utc),
        agent_name="CoderAgent",
        hypothesis_text="This should work.",
        action_taken="tool:read_file,path:existing_file.txt",
        action_result_summary="Content read successfully.",
        is_success=True
    )
    ledger.add_entry(success_entry)
    logger.info(f"\n--- Reflecting on Task ID: {task_id_no_failure} (No failure expected) ---")
    reflection_no_failure = cognoscente.reflect_on_failure(task_id_no_failure)
    print(f"Reflection (no failure) Result: {reflection_no_failure}")
    assert reflection_no_failure is None


    # --- Test case: No history ---
    task_id_no_history = "task_no_history_004"
    logger.info(f"\n--- Reflecting on Task ID: {task_id_no_history} (No history expected) ---")
    reflection_no_history = cognoscente.reflect_on_failure(task_id_no_history)
    print(f"Reflection (no history) Result: {reflection_no_history}")
    assert reflection_no_history is None

    ledger.close_connection() # Important to close the ledger connection
    logger.info("MetaAgentCognoscente example finished.")
    # if example_db_path.exists(): # Clean up
    #     example_db_path.unlink()

