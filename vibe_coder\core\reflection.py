import logging
from typing import List, Dict, Any, Optional

# Data models
from vibe_coder.data_models.cognitive_models import HypothesisLogEntry
from vibe_coder.data_models.api_contracts import CorrectiveAction, ToolCallRequest

# Memory and LLM imports
from vibe_coder.memory.action_ledger import ActionHypothesisLedger
from langchain_core.language_models import BaseChatModel
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import PydanticOutputParser

logger = logging.getLogger(__name__)

# Reflection prompt templates
REFLECTION_TIER1_PROMPT_TEMPLATE = """
You are a debugging assistant. You are given the history of a failed task,
specifically the last hypothesis, action taken, and the error or failure result.
Your goal is to propose a simple, direct corrective action using a tool call.

Task History (last entry):
Hypothesis: {hypothesis_text}
Action Taken: {action_taken}
Result/Error: {action_result_summary}

Based on this, provide a JSON object for a corrective tool call.
The JSON object must conform to the following Pydantic model:
{format_instructions}

Reason step-by-step about the error and the correction before outputting the JSON.
Your reasoning should be enclosed in <thought></thought> tags.
Output only the JSON object.
"""

REFLECTION_TIER2_PROMPT_TEMPLATE = """
You are a senior software architect. A task has failed multiple times or in a complex way.
You are given the recent history of attempts for this task.
Your goal is to rethink the strategy. Do NOT propose a direct code fix or tool call.
Instead, suggest a high-level strategic change, ask clarifying questions to the supervisor/user,
or identify a fundamental misunderstanding.

Task History:
{formatted_task_history}

Based on this history, provide your strategic advice or questions.
Your response should be a dictionary with a 'suggestion' key.
Example: {{"suggestion": "The current approach of X seems to be hitting a rate limit. Consider Y instead, or investigate Z."}}
"""


class MetaAgentCognoscente:
    """
    Embodies the self-correction and reflection logic for the Vibe Coder.
    It analyzes failures and proposes corrective actions or strategic shifts.
    """
    def __init__(self,
                 ledger: ActionHypothesisLedger,
                 llm: Optional[BaseChatModel] = None
                ):
        """
        Initializes the MetaAgentCognoscente.

        Args:
            ledger: An instance of ActionHypothesisLedger to access task history.
            llm: A LangChain compatible chat model for generating reflective responses.
        """
        self.ledger = ledger
        self.llm = llm
        logger.info("MetaAgentCognoscente initialized.")

    def _format_task_history(self, task_history: List[HypothesisLogEntry]) -> str:
        """Helper to format task history for prompts."""
        formatted_entries = []
        for entry in task_history:
            formatted_entries.append(
                f"- Hypothesis: {entry.hypothesis_text}\n"
                f"  Action: {entry.action_taken}\n"
                f"  Result: {entry.action_result_summary or 'N/A'}\n"
                f"  Success: {entry.is_success}"
            )
        return "\n".join(formatted_entries)

    def _run_tier1_correction(self, last_entry: HypothesisLogEntry) -> CorrectiveAction:
        """
        Forms a prompt for simple error correction (e.g., typo, minor logic error)
        and returns a CorrectiveAction dictionary.
        This tier aims for a direct, tool-based fix.
        """
        logger.info(f"Running Tier 1 correction for task: {last_entry.task_id}, entry: {last_entry.entry_id}")

        if self.llm:
            # Use LLM for intelligent correction
            try:
                parser = PydanticOutputParser(pydantic_object=CorrectiveAction)
                prompt_template = ChatPromptTemplate.from_messages([
                    ("system", REFLECTION_TIER1_PROMPT_TEMPLATE),
                    ("human", "Analyze the following failed attempt and provide a corrective action.")
                ])
                chain = prompt_template | self.llm | parser
                corrective_action = chain.invoke({
                    "hypothesis_text": last_entry.hypothesis_text,
                    "action_taken": last_entry.action_taken,
                    "action_result_summary": last_entry.action_result_summary or "No specific error message provided.",
                    "format_instructions": parser.get_format_instructions(),
                })
                return corrective_action
            except Exception as e:
                logger.error(f"LLM-based correction failed: {e}")
                # Fall back to simple correction

        # Simple fallback correction logic
        reasoning = f"Simple correction attempt for error: {last_entry.action_result_summary}"

        # Try to parse the original action
        original_tool_name = "unknown_tool"
        original_arguments: Dict[str, Any] = {}

        if isinstance(last_entry.action_taken, str) and ":" in last_entry.action_taken:
            parts = last_entry.action_taken.split(":", 1)
            original_tool_name = parts[0].strip()
            if len(parts) > 1:
                # Simple retry with original arguments
                original_arguments = {"retry": True}

        return CorrectiveAction(
            tool_name=original_tool_name,
            arguments=original_arguments,
            reasoning=reasoning
        )

    def _run_tier2_rethink(self, task_history: List[HypothesisLogEntry]) -> Dict[str, Any]:
        """
        Forms a prompt for the Supervisor to suggest a different strategy or
        identify a deeper issue. This tier is for more complex failures.
        Returns a dictionary, e.g., {"suggestion": "Consider X instead."}
        """
        logger.info(f"Running Tier 2 rethink for task: {task_history[0].task_id if task_history else 'Unknown'}")

        if self.llm:
            # Use LLM for strategic rethinking
            try:
                from langchain_core.output_parsers import StrOutputParser
                prompt_template = ChatPromptTemplate.from_template(REFLECTION_TIER2_PROMPT_TEMPLATE)
                chain = prompt_template | self.llm | StrOutputParser()
                response = chain.invoke({
                    "formatted_task_history": self._format_task_history(task_history)
                })
                return {"suggestion": response, "tier": 2}
            except Exception as e:
                logger.error(f"LLM-based rethinking failed: {e}")
                # Fall back to simple suggestion

        # Simple fallback suggestion
        suggestion = (
            "The repeated failures suggest a fundamental issue with the current approach. "
            "Consider verifying external dependencies or simplifying the immediate goal. "
            "Perhaps the task needs to be broken down further."
        )
        return {"suggestion": suggestion, "tier": 2}

    def reflect_on_failure(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        The main entry point for reflection.
        Examines the history for a given task_id, determines the failure tier,
        and calls a private helper for that tier.

        Args:
            task_id: The ID of the task that failed.

        Returns:
            A dictionary representing the outcome of reflection.
            For Tier 1: A CorrectiveAction model (as dict).
            For Tier 2: A dictionary with a 'suggestion' string.
            Returns None if no history is found or reflection is not applicable.
        """
        logger.info(f"Reflecting on failure for task_id: {task_id}")
        if not self.ledger:
            logger.error("ActionHypothesisLedger not available for reflection.")
            return None
            
        task_history = self.ledger.get_history_for_task(task_id)
        if not task_history:
            logger.warning(f"No history found for task_id: {task_id}. Cannot reflect.")
            return None

        # Filter for only failed entries to determine reflection strategy
        failed_entries = [entry for entry in task_history if entry.is_success is False]

        if not failed_entries:
            logger.info(f"No failed entries found for task_id: {task_id}. No reflection needed.")
            return None
        
        last_failure = failed_entries[-1]

        # Simple tier determination logic:
        # If only 1 failure, or last failure was simple, try Tier 1.
        # If multiple failures, or Tier 1 was already tried and failed again, try Tier 2.
        # This logic will need to be more sophisticated.
        
        # For now, let's default to Tier 1 if there's any failure.
        # A more robust system would track if Tier 1 was already attempted for this specific failure signature.
        
        # Let's assume for now: if it's the first or second failure for the task, try Tier 1.
        # Otherwise, Tier 2. This is a very basic heuristic.
        if len(failed_entries) <= 2: # Arbitrary threshold for Tier 1 vs Tier 2
            logger.info(f"Attempting Tier 1 correction for task {task_id}.")
            corrective_action = self._run_tier1_correction(last_failure)
            # The CorrectiveAction Pydantic model should be returned,
            # it will be serialized to dict by the caller if needed (e.g. LangGraph state)
            return corrective_action.model_dump() # Convert to dict for AgentState
        else:
            logger.info(f"Attempting Tier 2 rethink for task {task_id} due to multiple failures.")
            rethink_suggestion = self._run_tier2_rethink(failed_entries) # Pass only failed history
            return rethink_suggestion


# This module is designed to be imported and used by other components.
# For testing, use the test files in the tests/ directory.



