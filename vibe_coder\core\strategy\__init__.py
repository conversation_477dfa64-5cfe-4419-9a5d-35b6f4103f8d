"""
Strategic Team Module: Houses the strategic agents for high-level planning and debate.

This module contains:
- MuseAgent: Unconstrained creative ideation
- OracleAgent: Risk assessment and evidence-based analysis  
- WisdomLayer: Ethical governance and strategic alignment
- StrategicDebate: Orchestrates Muse vs Oracle dialectic
"""

from .muse_agent import MuseAgent
from .oracle_agent import OracleAgent
from .wisdom_layer import <PERSON><PERSON><PERSON><PERSON>
from .strategic_debate import StrategicDebate

__all__ = [
    'MuseAgent',
    'OracleAgent', 
    'WisdomLayer',
    'StrategicDebate'
]
