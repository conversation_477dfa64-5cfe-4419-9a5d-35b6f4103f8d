"""
Muse Agent: Unconstrained 'blue sky' ideation agent.
Generates creative, unconventional solutions without feasibility constraints.
"""

import logging
from typing import Dict, Any, List, Optional
from vibe_coder.agents.base_agent import BaseAgent
from vibe_coder.prompts.core_prompts import MUSE_PROMPT

logger = logging.getLogger(__name__)


class MuseAgent(BaseAgent):
    """
    Agent for unconstrained creative ideation.
    
    The Muse ignores initial feasibility constraints and focuses on generating
    creative and unconventional alternatives to problems. It embodies the
    optimist/innovator role in the strategic dialectic.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Muse Agent.
        
        Args:
            config: Configuration dictionary containing LLM settings
        """
        super().__init__(config, MUSE_PROMPT)
        self.agent_name = "muse_agent"
        logger.info("MuseAgent initialized for creative ideation")
    
    def generate_alternatives(self, problem_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate creative alternatives to a problem.
        
        Args:
            problem_context: Dictionary containing problem description and context
            
        Returns:
            Dictionary containing creative alternatives and reasoning
        """
        try:
            user_query = self._build_alternatives_query(problem_context)
            
            # Get creative alternatives from LLM
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "alternatives": self._parse_alternatives_response(response),
                "creative_reasoning": response,
                "agent": "muse"
            }
            
            logger.info(f"Generated {len(result['alternatives'])} creative alternatives")
            return result
            
        except Exception as e:
            logger.error(f"Alternative generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "alternatives": [],
                "agent": "muse"
            }
    
    def brainstorm_solutions(self, problem: str, constraints_to_ignore: List[str] = None) -> Dict[str, Any]:
        """
        Brainstorm solutions ignoring specified constraints.
        
        Args:
            problem: Problem description
            constraints_to_ignore: List of constraints to explicitly ignore
            
        Returns:
            Dictionary containing brainstormed solutions
        """
        try:
            constraints_to_ignore = constraints_to_ignore or []
            
            user_query = f"""
            Problem: {problem}
            
            Ignore these constraints completely: {', '.join(constraints_to_ignore)}
            
            Generate the most creative, unconventional solutions you can imagine.
            Think completely outside the box. What would be possible if we had unlimited resources,
            time, and technology? What approaches have never been tried before?
            
            Provide 5-7 wildly creative solutions with brief explanations.
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "solutions": self._parse_solutions_response(response),
                "ignored_constraints": constraints_to_ignore,
                "creative_reasoning": response,
                "agent": "muse"
            }
            
            logger.info(f"Brainstormed {len(result['solutions'])} unconstrained solutions")
            return result
            
        except Exception as e:
            logger.error(f"Solution brainstorming failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "solutions": [],
                "agent": "muse"
            }
    
    def ideate_breakthrough_approaches(self, current_approach: str, past_failures: List[str] = None) -> Dict[str, Any]:
        """
        Ideate completely different breakthrough approaches.
        
        Args:
            current_approach: Current approach being used
            past_failures: List of approaches that have failed
            
        Returns:
            Dictionary containing breakthrough approach ideas
        """
        try:
            past_failures = past_failures or []
            
            user_query = f"""
            Current approach: {current_approach}
            Past failed approaches: {', '.join(past_failures)}
            
            What is a completely different way to think about this problem?
            Ignore the current approach entirely. What if we approached this from
            a completely different angle? What paradigm shifts could we make?
            
            Think about:
            - Different problem framings
            - Analogies from other domains
            - Inverse approaches
            - Emergent solutions
            - Collaborative approaches
            
            Provide 3-5 breakthrough approaches that are fundamentally different.
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "breakthrough_approaches": self._parse_breakthrough_response(response),
                "paradigm_shifts": self._extract_paradigm_shifts(response),
                "creative_reasoning": response,
                "agent": "muse"
            }
            
            logger.info(f"Generated {len(result['breakthrough_approaches'])} breakthrough approaches")
            return result
            
        except Exception as e:
            logger.error(f"Breakthrough ideation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "breakthrough_approaches": [],
                "agent": "muse"
            }
    
    def _build_alternatives_query(self, problem_context: Dict[str, Any]) -> str:
        """Build query for generating alternatives."""
        problem = problem_context.get("problem", "")
        context = problem_context.get("context", "")
        goals = problem_context.get("goals", [])
        
        query = f"""
        Problem: {problem}
        Context: {context}
        Goals: {', '.join(goals) if goals else 'Not specified'}
        
        Generate creative alternatives that completely reimagine how to approach this.
        Don't worry about feasibility - focus on innovation and creativity.
        What are 5-7 completely different ways to solve this?
        """
        
        return query
    
    def _parse_alternatives_response(self, response: str) -> List[Dict[str, str]]:
        """Parse alternatives from LLM response."""
        alternatives = []
        lines = response.split('\n')
        
        current_alternative = None
        for line in lines:
            line = line.strip()
            if line and (line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.')) or 
                        line.startswith(('-', '*', '•'))):
                if current_alternative:
                    alternatives.append(current_alternative)
                current_alternative = {
                    "title": line.split('.', 1)[-1].strip() if '.' in line else line.strip(),
                    "description": ""
                }
            elif current_alternative and line:
                current_alternative["description"] += " " + line
        
        if current_alternative:
            alternatives.append(current_alternative)
        
        return alternatives
    
    def _parse_solutions_response(self, response: str) -> List[Dict[str, str]]:
        """Parse solutions from LLM response."""
        return self._parse_alternatives_response(response)  # Same parsing logic
    
    def _parse_breakthrough_response(self, response: str) -> List[Dict[str, str]]:
        """Parse breakthrough approaches from LLM response."""
        return self._parse_alternatives_response(response)  # Same parsing logic
    
    def _extract_paradigm_shifts(self, response: str) -> List[str]:
        """Extract paradigm shifts mentioned in the response."""
        paradigm_keywords = [
            "paradigm", "shift", "reframe", "different angle", "new perspective",
            "completely different", "breakthrough", "revolutionary", "innovative"
        ]
        
        shifts = []
        lines = response.lower().split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in paradigm_keywords):
                shifts.append(line.strip())
        
        return shifts[:5]  # Return top 5 paradigm shifts

    def execute_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task assigned to the Muse Agent.

        Args:
            task_data: Dictionary containing task information

        Returns:
            Dictionary containing the result of task execution
        """
        try:
            task_type = task_data.get("type", "generate_alternatives")

            if task_type == "generate_alternatives":
                return self.generate_alternatives(task_data.get("problem_context", {}))
            elif task_type == "brainstorm_solutions":
                return self.brainstorm_solutions(
                    task_data.get("problem", ""),
                    task_data.get("constraints_to_ignore", [])
                )
            elif task_type == "ideate_breakthrough":
                return self.ideate_breakthrough_approaches(
                    task_data.get("current_approach", ""),
                    task_data.get("past_failures", [])
                )
            else:
                # Default to generating alternatives
                return self.generate_alternatives(task_data.get("problem_context", {}))

        except Exception as e:
            logger.error(f"Muse task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent": "muse"
            }
