"""
Oracle Agent: Risk assessment and evidence-based analysis agent.
Analyzes proposals for potential failures and hidden dependencies.
"""

import logging
from typing import Dict, Any, List, Optional
from vibe_coder.agents.base_agent import BaseAgent
from vibe_coder.prompts.core_prompts import ORACLE_PROMPT

logger = logging.getLogger(__name__)


class OracleAgent(BaseAgent):
    """
    Agent for risk assessment and root cause analysis.
    
    The Oracle embodies the pessimist/realist role in the strategic dialectic,
    focusing on evidence-based analysis and identifying potential failure points.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Oracle Agent.
        
        Args:
            config: Configuration dictionary containing LLM settings
        """
        super().__init__(config, ORACLE_PROMPT)
        self.agent_name = "oracle_agent"
        logger.info("OracleAgent initialized for risk assessment")
    
    def assess_risks(self, proposal: Dict[str, Any], historical_data: List[Dict] = None) -> Dict[str, Any]:
        """
        Assess risks in a proposal based on historical evidence.
        
        Args:
            proposal: Dictionary containing proposal details
            historical_data: List of historical failure/success data
            
        Returns:
            Dictionary containing risk assessment and mitigation strategies
        """
        try:
            historical_data = historical_data or []
            
            user_query = self._build_risk_assessment_query(proposal, historical_data)
            
            # Get risk assessment from LLM
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "risk_level": self._extract_risk_level(response),
                "identified_risks": self._parse_risks(response),
                "failure_scenarios": self._extract_failure_scenarios(response),
                "mitigation_strategies": self._extract_mitigation_strategies(response),
                "evidence_basis": self._extract_evidence_basis(response),
                "assessment_reasoning": response,
                "agent": "oracle"
            }
            
            logger.info(f"Assessed {len(result['identified_risks'])} risks for proposal")
            return result
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "risk_level": "unknown",
                "identified_risks": [],
                "agent": "oracle"
            }
    
    def analyze_failure_patterns(self, past_failures: List[Dict]) -> Dict[str, Any]:
        """
        Analyze patterns in past failures to predict future risks.
        
        Args:
            past_failures: List of past failure records
            
        Returns:
            Dictionary containing failure pattern analysis
        """
        try:
            if not past_failures:
                return {
                    "success": True,
                    "patterns": [],
                    "predictions": [],
                    "message": "No historical failure data available",
                    "agent": "oracle"
                }
            
            user_query = self._build_pattern_analysis_query(past_failures)
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "failure_patterns": self._parse_failure_patterns(response),
                "root_causes": self._extract_root_causes(response),
                "predictive_indicators": self._extract_predictive_indicators(response),
                "pattern_analysis": response,
                "agent": "oracle"
            }
            
            logger.info(f"Analyzed {len(result['failure_patterns'])} failure patterns")
            return result
            
        except Exception as e:
            logger.error(f"Failure pattern analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "failure_patterns": [],
                "agent": "oracle"
            }
    
    def evaluate_proposal_viability(self, proposal: Dict[str, Any], constraints: List[str] = None) -> Dict[str, Any]:
        """
        Evaluate the viability of a proposal against known constraints.
        
        Args:
            proposal: Proposal to evaluate
            constraints: List of known constraints
            
        Returns:
            Dictionary containing viability assessment
        """
        try:
            constraints = constraints or []
            
            user_query = f"""
            Proposal: {proposal.get('description', '')}
            Details: {proposal.get('details', '')}
            Constraints: {', '.join(constraints)}
            
            Based on evidence and past experience, why might this proposal fail?
            What are the hidden dependencies and assumptions?
            What could go wrong that hasn't been considered?
            
            Provide a realistic assessment of:
            1. Technical feasibility
            2. Resource requirements
            3. Timeline viability
            4. Hidden dependencies
            5. Potential failure points
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "viability_score": self._extract_viability_score(response),
                "feasibility_concerns": self._parse_feasibility_concerns(response),
                "hidden_dependencies": self._extract_hidden_dependencies(response),
                "timeline_risks": self._extract_timeline_risks(response),
                "resource_concerns": self._extract_resource_concerns(response),
                "viability_reasoning": response,
                "agent": "oracle"
            }
            
            logger.info(f"Evaluated proposal viability: {result['viability_score']}/10")
            return result
            
        except Exception as e:
            logger.error(f"Viability evaluation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "viability_score": 0,
                "agent": "oracle"
            }
    
    def _build_risk_assessment_query(self, proposal: Dict[str, Any], historical_data: List[Dict]) -> str:
        """Build query for risk assessment."""
        proposal_desc = proposal.get('description', '')
        proposal_details = proposal.get('details', '')
        
        historical_context = ""
        if historical_data:
            historical_context = "\n\nHistorical context:\n"
            for i, data in enumerate(historical_data[:5]):  # Limit to 5 most recent
                historical_context += f"- {data.get('description', 'Unknown')}: {data.get('outcome', 'Unknown outcome')}\n"
        
        query = f"""
        Proposal to assess: {proposal_desc}
        Details: {proposal_details}
        {historical_context}
        
        Based on evidence and past experience, what are the risks?
        Why might this fail? What are the hidden assumptions?
        What evidence supports or contradicts this approach?
        
        Provide specific, evidence-based risk assessment.
        """
        
        return query
    
    def _build_pattern_analysis_query(self, past_failures: List[Dict]) -> str:
        """Build query for failure pattern analysis."""
        failures_summary = ""
        for i, failure in enumerate(past_failures[:10]):  # Limit to 10 most recent
            failures_summary += f"{i+1}. {failure.get('description', 'Unknown')}: {failure.get('reason', 'Unknown reason')}\n"
        
        query = f"""
        Past failures to analyze:
        {failures_summary}
        
        What patterns do you see in these failures?
        What are the common root causes?
        What early warning signs could predict similar failures?
        What systemic issues are revealed?
        
        Provide evidence-based pattern analysis.
        """
        
        return query
    
    def _extract_risk_level(self, response: str) -> str:
        """Extract overall risk level from response."""
        response_lower = response.lower()
        
        if any(word in response_lower for word in ['high risk', 'very risky', 'dangerous', 'likely to fail']):
            return "high"
        elif any(word in response_lower for word in ['medium risk', 'moderate risk', 'some concerns']):
            return "medium"
        elif any(word in response_lower for word in ['low risk', 'minimal risk', 'safe', 'likely to succeed']):
            return "low"
        else:
            return "medium"  # Default to medium if unclear
    
    def _parse_risks(self, response: str) -> List[Dict[str, str]]:
        """Parse identified risks from response."""
        risks = []
        lines = response.split('\n')
        
        current_risk = None
        for line in lines:
            line = line.strip()
            if line and (line.startswith(('1.', '2.', '3.', '4.', '5.')) or 
                        line.startswith(('-', '*', '•')) and 
                        any(keyword in line.lower() for keyword in ['risk', 'fail', 'problem', 'issue', 'concern'])):
                if current_risk:
                    risks.append(current_risk)
                current_risk = {
                    "risk": line.split('.', 1)[-1].strip() if '.' in line else line.strip(),
                    "description": ""
                }
            elif current_risk and line:
                current_risk["description"] += " " + line
        
        if current_risk:
            risks.append(current_risk)
        
        return risks
    
    def _extract_failure_scenarios(self, response: str) -> List[str]:
        """Extract failure scenarios from response."""
        scenarios = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['scenario', 'might fail', 'could fail', 'failure']):
                scenarios.append(line)
        
        return scenarios[:5]  # Return top 5 scenarios
    
    def _extract_mitigation_strategies(self, response: str) -> List[str]:
        """Extract mitigation strategies from response."""
        strategies = []
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['mitigate', 'prevent', 'avoid', 'reduce', 'strategy']):
                strategies.append(line)
        
        return strategies[:5]  # Return top 5 strategies
    
    def _extract_evidence_basis(self, response: str) -> List[str]:
        """Extract evidence basis from response."""
        evidence = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['evidence', 'data', 'history', 'past', 'experience']):
                evidence.append(line)

        return evidence[:3]  # Return top 3 evidence points

    def _parse_failure_patterns(self, response: str) -> List[Dict[str, str]]:
        """Parse failure patterns from response."""
        patterns = []
        lines = response.split('\n')

        current_pattern = None
        for line in lines:
            line = line.strip()
            if line and (line.startswith(('1.', '2.', '3.', '4.', '5.')) or
                        line.startswith(('-', '*', '•')) and
                        'pattern' in line.lower()):
                if current_pattern:
                    patterns.append(current_pattern)
                current_pattern = {
                    "pattern": line.split('.', 1)[-1].strip() if '.' in line else line.strip(),
                    "description": ""
                }
            elif current_pattern and line:
                current_pattern["description"] += " " + line

        if current_pattern:
            patterns.append(current_pattern)

        return patterns

    def _extract_root_causes(self, response: str) -> List[str]:
        """Extract root causes from response."""
        causes = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['root cause', 'underlying', 'fundamental', 'core issue']):
                causes.append(line)

        return causes[:5]

    def _extract_predictive_indicators(self, response: str) -> List[str]:
        """Extract predictive indicators from response."""
        indicators = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['indicator', 'warning', 'sign', 'predict', 'early']):
                indicators.append(line)

        return indicators[:5]

    def _extract_viability_score(self, response: str) -> int:
        """Extract viability score from response (0-10)."""
        import re

        # Look for explicit scores
        score_patterns = [
            r'(\d+)/10',
            r'score[:\s]*(\d+)',
            r'viability[:\s]*(\d+)',
            r'(\d+)\s*out\s*of\s*10'
        ]

        for pattern in score_patterns:
            match = re.search(pattern, response.lower())
            if match:
                score = int(match.group(1))
                return min(max(score, 0), 10)  # Clamp between 0-10

        # Fallback: analyze sentiment
        response_lower = response.lower()
        if any(word in response_lower for word in ['very low', 'extremely poor', 'impossible']):
            return 2
        elif any(word in response_lower for word in ['low', 'poor', 'unlikely']):
            return 4
        elif any(word in response_lower for word in ['moderate', 'medium', 'possible']):
            return 6
        elif any(word in response_lower for word in ['good', 'high', 'likely']):
            return 8
        elif any(word in response_lower for word in ['excellent', 'very high', 'certain']):
            return 10
        else:
            return 5  # Default neutral score

    def _parse_feasibility_concerns(self, response: str) -> List[str]:
        """Parse feasibility concerns from response."""
        concerns = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['feasibility', 'concern', 'challenge', 'difficult']):
                concerns.append(line)

        return concerns[:5]

    def _extract_hidden_dependencies(self, response: str) -> List[str]:
        """Extract hidden dependencies from response."""
        dependencies = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['dependency', 'depends on', 'requires', 'needs']):
                dependencies.append(line)

        return dependencies[:5]

    def _extract_timeline_risks(self, response: str) -> List[str]:
        """Extract timeline risks from response."""
        risks = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['timeline', 'schedule', 'time', 'deadline', 'delay']):
                risks.append(line)

        return risks[:3]

    def _extract_resource_concerns(self, response: str) -> List[str]:
        """Extract resource concerns from response."""
        concerns = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['resource', 'budget', 'cost', 'expensive', 'personnel']):
                concerns.append(line)

        return concerns[:3]

    def execute_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task assigned to the Oracle Agent.

        Args:
            task_data: Dictionary containing task information

        Returns:
            Dictionary containing the result of task execution
        """
        try:
            task_type = task_data.get("type", "assess_risks")

            if task_type == "assess_risks":
                return self.assess_risks(
                    task_data.get("proposal", {}),
                    task_data.get("historical_data", [])
                )
            elif task_type == "analyze_failure_patterns":
                return self.analyze_failure_patterns(task_data.get("past_failures", []))
            elif task_type == "evaluate_viability":
                return self.evaluate_proposal_viability(
                    task_data.get("proposal", {}),
                    task_data.get("constraints", [])
                )
            else:
                # Default to risk assessment
                return self.assess_risks(
                    task_data.get("proposal", {}),
                    task_data.get("historical_data", [])
                )

        except Exception as e:
            logger.error(f"Oracle task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent": "oracle"
            }
