"""
Strategic Debate: Orchestrates Muse vs Oracle dialectic.
Creates productive tension between creative ideation and risk assessment.
"""

import logging
from typing import Dict, Any, List, Optional
from vibe_coder.core.strategy.muse_agent import MuseAgent
from vibe_coder.core.strategy.oracle_agent import OracleAgent
from vibe_coder.core.strategy.wisdom_layer import WisdomLayer

logger = logging.getLogger(__name__)


class StrategicDebate:
    """
    Orchestrates strategic dialogue between Muse and Oracle.
    
    Creates productive tension between unconstrained creativity (Muse)
    and evidence-based risk assessment (Oracle), with wisdom synthesis
    from the Wisdom Layer.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Strategic Debate orchestrator.

        Args:
            config: Configuration dictionary for agents
        """
        self.config = config
        self.muse = MuseAgent(config)
        self.oracle = OracleAgent(config)
        self.wisdom_layer = WisdomLayer(config)
        self.technical_mode = False  # Flag for technical vs conceptual debate

        logger.info("StrategicDebate initialized with Muse, Oracle, and <PERSON> Layer")

    def set_technical_mode(self, technical: bool = True):
        """Set debate mode to technical (implementation) vs conceptual (ideation)."""
        self.technical_mode = technical

        # Set technical mode on all agents
        self.muse.set_technical_mode(technical)
        self.oracle.set_technical_mode(technical)
        self.wisdom_layer.set_technical_mode(technical)

        mode_name = "technical implementation" if technical else "conceptual ideation"
        logger.info(f"Strategic debate mode set to: {mode_name}")

    def conduct_debate(self, problem_context: Dict[str, Any], rounds: int = 3) -> Dict[str, Any]:
        """
        Conduct strategic debate between Muse and Oracle.
        
        Args:
            problem_context: Dictionary containing problem description and context
            rounds: Number of debate rounds to conduct
            
        Returns:
            Dictionary containing debate results and synthesis
        """
        try:
            logger.info(f"Starting strategic debate with {rounds} rounds")
            
            debate_history = []
            current_context = problem_context.copy()
            
            for round_num in range(rounds):
                logger.info(f"Conducting debate round {round_num + 1}")
                
                # Round structure: Muse proposes -> Oracle critiques -> Muse refines
                round_result = self._conduct_single_round(current_context, round_num + 1, debate_history)
                debate_history.append(round_result)
                
                # Update context with learnings from this round
                current_context = self._update_context_with_round_learnings(current_context, round_result)
            
            # Final synthesis by Wisdom Layer
            synthesis = self._synthesize_debate_results(debate_history, problem_context)
            
            result = {
                "success": True,
                "debate_rounds": debate_history,
                "final_synthesis": synthesis,
                "total_rounds": rounds,
                "problem_context": problem_context,
                "strategic_recommendation": synthesis.get("final_recommendation", ""),
                "debate_summary": self._create_debate_summary(debate_history, synthesis)
            }
            
            mode_name = "technical implementation" if self.technical_mode else "conceptual ideation"
            logger.info(f"Strategic debate ({mode_name}) completed successfully with {rounds} rounds")

            # Add mode information to result
            result["debate_mode"] = mode_name
            result["technical_mode"] = self.technical_mode

            return result
            
        except Exception as e:
            logger.error(f"Strategic debate failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "debate_rounds": debate_history if 'debate_history' in locals() else [],
                "total_rounds": 0
            }
    
    def _conduct_single_round(self, context: Dict[str, Any], round_num: int, previous_rounds: List[Dict]) -> Dict[str, Any]:
        """
        Conduct a single round of debate.
        
        Args:
            context: Current problem context
            round_num: Current round number
            previous_rounds: Results from previous rounds
            
        Returns:
            Dictionary containing round results
        """
        round_result = {
            "round_number": round_num,
            "muse_proposal": None,
            "oracle_assessment": None,
            "muse_refinement": None,
            "round_synthesis": None
        }
        
        # Phase 1: Muse generates creative proposal
        if round_num == 1:
            # First round: Generate initial alternatives (including build vs adapt considerations)
            enhanced_context = context.copy()
            enhanced_context.update({
                "consider_open_source": True,
                "build_vs_adapt_analysis": "Consider both building from scratch and adapting existing open-source solutions"
            })
            muse_result = self.muse.generate_alternatives(enhanced_context)
        else:
            # Subsequent rounds: Refine based on Oracle feedback
            previous_oracle_feedback = previous_rounds[-1].get("oracle_assessment", {})
            muse_result = self.muse.ideate_breakthrough_approaches(
                current_approach=context.get("current_approach", ""),
                past_failures=self._extract_past_failures(previous_oracle_feedback)
            )
        
        round_result["muse_proposal"] = muse_result
        
        # Phase 2: Oracle assesses risks and challenges
        oracle_result = self.oracle.assess_risks(
            proposal=muse_result,
            historical_data=self._extract_historical_data(previous_rounds)
        )
        round_result["oracle_assessment"] = oracle_result
        
        # Phase 3: Muse refines proposal based on Oracle feedback
        if oracle_result.get("success") and oracle_result.get("identified_risks"):
            refinement_context = {
                "original_proposal": muse_result,
                "oracle_concerns": oracle_result.get("identified_risks", []),
                "risk_level": oracle_result.get("risk_level", "medium")
            }
            
            muse_refinement = self.muse.brainstorm_solutions(
                problem=f"Address Oracle concerns: {', '.join([r.get('risk', '') for r in oracle_result.get('identified_risks', [])])}",
                constraints_to_ignore=["current implementation", "existing approach"]
            )
            round_result["muse_refinement"] = muse_refinement
        
        # Phase 4: Round synthesis
        round_synthesis = self._synthesize_round(round_result)
        round_result["round_synthesis"] = round_synthesis
        
        return round_result
    
    def _synthesize_round(self, round_result: Dict[str, Any]) -> Dict[str, Any]:
        """Synthesize results from a single round."""
        muse_proposal = round_result.get("muse_proposal", {})
        oracle_assessment = round_result.get("oracle_assessment", {})
        muse_refinement = round_result.get("muse_refinement", {})
        
        synthesis = {
            "creative_insights": self._extract_creative_insights(muse_proposal, muse_refinement),
            "risk_insights": self._extract_risk_insights(oracle_assessment),
            "tension_points": self._identify_tension_points(muse_proposal, oracle_assessment),
            "emerging_solutions": self._identify_emerging_solutions(muse_refinement),
            "round_learnings": self._extract_round_learnings(round_result)
        }
        
        return synthesis
    
    def _synthesize_debate_results(self, debate_history: List[Dict], original_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Synthesize final debate results using Wisdom Layer.
        
        Args:
            debate_history: Complete debate history
            original_context: Original problem context
            
        Returns:
            Dictionary containing final synthesis
        """
        # Prepare synthesis input for Wisdom Layer
        final_muse_proposal = debate_history[-1].get("muse_refinement", {}) if debate_history else {}
        final_oracle_assessment = debate_history[-1].get("oracle_assessment", {}) if debate_history else {}
        
        # Get wisdom synthesis
        wisdom_synthesis = self.wisdom_layer.synthesize_wisdom(
            muse_proposal=final_muse_proposal,
            oracle_assessment=final_oracle_assessment
        )
        
        # Compile comprehensive synthesis
        synthesis = {
            "wisdom_synthesis": wisdom_synthesis,
            "debate_evolution": self._trace_debate_evolution(debate_history),
            "creative_breakthrough": self._identify_creative_breakthrough(debate_history),
            "risk_mitigation_strategy": self._compile_risk_mitigation(debate_history),
            "balanced_recommendation": self._create_balanced_recommendation(debate_history, wisdom_synthesis),
            "implementation_guidance": self._create_implementation_guidance(debate_history),
            "final_recommendation": wisdom_synthesis.get("final_recommendation", "")
        }
        
        return synthesis
    
    def _update_context_with_round_learnings(self, context: Dict[str, Any], round_result: Dict[str, Any]) -> Dict[str, Any]:
        """Update context with learnings from the round."""
        updated_context = context.copy()
        
        # Add learnings from round synthesis
        round_synthesis = round_result.get("round_synthesis", {})
        if round_synthesis:
            updated_context["learnings"] = updated_context.get("learnings", [])
            updated_context["learnings"].extend(round_synthesis.get("round_learnings", []))
            
            # Update current approach based on emerging solutions
            emerging_solutions = round_synthesis.get("emerging_solutions", [])
            if emerging_solutions:
                updated_context["current_approach"] = emerging_solutions[0]  # Use best emerging solution
        
        return updated_context
    
    def _extract_past_failures(self, oracle_assessment: Dict[str, Any]) -> List[str]:
        """Extract past failures from Oracle assessment."""
        failures = []
        
        failure_scenarios = oracle_assessment.get("failure_scenarios", [])
        identified_risks = oracle_assessment.get("identified_risks", [])
        
        failures.extend(failure_scenarios)
        failures.extend([risk.get("risk", "") for risk in identified_risks])
        
        return failures[:5]  # Limit to top 5
    
    def _extract_historical_data(self, previous_rounds: List[Dict]) -> List[Dict]:
        """Extract historical data from previous rounds."""
        historical_data = []
        
        for round_data in previous_rounds:
            oracle_assessment = round_data.get("oracle_assessment", {})
            if oracle_assessment.get("success"):
                historical_data.append({
                    "description": f"Round {round_data.get('round_number', 0)} assessment",
                    "outcome": f"Risk level: {oracle_assessment.get('risk_level', 'unknown')}",
                    "risks": oracle_assessment.get("identified_risks", [])
                })
        
        return historical_data
    
    def _extract_creative_insights(self, muse_proposal: Dict[str, Any], muse_refinement: Dict[str, Any]) -> List[str]:
        """Extract creative insights from Muse contributions."""
        insights = []
        
        # From original proposal
        if muse_proposal.get("alternatives"):
            insights.extend([alt.get("title", "") for alt in muse_proposal["alternatives"]])
        
        # From refinement
        if muse_refinement.get("solutions"):
            insights.extend([sol.get("title", "") for sol in muse_refinement["solutions"]])
        
        return insights[:5]
    
    def _extract_risk_insights(self, oracle_assessment: Dict[str, Any]) -> List[str]:
        """Extract risk insights from Oracle assessment."""
        insights = []
        
        identified_risks = oracle_assessment.get("identified_risks", [])
        insights.extend([risk.get("risk", "") for risk in identified_risks])
        
        mitigation_strategies = oracle_assessment.get("mitigation_strategies", [])
        insights.extend(mitigation_strategies)
        
        return insights[:5]

    def _identify_tension_points(self, muse_proposal: Dict[str, Any], oracle_assessment: Dict[str, Any]) -> List[str]:
        """Identify tension points between creativity and risk assessment."""
        tensions = []

        # Compare creative alternatives with identified risks
        alternatives = muse_proposal.get("alternatives", [])
        risks = oracle_assessment.get("identified_risks", [])

        for alt in alternatives:
            alt_title = alt.get("title", "").lower()
            for risk in risks:
                risk_text = risk.get("risk", "").lower()
                # Simple keyword matching to identify tensions
                if any(word in risk_text for word in alt_title.split()):
                    tensions.append(f"Tension: {alt.get('title', '')} vs {risk.get('risk', '')}")

        return tensions[:3]

    def _identify_emerging_solutions(self, muse_refinement: Dict[str, Any]) -> List[str]:
        """Identify emerging solutions from Muse refinement."""
        solutions = []

        if muse_refinement.get("solutions"):
            solutions.extend([sol.get("title", "") for sol in muse_refinement["solutions"]])

        if muse_refinement.get("breakthrough_approaches"):
            solutions.extend([ba.get("title", "") for ba in muse_refinement["breakthrough_approaches"]])

        return solutions[:3]

    def _extract_round_learnings(self, round_result: Dict[str, Any]) -> List[str]:
        """Extract key learnings from a round."""
        learnings = []

        # From Muse
        muse_proposal = round_result.get("muse_proposal", {})
        if muse_proposal.get("paradigm_shifts"):
            learnings.extend(muse_proposal["paradigm_shifts"])

        # From Oracle
        oracle_assessment = round_result.get("oracle_assessment", {})
        if oracle_assessment.get("evidence_basis"):
            learnings.extend(oracle_assessment["evidence_basis"])

        return learnings[:3]

    def _trace_debate_evolution(self, debate_history: List[Dict]) -> Dict[str, Any]:
        """Trace how the debate evolved over rounds."""
        evolution = {
            "initial_creativity": [],
            "risk_awareness_growth": [],
            "solution_refinement": [],
            "convergence_points": []
        }

        for i, round_data in enumerate(debate_history):
            round_num = i + 1

            # Track creativity evolution
            muse_proposal = round_data.get("muse_proposal", {})
            if muse_proposal.get("alternatives"):
                evolution["initial_creativity"].append(f"Round {round_num}: {len(muse_proposal['alternatives'])} alternatives")

            # Track risk awareness
            oracle_assessment = round_data.get("oracle_assessment", {})
            if oracle_assessment.get("identified_risks"):
                evolution["risk_awareness_growth"].append(f"Round {round_num}: {len(oracle_assessment['identified_risks'])} risks identified")

            # Track solution refinement
            muse_refinement = round_data.get("muse_refinement", {})
            if muse_refinement.get("solutions"):
                evolution["solution_refinement"].append(f"Round {round_num}: {len(muse_refinement['solutions'])} refined solutions")

        return evolution

    def _identify_creative_breakthrough(self, debate_history: List[Dict]) -> Dict[str, Any]:
        """Identify the most significant creative breakthrough."""
        breakthroughs = []

        for round_data in debate_history:
            muse_proposal = round_data.get("muse_proposal", {})
            muse_refinement = round_data.get("muse_refinement", {})

            # Look for breakthrough approaches
            if muse_proposal.get("breakthrough_approaches"):
                breakthroughs.extend(muse_proposal["breakthrough_approaches"])

            if muse_refinement.get("breakthrough_approaches"):
                breakthroughs.extend(muse_refinement["breakthrough_approaches"])

        # Return the most comprehensive breakthrough
        if breakthroughs:
            best_breakthrough = max(breakthroughs, key=lambda x: len(x.get("description", "")))
            return {
                "breakthrough": best_breakthrough,
                "total_breakthroughs": len(breakthroughs),
                "breakthrough_evolution": breakthroughs
            }

        return {"breakthrough": None, "total_breakthroughs": 0}

    def _compile_risk_mitigation(self, debate_history: List[Dict]) -> Dict[str, Any]:
        """Compile comprehensive risk mitigation strategy."""
        all_risks = []
        all_mitigations = []

        for round_data in debate_history:
            oracle_assessment = round_data.get("oracle_assessment", {})

            if oracle_assessment.get("identified_risks"):
                all_risks.extend(oracle_assessment["identified_risks"])

            if oracle_assessment.get("mitigation_strategies"):
                all_mitigations.extend(oracle_assessment["mitigation_strategies"])

        # Deduplicate and prioritize
        unique_risks = self._deduplicate_risks(all_risks)
        unique_mitigations = list(set(all_mitigations))

        return {
            "total_risks_identified": len(all_risks),
            "unique_risks": unique_risks,
            "mitigation_strategies": unique_mitigations[:5],
            "risk_coverage": len(unique_mitigations) / max(len(unique_risks), 1)
        }

    def _create_balanced_recommendation(self, debate_history: List[Dict], wisdom_synthesis: Dict[str, Any]) -> str:
        """Create a balanced recommendation incorporating all perspectives."""
        if not debate_history:
            return "No debate history available for recommendation"

        final_round = debate_history[-1]
        muse_refinement = final_round.get("muse_refinement", {})
        oracle_assessment = final_round.get("oracle_assessment", {})

        # Extract key elements
        creative_solutions = muse_refinement.get("solutions", [])
        risk_level = oracle_assessment.get("risk_level", "medium")
        wisdom_recommendation = wisdom_synthesis.get("final_recommendation", "")

        # Create balanced recommendation
        if creative_solutions and risk_level == "low":
            return f"Proceed with creative solution: {creative_solutions[0].get('title', '')} - Low risk profile supports innovation"
        elif creative_solutions and risk_level == "medium":
            return f"Cautiously implement: {creative_solutions[0].get('title', '')} with risk mitigation - {wisdom_recommendation}"
        elif risk_level == "high":
            return f"High risk identified - Consider alternative approach or additional risk mitigation before proceeding"
        else:
            return wisdom_recommendation or "Continue strategic analysis needed"

    def _create_implementation_guidance(self, debate_history: List[Dict]) -> Dict[str, Any]:
        """Create implementation guidance based on debate results."""
        guidance = {
            "recommended_approach": "incremental",
            "key_success_factors": [],
            "critical_risks_to_monitor": [],
            "decision_checkpoints": [],
            "fallback_strategies": []
        }

        if not debate_history:
            return guidance

        # Extract guidance from final round
        final_round = debate_history[-1]
        oracle_assessment = final_round.get("oracle_assessment", {})

        # Set approach based on risk level
        risk_level = oracle_assessment.get("risk_level", "medium")
        if risk_level == "high":
            guidance["recommended_approach"] = "pilot_first"
        elif risk_level == "low":
            guidance["recommended_approach"] = "full_implementation"

        # Extract key factors
        if oracle_assessment.get("identified_risks"):
            guidance["critical_risks_to_monitor"] = [
                risk.get("risk", "") for risk in oracle_assessment["identified_risks"][:3]
            ]

        if oracle_assessment.get("mitigation_strategies"):
            guidance["key_success_factors"] = oracle_assessment["mitigation_strategies"][:3]

        return guidance

    def _create_debate_summary(self, debate_history: List[Dict], synthesis: Dict[str, Any]) -> str:
        """Create a concise summary of the entire debate."""
        if not debate_history:
            return "No debate conducted"

        total_rounds = len(debate_history)
        final_recommendation = synthesis.get("final_recommendation", "No clear recommendation")

        # Count total alternatives and risks
        total_alternatives = sum(
            len(round_data.get("muse_proposal", {}).get("alternatives", []))
            for round_data in debate_history
        )

        total_risks = sum(
            len(round_data.get("oracle_assessment", {}).get("identified_risks", []))
            for round_data in debate_history
        )

        summary = f"""
        Strategic Debate Summary:
        - {total_rounds} rounds of Muse vs Oracle dialogue
        - {total_alternatives} creative alternatives explored
        - {total_risks} risks identified and assessed
        - Final recommendation: {final_recommendation}
        """

        return summary.strip()

    def _deduplicate_risks(self, risks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate risks based on similarity."""
        unique_risks = []
        seen_risks = set()

        for risk in risks:
            risk_text = risk.get("risk", "").lower()
            # Simple deduplication based on first few words
            risk_key = " ".join(risk_text.split()[:3])

            if risk_key not in seen_risks:
                seen_risks.add(risk_key)
                unique_risks.append(risk)

        return unique_risks
