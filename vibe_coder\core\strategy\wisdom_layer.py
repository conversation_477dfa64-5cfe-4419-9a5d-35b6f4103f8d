"""
Wisdom Layer: Ethical governance and strategic alignment agent.
Ensures proposals align with long-term goals and ethical considerations.
"""

import logging
from typing import Dict, Any, List, Optional
from vibe_coder.agents.base_agent import BaseAgent
from vibe_coder.prompts.core_prompts import WISDOM_LAYER_PROMPT

logger = logging.getLogger(__name__)


class WisdomLayer(BaseAgent):
    """
    Agent for ethical governance and strategic alignment.
    
    The Wisdom Layer focuses on strategic and ethical governance, ensuring
    proposals align with long-term project goals and considering unintended
    consequences.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Wisdom Layer.
        
        Args:
            config: Configuration dictionary containing LLM settings
        """
        super().__init__(config, WISDOM_LAYER_PROMPT)
        self.agent_name = "wisdom_layer"
        logger.info("WisdomLayer initialized for ethical governance")
    
    def evaluate_alignment(self, proposal: Dict[str, Any], project_goals: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate if proposal aligns with long-term project goals.
        
        Args:
            proposal: Dictionary containing proposal details
            project_goals: Dictionary containing project goals and values
            
        Returns:
            Dictionary containing alignment evaluation
        """
        try:
            user_query = self._build_alignment_query(proposal, project_goals)
            
            # Get alignment evaluation from LLM
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "alignment_score": self._extract_alignment_score(response),
                "alignment_analysis": self._parse_alignment_analysis(response),
                "goal_conflicts": self._extract_goal_conflicts(response),
                "strategic_recommendations": self._extract_strategic_recommendations(response),
                "long_term_implications": self._extract_long_term_implications(response),
                "wisdom_reasoning": response,
                "agent": "wisdom_layer"
            }
            
            logger.info(f"Evaluated alignment: {result['alignment_score']}/10")
            return result
            
        except Exception as e:
            logger.error(f"Alignment evaluation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "alignment_score": 0,
                "agent": "wisdom_layer"
            }
    
    def assess_ethical_implications(self, proposal: Dict[str, Any], stakeholders: List[str] = None) -> Dict[str, Any]:
        """
        Assess ethical implications and unintended consequences.
        
        Args:
            proposal: Proposal to assess
            stakeholders: List of stakeholders who might be affected
            
        Returns:
            Dictionary containing ethical assessment
        """
        try:
            stakeholders = stakeholders or []
            
            user_query = f"""
            Proposal: {proposal.get('description', '')}
            Details: {proposal.get('details', '')}
            Affected stakeholders: {', '.join(stakeholders)}
            
            What are the ethical implications of this proposal?
            What unintended negative consequences might arise?
            How might this affect different stakeholders?
            What are the long-term societal impacts?
            
            Consider:
            - Fairness and equity
            - Privacy and security
            - Environmental impact
            - Social responsibility
            - Unintended consequences
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "ethical_score": self._extract_ethical_score(response),
                "ethical_concerns": self._parse_ethical_concerns(response),
                "stakeholder_impacts": self._extract_stakeholder_impacts(response),
                "unintended_consequences": self._extract_unintended_consequences(response),
                "mitigation_recommendations": self._extract_mitigation_recommendations(response),
                "ethical_reasoning": response,
                "agent": "wisdom_layer"
            }
            
            logger.info(f"Assessed ethical implications: {result['ethical_score']}/10")
            return result
            
        except Exception as e:
            logger.error(f"Ethical assessment failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "ethical_score": 0,
                "agent": "wisdom_layer"
            }
    
    def evaluate_strategic_coherence(self, proposal: Dict[str, Any], existing_strategy: Dict[str, Any]) -> Dict[str, Any]:
        """
        Evaluate how well a proposal fits with existing strategic direction.
        
        Args:
            proposal: Proposal to evaluate
            existing_strategy: Current strategic direction and priorities
            
        Returns:
            Dictionary containing strategic coherence evaluation
        """
        try:
            user_query = f"""
            Proposal: {proposal.get('description', '')}
            Current strategy: {existing_strategy.get('description', '')}
            Strategic priorities: {', '.join(existing_strategy.get('priorities', []))}
            
            How well does this proposal fit with our existing strategic direction?
            Does it strengthen or weaken our strategic position?
            What are the strategic trade-offs?
            How does this affect our long-term competitive advantage?
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "coherence_score": self._extract_coherence_score(response),
                "strategic_fit": self._parse_strategic_fit(response),
                "trade_offs": self._extract_trade_offs(response),
                "competitive_impact": self._extract_competitive_impact(response),
                "strategic_recommendations": self._extract_strategic_recommendations(response),
                "coherence_reasoning": response,
                "agent": "wisdom_layer"
            }
            
            logger.info(f"Evaluated strategic coherence: {result['coherence_score']}/10")
            return result
            
        except Exception as e:
            logger.error(f"Strategic coherence evaluation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "coherence_score": 0,
                "agent": "wisdom_layer"
            }
    
    def synthesize_wisdom(self, muse_proposal: Dict[str, Any], oracle_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """
        Synthesize wisdom from Muse proposal and Oracle assessment.
        
        Args:
            muse_proposal: Creative proposal from Muse
            oracle_assessment: Risk assessment from Oracle
            
        Returns:
            Dictionary containing synthesized wisdom and recommendations
        """
        try:
            user_query = f"""
            Creative Proposal (Muse): {muse_proposal.get('creative_reasoning', '')}
            Risk Assessment (Oracle): {oracle_assessment.get('assessment_reasoning', '')}
            
            Synthesize these perspectives into wise guidance.
            How can we balance innovation with prudence?
            What is the wise path forward that honors both creativity and caution?
            What governance principles should guide our decision?
            """
            
            response = self._invoke_llm(user_query)
            
            result = {
                "success": True,
                "wisdom_synthesis": response,
                "balanced_approach": self._extract_balanced_approach(response),
                "governance_principles": self._extract_governance_principles(response),
                "decision_framework": self._extract_decision_framework(response),
                "final_recommendation": self._extract_final_recommendation(response),
                "agent": "wisdom_layer"
            }
            
            logger.info("Synthesized wisdom from Muse and Oracle perspectives")
            return result
            
        except Exception as e:
            logger.error(f"Wisdom synthesis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent": "wisdom_layer"
            }
    
    def _build_alignment_query(self, proposal: Dict[str, Any], project_goals: Dict[str, Any]) -> str:
        """Build query for alignment evaluation."""
        proposal_desc = proposal.get('description', '')
        proposal_details = proposal.get('details', '')
        
        goals = project_goals.get('goals', [])
        values = project_goals.get('values', [])
        vision = project_goals.get('vision', '')
        
        query = f"""
        Proposal: {proposal_desc}
        Details: {proposal_details}
        
        Project Goals: {', '.join(goals) if goals else 'Not specified'}
        Project Values: {', '.join(values) if values else 'Not specified'}
        Project Vision: {vision}
        
        How well does this proposal align with our long-term project goals?
        Does it support or conflict with our stated values and vision?
        What are the strategic implications for our long-term success?
        """
        
        return query
    
    def _extract_alignment_score(self, response: str) -> int:
        """Extract alignment score from response (0-10)."""
        return self._extract_score_from_response(response, ['alignment', 'fit', 'match'])
    
    def _extract_ethical_score(self, response: str) -> int:
        """Extract ethical score from response (0-10)."""
        return self._extract_score_from_response(response, ['ethical', 'moral', 'responsible'])
    
    def _extract_coherence_score(self, response: str) -> int:
        """Extract coherence score from response (0-10)."""
        return self._extract_score_from_response(response, ['coherence', 'fit', 'consistency'])
    
    def _extract_score_from_response(self, response: str, keywords: List[str]) -> int:
        """Extract score from response based on keywords."""
        import re
        
        # Look for explicit scores
        score_patterns = [
            r'(\d+)/10',
            r'score[:\s]*(\d+)',
            r'(\d+)\s*out\s*of\s*10'
        ]
        
        for pattern in score_patterns:
            match = re.search(pattern, response.lower())
            if match:
                score = int(match.group(1))
                return min(max(score, 0), 10)
        
        # Fallback: analyze sentiment
        response_lower = response.lower()
        if any(word in response_lower for word in ['excellent', 'perfect', 'outstanding']):
            return 9
        elif any(word in response_lower for word in ['good', 'strong', 'well']):
            return 7
        elif any(word in response_lower for word in ['moderate', 'adequate', 'reasonable']):
            return 5
        elif any(word in response_lower for word in ['poor', 'weak', 'concerning']):
            return 3
        elif any(word in response_lower for word in ['terrible', 'awful', 'unacceptable']):
            return 1
        else:
            return 5  # Default neutral score

    def _parse_alignment_analysis(self, response: str) -> List[str]:
        """Parse alignment analysis from response."""
        analysis = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['align', 'support', 'conflict', 'goal']):
                analysis.append(line)

        return analysis[:5]

    def _extract_goal_conflicts(self, response: str) -> List[str]:
        """Extract goal conflicts from response."""
        conflicts = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['conflict', 'contradict', 'oppose', 'against']):
                conflicts.append(line)

        return conflicts[:3]

    def _extract_strategic_recommendations(self, response: str) -> List[str]:
        """Extract strategic recommendations from response."""
        recommendations = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['recommend', 'suggest', 'should', 'consider']):
                recommendations.append(line)

        return recommendations[:5]

    def _extract_long_term_implications(self, response: str) -> List[str]:
        """Extract long-term implications from response."""
        implications = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['long-term', 'future', 'implication', 'consequence']):
                implications.append(line)

        return implications[:3]

    def _parse_ethical_concerns(self, response: str) -> List[str]:
        """Parse ethical concerns from response."""
        concerns = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['ethical', 'moral', 'concern', 'issue']):
                concerns.append(line)

        return concerns[:5]

    def _extract_stakeholder_impacts(self, response: str) -> List[str]:
        """Extract stakeholder impacts from response."""
        impacts = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['stakeholder', 'affect', 'impact', 'user']):
                impacts.append(line)

        return impacts[:5]

    def _extract_unintended_consequences(self, response: str) -> List[str]:
        """Extract unintended consequences from response."""
        consequences = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['unintended', 'unexpected', 'side effect', 'consequence']):
                consequences.append(line)

        return consequences[:3]

    def _extract_mitigation_recommendations(self, response: str) -> List[str]:
        """Extract mitigation recommendations from response."""
        recommendations = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['mitigate', 'prevent', 'address', 'solution']):
                recommendations.append(line)

        return recommendations[:5]

    def _parse_strategic_fit(self, response: str) -> List[str]:
        """Parse strategic fit analysis from response."""
        fit_analysis = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['fit', 'strategic', 'position', 'advantage']):
                fit_analysis.append(line)

        return fit_analysis[:5]

    def _extract_trade_offs(self, response: str) -> List[str]:
        """Extract trade-offs from response."""
        trade_offs = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['trade-off', 'trade off', 'sacrifice', 'cost']):
                trade_offs.append(line)

        return trade_offs[:3]

    def _extract_competitive_impact(self, response: str) -> List[str]:
        """Extract competitive impact from response."""
        impacts = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['competitive', 'advantage', 'market', 'position']):
                impacts.append(line)

        return impacts[:3]

    def _extract_balanced_approach(self, response: str) -> List[str]:
        """Extract balanced approach from response."""
        approaches = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['balance', 'middle', 'compromise', 'both']):
                approaches.append(line)

        return approaches[:3]

    def _extract_governance_principles(self, response: str) -> List[str]:
        """Extract governance principles from response."""
        principles = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['principle', 'governance', 'guide', 'rule']):
                principles.append(line)

        return principles[:5]

    def _extract_decision_framework(self, response: str) -> List[str]:
        """Extract decision framework from response."""
        framework = []
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['framework', 'decision', 'criteria', 'process']):
                framework.append(line)

        return framework[:3]

    def _extract_final_recommendation(self, response: str) -> str:
        """Extract final recommendation from response."""
        lines = response.split('\n')

        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['recommend', 'final', 'conclusion', 'decision']):
                return line

        # Fallback: return last substantial line
        for line in reversed(lines):
            line = line.strip()
            if len(line) > 20:  # Substantial content
                return line

        return "No clear recommendation found"

    def execute_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task assigned to the Wisdom Layer.

        Args:
            task_data: Dictionary containing task information

        Returns:
            Dictionary containing the result of task execution
        """
        try:
            task_type = task_data.get("type", "evaluate_alignment")

            if task_type == "evaluate_alignment":
                return self.evaluate_alignment(
                    task_data.get("proposal", {}),
                    task_data.get("project_goals", {})
                )
            elif task_type == "assess_ethical":
                return self.assess_ethical_implications(
                    task_data.get("proposal", {}),
                    task_data.get("stakeholders", [])
                )
            elif task_type == "evaluate_coherence":
                return self.evaluate_strategic_coherence(
                    task_data.get("proposal", {}),
                    task_data.get("existing_strategy", {})
                )
            elif task_type == "synthesize_wisdom":
                return self.synthesize_wisdom(
                    task_data.get("muse_proposal", {}),
                    task_data.get("oracle_assessment", {})
                )
            else:
                # Default to alignment evaluation
                return self.evaluate_alignment(
                    task_data.get("proposal", {}),
                    task_data.get("project_goals", {})
                )

        except Exception as e:
            logger.error(f"Wisdom Layer task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent": "wisdom_layer"
            }
