from typing import List, Dict, Any, Optional
from enum import Enum
import warnings

from pydantic import BaseModel, Field


class TaskIntent(str, Enum):
    """
    Defines the high-level intent of a task.
    This helps the system understand what kind of action is expected.
    """
    EXECUTE_TOOL = "EXECUTE_TOOL"
    VERIFY_STATE = "VERIFY_STATE"
    PLAN_SUB_TASKS = "PLAN_SUB_TASKS"
    REQUEST_CLARIFICATION = "REQUEST_CLARIFICATION"
    # Add more intents as the system evolves


class TaskStatus(str, Enum):
    """
    Represents the current status of a task.
    """
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    WAITING_FOR_DEPENDENCY = "WAITING_FOR_DEPENDENCY"
    WAITING_FOR_CLARIFICATION = "WAITING_FOR_CLARIFICATION"


class TaskNode(BaseModel):
    """
    Represents a single node in the task graph.
    Each node is a unit of work to be performed.
    """
    id: str = Field(..., description="Unique identifier for the task node.")
    description: str = Field(..., description="Human-readable description of the task.")
    intent: TaskIntent = Field(..., description="The primary intent or goal of this task.")
    assigned_agent: str = Field(..., description="The name or type of agent responsible for this task (e.g., 'Coder', 'Verifier').")
    status: TaskStatus = Field(TaskStatus.PENDING, description="Current status of the task.")
    dependencies: List[str] = Field(default_factory=list, description="List of task IDs that must be completed before this task can start.")
    tool_name: Optional[str] = Field(None, description="If intent is EXECUTE_TOOL, the name of the tool to be used.")
    tool_arguments: Dict[str, Any] = Field(default_factory=dict, description="Arguments for the tool, if applicable.")
    result: Optional[Any] = Field(None, description="The output or result of the task execution.")
    error_message: Optional[str] = Field(None, description="Error message if the task failed.")
    # Future fields: priority, estimated_effort, sub_tasks


class TaskEdge(BaseModel):
    """
    Represents an edge (dependency) between two tasks in the task graph.
    """
    source: str = Field(..., description="ID of the source task node.")
    target: str = Field(..., description="ID of the target task node.")


class TaskGraph(BaseModel):
    """
    DEPRECATED: Use TaskGraphDefinition instead.

    Represents a task graph with explicit nodes and edges.
    This format is deprecated in favor of TaskGraphDefinition which uses
    implicit dependencies within TaskNode objects for LangGraph compatibility.
    """
    startNode: str = Field(..., description="The ID of the first task to be executed.")
    nodes: List[TaskNode] = Field(..., description="A list of all task nodes in the graph.")
    edges: List[TaskEdge] = Field(default_factory=list, description="A list of edges defining task dependencies.")

    def __init__(self, **data):
        warnings.warn(
            "TaskGraph is deprecated. Use TaskGraphDefinition instead for LangGraph compatibility.",
            DeprecationWarning,
            stacklevel=2
        )
        super().__init__(**data)

    # Helper method to easily find a node by its ID
    def get_node_by_id(self, node_id: str) -> Optional[TaskNode]:
        for node in self.nodes:
            if node.id == node_id:
                return node
        return None


class TaskGraphDefinition(BaseModel):
    """
    Defines the structure of the task graph, including nodes and their relationships.

    This is the preferred format for LangGraph-based workflows. Dependencies are
    defined within each TaskNode, creating a clean DAG structure that's compatible
    with sophisticated workflow orchestration.

    Generated by the TaskDecomposerAgent and consumed by the WorkflowManager.
    """
    start_node_id: str = Field(..., description="The ID of the first task to be executed in this graph or subgraph.")
    nodes: List[TaskNode] = Field(..., description="A list of all task nodes in the graph.")
    # Edges are implicitly defined by TaskNode.dependencies for a directed acyclic graph (DAG)
    # This design is optimized for LangGraph workflows and eliminates redundancy

    # Helper method to easily find a node by its ID
    def get_node_by_id(self, node_id: str) -> Optional[TaskNode]:
        for node in self.nodes:
            if node.id == node_id:
                return node
        return None

    def get_ready_tasks(self, completed_task_ids: set) -> List[TaskNode]:
        """
        Get all tasks that are ready to execute (dependencies met).

        Args:
            completed_task_ids: Set of task IDs that have been completed

        Returns:
            List of TaskNode objects ready for execution
        """
        ready_tasks = []
        for node in self.nodes:
            if node.status == TaskStatus.PENDING:
                if all(dep_id in completed_task_ids for dep_id in node.dependencies):
                    ready_tasks.append(node)
        return ready_tasks

