import json
import logging
from typing import Dict, Any, Optional, List, Callable

from langgraph.graph import Graph as CompiledGraph # Renamed for clarity

from vibe_coder.data_models.task_models import TaskGraphDefinition, TaskNode, TaskStatus, TaskIntent
from vibe_coder.engine.state import AgentState
# from vibe_coder.ui.agent_controller import Agent<PERSON><PERSON>roller # Forward reference

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - DRIVER - %(message)s')
logger = logging.getLogger(__name__)

# Placeholder for the AgentController type hint until it's defined
AgentController = Any 

class Driver:
    """
    Manages the step-by-step execution of a task graph defined by TaskGraphDefinition.
    It orchestrates the execution of individual tasks, using a compiled LangGraph
    (agent_workflow_graph) for the internal cognitive loop of agents performing those tasks.
    """
    def __init__(self, 
                 task_graph_definition: TaskGraphDefinition, 
                 agent_workflow_graph: CompiledGraph,
                 signals_controller: Optional[AgentController] = None,
                 project_state_file: str = "./data/project_state.json"):
        self.task_graph_definition: TaskGraphDefinition = task_graph_definition
        self.agent_workflow_graph: CompiledGraph = agent_workflow_graph
        self.signals_controller: Optional[AgentController] = signals_controller
        self.project_state_file: str = project_state_file
        self.current_task_id: Optional[str] = None
        self._task_status_map: Dict[str, TaskStatus] = {
            node.id: node.status for node in self.task_graph_definition.nodes
        }

    def _log_to_ui(self, message: str):
        logger.info(message)
        if self.signals_controller and hasattr(self.signals_controller, 'log_signal'):
            self.signals_controller.log_signal.emit(f"DRIVER: {message}")
        else:
            print(f"UI_LOG (Driver): {message}") # Fallback if no controller

    def _update_task_status(self, task_id: str, status: TaskStatus, error_message: Optional[str] = None, result: Optional[Any] = None):
        task_node = self.task_graph_definition.get_node_by_id(task_id)
        if task_node:
            task_node.status = status
            self._task_status_map[task_id] = status
            if error_message:
                task_node.error_message = error_message
            if result:
                task_node.result = result # Store the final result on the task node
            self._log_to_ui(f"Task '{task_id}' ({task_node.description[:30]}...) status updated to {status}.")
            # TODO: Persist this change if project_state.json is used for full task graph state
        else:
            self._log_to_ui(f"Warning: Could not find task '{task_id}' to update status.")

    def _get_next_task(self) -> Optional[TaskNode]:
        # Simple sequential execution based on initial order, respecting dependencies.
        # More sophisticated scheduling (e.g., topological sort) could be added.
        
        # Start with the designated start_node_id if no task is current or completed
        if not self.current_task_id and self.task_graph_definition.start_node_id:
            start_node = self.task_graph_definition.get_node_by_id(self.task_graph_definition.start_node_id)
            # Only consider the start_node if it's PENDING and its dependencies are met
            if start_node and \
               self._task_status_map.get(start_node.id) == TaskStatus.PENDING and \
               self._are_dependencies_met(start_node):
                return start_node # Return the start_node if it's genuinely the next to run

        for task_node in self.task_graph_definition.nodes:
            if self._task_status_map.get(task_node.id) == TaskStatus.PENDING:
                if self._are_dependencies_met(task_node):
                    return task_node
        return None

    def _are_dependencies_met(self, task_node: TaskNode) -> bool:
        for dep_id in task_node.dependencies:
            if self._task_status_map.get(dep_id) != TaskStatus.COMPLETED:
                # self._log_to_ui(f"Task '{task_node.id}' waiting for dependency '{dep_id}' (status: {self._task_status_map.get(dep_id)}).")
                return False
        return True

    def _prepare_initial_state_for_task(self, task_node: TaskNode) -> AgentState:
        """Prepares the initial AgentState for invoking the agent_workflow_graph."""
        self._log_to_ui(f"Preparing initial state for task: {task_node.id} - {task_node.description}")
        
        # The input_query for the agent_workflow_graph could be the task description,
        # or a more specific instruction derived from the task's intent and properties.
        input_query = f"Execute task: {task_node.description}."
        if task_node.intent == TaskIntent.EXECUTE_TOOL and task_node.tool_name:
            input_query = f"Use tool '{task_node.tool_name}' with arguments {task_node.tool_arguments} to achieve: {task_node.description}"
        elif task_node.intent == TaskIntent.VERIFY_STATE:
            input_query = f"Verify state related to: {task_node.description}."
            # Potentially add more context for verification tasks

        initial_state: AgentState = {
            "current_task_id": task_node.id,
            "current_task_description": task_node.description,
            "current_task_intent": task_node.intent.value, # Pass enum value
            "current_task_assigned_agent": task_node.assigned_agent,
            "current_task_tool_name": task_node.tool_name,
            "current_task_tool_arguments": task_node.tool_arguments.copy() if task_node.tool_arguments else {},
            "input_query": input_query,
            "history": [], # Initialize history for the agent workflow
            "is_final_answer": False, # Agent workflow will set this
        }
        return initial_state

    def run_main_loop(self):
        self._log_to_ui("Starting main execution loop.")
        
        # Load state if available (basic implementation)
        # self.load_project_state() # TODO: Implement robust state loading

        while True:
            next_task = self._get_next_task()
            if not next_task:
                self._log_to_ui("No more pending tasks with met dependencies. Execution finished or stalled.")
                break

            self.current_task_id = next_task.id
            self._update_task_status(next_task.id, TaskStatus.IN_PROGRESS)
            self._log_to_ui(f"Executing task: {next_task.id} - {next_task.description}")

            initial_agent_state = self._prepare_initial_state_for_task(next_task)
            
            # Configuration for the agent_workflow_graph invocation (e.g., for checkpointing)
            # Each task execution can be a new "thread" or "conversation" for the agent_workflow_graph
            thread_id = f"task_{next_task.id}_attempt_{sum(1 for node_id, status in self._task_status_map.items() if node_id == next_task.id and status != TaskStatus.PENDING)}"
            config = {"configurable": {"thread_id": thread_id}}

            final_task_output: Optional[Any] = None
            task_error_message: Optional[str] = None

            try:
                for i, step_state_dict in enumerate(self.agent_workflow_graph.stream(initial_agent_state, config=config)):
                    # The key in step_state_dict is the name of the node that just ran
                    # The value is the AgentState after that node's execution
                    node_name = list(step_state_dict.keys())[0]
                    current_agent_state: AgentState = step_state_dict[node_name]
                    
                    self._log_to_ui(f"Task '{next_task.id}', Step {i+1} ({node_name}): Thought - {current_agent_state.get('thought_process', 'N/A')}")
                    
                    if current_agent_state.get('tool_call_request_name'):
                        self._log_to_ui(f"  Tool requested: {current_agent_state['tool_call_request_name']} with args {current_agent_state.get('tool_call_request_args')}")
                    if current_agent_state.get('tool_invocation_result'):
                        self._log_to_ui(f"  Tool result: {current_agent_state['tool_invocation_result'][:200]}") # Log snippet
                    if current_agent_state.get('error_message'):
                        self._log_to_ui(f"  Error in agent workflow: {current_agent_state['error_message']}")
                        # This error is internal to the agent_workflow_graph.
                        # It might be handled by a reflector within that graph.

                    if current_agent_state.get("is_final_answer", False):
                        self._log_to_ui(f"Task '{next_task.id}' marked as final by agent workflow.")
                        final_task_output = current_agent_state.get("final_output")
                        if current_agent_state.get("error_message"): # If workflow ended with an error
                            task_error_message = current_agent_state.get("error_message")
                        break 
                    # Add a safety break for very long internal loops if necessary,
                    # though LangGraph's structure should prevent most infinite loops if designed well.
                    if i > 50: # Safety break for agent_workflow_graph stream
                        self._log_to_ui(f"Warning: Task '{next_task.id}' agent workflow exceeded 50 steps. Breaking.")
                        task_error_message = "Agent workflow exceeded maximum steps."
                        break
                
                if task_error_message: # If loop ended due to error or safety break
                    self._update_task_status(next_task.id, TaskStatus.FAILED, error_message=task_error_message, result=final_task_output)
                else: # If loop completed normally (is_final_answer was True)
                    self._update_task_status(next_task.id, TaskStatus.COMPLETED, result=final_task_output)

            except Exception as e:
                logger.exception(f"Critical error during task '{next_task.id}' execution in agent_workflow_graph.")
                self._update_task_status(next_task.id, TaskStatus.FAILED, error_message=f"Driver-level exception: {str(e)}")
            
            # self.save_project_state() # TODO: Implement robust state saving
            self.current_task_id = None # Reset for the next iteration of the main loop

        self._log_to_ui("Main execution loop finished.")

    def save_project_state(self):
        """Saves the current state of the task graph definition to a JSON file."""
        # This is a basic implementation. A more robust one would handle errors
        # and potentially save more comprehensive state.
        try:
            state_data = {
                "task_graph_definition": self.task_graph_definition.model_dump(mode='json'),
                "current_task_id": self.current_task_id,
                "task_statuses": self._task_status_map # Persist current statuses
            }
            # Ensure ./data directory exists
            import os
            os.makedirs("./data", exist_ok=True)
            
            with open(self.project_state_file, 'w') as f:
                json.dump(state_data, f, indent=2)
            self._log_to_ui(f"Project state saved to {self.project_state_file}")
        except Exception as e:
            logger.error(f"Failed to save project state: {e}")
            self._log_to_ui(f"Error: Failed to save project state: {e}")

    def load_project_state(self):
        """Loads project state from a JSON file."""
        try:
            with open(self.project_state_file, 'r') as f:
                state_data = json.load(f)
            
            # Re-hydrate TaskGraphDefinition and TaskNode objects
            loaded_nodes_data = state_data.get("task_graph_definition", {}).get("nodes", [])
            loaded_nodes = [TaskNode(**node_data) for node_data in loaded_nodes_data]
            
            self.task_graph_definition = TaskGraphDefinition(
                start_node_id=state_data.get("task_graph_definition", {}).get("start_node_id"),
                nodes=loaded_nodes
            )
            self.current_task_id = state_data.get("current_task_id")
            # Ensure _task_status_map is correctly typed if loaded from JSON
            loaded_statuses = state_data.get("task_statuses", {})
            self._task_status_map = {
                task_id: TaskStatus(status_str) if isinstance(status_str, str) else status_str 
                for task_id, status_str in loaded_statuses.items()
            }
            
            # Ensure loaded task nodes reflect persisted statuses
            for task_id, status_enum in self._task_status_map.items():
                task_node = self.task_graph_definition.get_node_by_id(task_id)
                if task_node:
                    task_node.status = status_enum # Already an enum from above


            self._log_to_ui(f"Project state loaded from {self.project_state_file}")
        except FileNotFoundError:
            self._log_to_ui(f"No project state file found at {self.project_state_file}. Starting fresh.")
        except Exception as e:
            logger.error(f"Failed to load project state: {e}")
            self._log_to_ui(f"Error: Failed to load project state: {e}. Starting fresh.")


# This module is designed to be imported and used by other components.
# For testing, use the test files in the tests/ directory.
