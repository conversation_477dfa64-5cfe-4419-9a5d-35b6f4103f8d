from typing import Dict, Callable, Any, Set
from langgraph.graph import Graph, END # Updated import

try:
    # Attempt to import SqliteSaver for optional checkpointing in the example
    from langgraph.checkpoint.sqlite import SqliteSaver
except ImportError:
    # If it fails, set SqliteSaver to None so the example can run without it
    SqliteSaver = None
from vibe_coder.engine.state import AgentState

# A dictionary defining the roles we expect in our agent execution graph
# The actual agent methods will be passed via agent_definitions
# These are symbolic names for nodes in our LangGraph.
PLANNER_NODE = "planner"
EXECUTOR_NODE = "executor" # This node will likely decide to call a tool
TOOL_INVOKER_NODE = "tool_invoker" # This node actually runs the tool
VERIFIER_NODE = "verifier"
REFLECTOR_NODE = "reflector"
RESPONSE_HANDLER_NODE = "response_handler" # Processes results and decides if finished


class TaskGraphBuilder:
    """
    Compiles a LangGraph state machine from agent definitions.
    This graph represents the internal cognitive loop of an agent performing a task.
    """

    def __init__(self, memory_for_checkpointing: Any = None):
        """
        Initializes the TaskGraphBuilder.
        Args:
            memory_for_checkpointing: Optional LangGraph memory (e.g., SqliteSaver.memory())
                                      for graph persistence. If None, graph is in-memory.
        """
        self.memory = memory_for_checkpointing
        self.defined_nodes: Set[str] = set() # To store names of nodes defined in the current graph

    def build_graph(self, agent_definitions: Dict[str, Callable[[AgentState], AgentState]]) -> Graph: # Return type updated
        """
        Builds and compiles the LangGraph.

        Args:
            agent_definitions: A dictionary mapping node names (roles like PLANNER_NODE,
                               EXECUTOR_NODE, etc.) to their callable implementations.
                               Each callable should take AgentState and return AgentState.

        Returns:
            A compiled LangGraph (StatefulGraph instance).
        """
        self.defined_nodes = set(agent_definitions.keys()) # Store defined nodes for decision methods
        
        workflow = Graph() # Updated instantiation

        # Add nodes to the graph
        for node_name, node_callable in agent_definitions.items():
            workflow.add_node(node_name, node_callable)

        # --- Define the graph structure (edges) ---
        # This is a simplified initial structure. It will become more complex
        # with conditional edges based on the AgentState.

        # Example: A simple flow might be Planner -> Executor -> ToolInvoker -> ResponseHandler
        # More complex: Planner -> Executor -> (conditional: tool or direct_answer)
        # If tool: -> ToolInvoker -> Verifier -> (conditional: success or reflect)
        # If reflect: -> Reflector -> Planner (retry)

        # For now, let's define a basic flow assuming these nodes exist in agent_definitions
        # and handle their own logic for populating state for the next step.

        if PLANNER_NODE in self.defined_nodes:
            workflow.set_entry_point(PLANNER_NODE)
            if EXECUTOR_NODE in self.defined_nodes:
                workflow.add_edge(PLANNER_NODE, EXECUTOR_NODE)
            elif RESPONSE_HANDLER_NODE in self.defined_nodes:
                workflow.add_edge(PLANNER_NODE, RESPONSE_HANDLER_NODE)
            else:
                workflow.add_edge(PLANNER_NODE, END)
        elif EXECUTOR_NODE in self.defined_nodes:
            workflow.set_entry_point(EXECUTOR_NODE)
        else:
            if not self.defined_nodes:
                 raise ValueError("Graph must have at least one node defined.")
            if RESPONSE_HANDLER_NODE in self.defined_nodes and PLANNER_NODE not in self.defined_nodes and EXECUTOR_NODE not in self.defined_nodes:
                workflow.set_entry_point(RESPONSE_HANDLER_NODE)
            # LangGraph will use the first added node if no entry point is set and no specific logic matches.


        if EXECUTOR_NODE in self.defined_nodes:
            # Executor decides if a tool needs to be called.
            # We need a conditional edge here.
            workflow.add_conditional_edges(
                EXECUTOR_NODE,
                self._decide_after_executor,
                {
                    "invoke_tool": TOOL_INVOKER_NODE if TOOL_INVOKER_NODE in self.defined_nodes else END,
                    "handle_response": RESPONSE_HANDLER_NODE if RESPONSE_HANDLER_NODE in self.defined_nodes else END,
                    "reflect": REFLECTOR_NODE if REFLECTOR_NODE in self.defined_nodes else END,
                    "end": END
                }
            )
        
        if TOOL_INVOKER_NODE in self.defined_nodes:
            # After tool invocation, we might verify or just handle response
            # For now, let's assume it goes to response_handler or reflector if tool failed
            workflow.add_conditional_edges(
                TOOL_INVOKER_NODE,
                self._decide_after_tool_invocation,
                {
                    "verify": VERIFIER_NODE if VERIFIER_NODE in self.defined_nodes else (RESPONSE_HANDLER_NODE if RESPONSE_HANDLER_NODE in self.defined_nodes else END),
                    "handle_response": RESPONSE_HANDLER_NODE if RESPONSE_HANDLER_NODE in self.defined_nodes else END,
                    "reflect": REFLECTOR_NODE if REFLECTOR_NODE in self.defined_nodes else END,
                    "end": END
                }
            )

        if VERIFIER_NODE in self.defined_nodes:
            workflow.add_conditional_edges(
                VERIFIER_NODE,
                self._decide_after_verification,
                {
                    "handle_response": RESPONSE_HANDLER_NODE if RESPONSE_HANDLER_NODE in self.defined_nodes else END,
                    "reflect": REFLECTOR_NODE if REFLECTOR_NODE in self.defined_nodes else (PLANNER_NODE if PLANNER_NODE in self.defined_nodes else END),
                    "end": END
                }
            )

        if REFLECTOR_NODE in self.defined_nodes:
            # After reflection, typically re-plan or try a different approach
            workflow.add_edge(REFLECTOR_NODE, PLANNER_NODE if PLANNER_NODE in self.defined_nodes else END)


        if RESPONSE_HANDLER_NODE in self.defined_nodes:
             workflow.add_conditional_edges(
                RESPONSE_HANDLER_NODE,
                self._decide_is_final_answer,
                {
                    "continue": PLANNER_NODE if PLANNER_NODE in self.defined_nodes else END, # Loop back for more sub-tasks/iterations
                    "end": END
                }
            )
        
        # Fallback for any node that isn't explicitly connected to END but should be a terminal
        # This is tricky; nodes should ideally route to END via conditional logic.
        # For now, ensure all defined conditional paths can reach END.
        # LangGraph will validate if nodes are unreachable or don't lead to an END.

        return workflow.compile(checkpointer=self.memory)

    def _decide_after_executor(self, state: AgentState) -> str:
        """Decides next step after the executor node."""
        if state.get("error_message"): # Executor itself failed critically
            return "reflect" 
        if state.get("tool_call_request_name"):
            return "invoke_tool"
        if state.get("is_final_answer", False): # Executor decided it's done
             return "end"
        return "handle_response" # No tool, proceed to handle whatever executor produced

    def _decide_after_tool_invocation(self, state: AgentState) -> str:
        """Decides next step after tool invocation."""
        if state.get("error_message"): # Tool invocation failed
            return "reflect"
        # If a verifier is defined, use it. Otherwise, go to response handler.
        if VERIFIER_NODE in self.defined_nodes: # Use instance variable
             return "verify"
        return "handle_response"

    def _decide_after_verification(self, state: AgentState) -> str:
        """Decides next step after verification."""
        # Example: if verification_output indicates failure, reflect. Otherwise, handle response.
        # This needs more specific logic based on verifier's output format.
        verification_result_summary = state.get("verification_output", "") 
        if "FAIL" in verification_result_summary.upper() or state.get("error_message"): # Simplified check
            return "reflect"
        return "handle_response"

    def _decide_is_final_answer(self, state: AgentState) -> str:
        """Checks if the response handler marked the answer as final."""
        if state.get("is_final_answer", False):
            return "end"
        return "continue" # Implies more work or iterations needed, loop back to planner


# This module is designed to be imported and used by other components.
# For testing, use the test files in the tests/ directory.


