from typing import TypedDict, Optional, Any, List, Dict

# Forward reference for TaskN<PERSON>, will be imported properly if needed by type hints
# but for TypedDict, string annotations are often fine.
# from vibe_coder.data_models.task_models import TaskNode
# from vibe_coder.data_models.api_contracts import ToolCallRequest

class AgentState(TypedDict, total=False):
    """
    Represents the state passed between nodes in the LangGraph.
    Each key is optional, and nodes will update parts of the state
    as the graph executes.

    Enhanced to support the architect's vision with strategic planning,
    procedural memory, and multi-phase workflows.
    """
    # Input to the graph / current task context
    current_task_id: Optional[str]
    current_task_description: Optional[str]
    current_task_intent: Optional[str] # From TaskIntent enum
    current_task_assigned_agent: Optional[str]
    current_task_tool_name: Optional[str]
    current_task_tool_arguments: Optional[Dict[str, Any]]
    current_task: Optional[Dict[str, Any]]  # Enhanced task object

    # Agent's internal workings
    input_query: Optional[str]          # Initial query or instruction for the current step
    thought_process: Optional[str]      # LLM's reasoning
    tool_call_request_thought: Optional[str] # Specific thought for tool call
    tool_call_request_name: Optional[str]    # Name of tool to call
    tool_call_request_args: Optional[Dict[str, Any]] # Args for tool

    # Outcome of actions
    tool_invocation_result: Optional[str] # Raw string result from a tool
    verification_output: Optional[str]    # Output from verifier (e.g., linter, test results)
    reflection_output: Optional[Dict[str, Any]] # Output from reflector (e.g., CorrectiveAction)

    # Control flow and status
    error_message: Optional[str]        # If any step encounters an error
    is_final_answer: bool               # Flag to indicate the graph should end
    final_output: Optional[Any]         # The final result of the graph's execution for the task

    # History and context
    history: List[str]                  # Log of significant events or messages
    # We can add more specific fields as needed, e.g., for context from LibrarianAgent
    retrieved_context: Optional[str]

    # Strategic Planning State (NEW)
    charter: Optional[Dict[str, Any]]           # Project charter for strategic planning
    strategic_context: Optional[Dict[str, Any]] # Context for strategic decisions
    lessons_consulted: Optional[Dict[str, Any]] # Results from lessons learned consultation
    debate_results: Optional[Dict[str, Any]]    # Results from Muse vs Oracle debate
    wisdom_synthesis: Optional[Dict[str, Any]]  # Results from Wisdom Layer synthesis
    execution_strategy: Optional[Dict[str, Any]] # Final execution strategy

    # Workflow Management (NEW)
    workflow_stage: Optional[str]               # Current stage in workflow (strategic/planning/execution/reflection)
    whiteboard_session_results: Optional[Dict[str, Any]] # Results from whiteboard session
    socratic_review_results: Optional[Dict[str, Any]]    # Results from Socratic supervision

    # Enhanced Memory Integration (NEW)
    procedural_memory_context: Optional[Dict[str, Any]]  # Context from procedural memory
    applicable_lessons: Optional[List[Dict[str, Any]]]   # Lessons to apply in current context

    # Multi-Agent Coordination (NEW)
    muse_proposals: Optional[List[Dict[str, Any]]]       # Creative proposals from Muse
    oracle_assessments: Optional[List[Dict[str, Any]]]   # Risk assessments from Oracle
    supervisor_critiques: Optional[List[Dict[str, Any]]] # Critiques from Socratic Supervisor

    # Task Graph Context (NEW)
    task_graph: Optional[Dict[str, Any]]        # Current task graph being executed
    task_dependencies: Optional[List[str]]      # Dependencies for current task
    parallel_tasks: Optional[List[str]]         # Tasks that can run in parallel

    # Cognitive Loop State (NEW)
    cognitive_cycle_stage: Optional[str]        # Current stage: think/act/verify/reflect
    hypothesis_under_test: Optional[str]        # Current hypothesis being tested
    action_hypothesis_entry: Optional[Dict[str, Any]] # Entry for action-hypothesis ledger
