"""
Whiteboard Session: Strategic planning phase before execution.
Orchestrates strategic debate and synthesizes results into execution plan.
"""

import logging
from typing import Dict, Any, List, Optional
from vibe_coder.core.strategy.strategic_debate import StrategicDebate
from vibe_coder.core.planning.socratic_supervisor import SocraticSupervisor
from vibe_coder.memory.lessons_learned import LessonsLearnedManager
from vibe_coder.data_models.project_models import ProjectCharter

logger = logging.getLogger(__name__)


class WhiteboardSession:
    """
    Manages strategic planning phase with debate and synthesis.
    
    The Whiteboard Session is the pre-execution strategic loop that:
    1. Consults lessons learned from past experience
    2. Conducts strategic debate between Muse and Oracle
    3. Applies Socratic supervision to challenge assumptions
    4. Synthesizes results into actionable execution strategy
    """
    
    def __init__(self, config: Dict[str, Any], lessons_manager: LessonsLearnedManager):
        """
        Initialize the Whiteboard Session.
        
        Args:
            config: Configuration dictionary for agents
            lessons_manager: Lessons learned manager for procedural memory
        """
        self.config = config
        self.lessons_manager = lessons_manager
        self.strategic_debate = StrategicDebate(config)
        self.socratic_supervisor = SocraticSupervisor(config)
        
        logger.info("WhiteboardSession initialized with strategic debate and supervision")
    
    def conduct_session(self, charter: ProjectCharter) -> Dict[str, Any]:
        """
        Conduct complete whiteboard session.
        
        Args:
            charter: Project charter containing goals and context
            
        Returns:
            Dictionary containing session results and execution strategy
        """
        try:
            logger.info(f"Starting whiteboard session for: {charter.goal}")
            
            session_results = {
                "charter": charter.dict() if hasattr(charter, 'dict') else charter,
                "phase_1_lessons": None,
                "phase_2_strategic_debate": None,
                "phase_3_socratic_review": None,
                "phase_4_synthesis": None,
                "execution_strategy": None,
                "session_success": False
            }
            
            # Phase 1: Consult lessons learned
            logger.info("Phase 1: Consulting lessons learned")
            lessons_consultation = self._consult_lessons_learned(charter)
            session_results["phase_1_lessons"] = lessons_consultation
            
            # Phase 2: Strategic debate
            logger.info("Phase 2: Conducting strategic debate")
            problem_context = self._build_problem_context(charter, lessons_consultation)
            strategic_results = self.strategic_debate.conduct_debate(problem_context, rounds=3)
            session_results["phase_2_strategic_debate"] = strategic_results
            
            # Phase 3: Socratic review
            logger.info("Phase 3: Applying Socratic supervision")
            if strategic_results.get("success"):
                socratic_review = self._conduct_socratic_review(strategic_results)
                session_results["phase_3_socratic_review"] = socratic_review
            
            # Phase 4: Final synthesis
            logger.info("Phase 4: Synthesizing execution strategy")
            execution_strategy = self._synthesize_execution_strategy(session_results)
            session_results["execution_strategy"] = execution_strategy
            session_results["session_success"] = True
            
            logger.info("Whiteboard session completed successfully")
            return session_results
            
        except Exception as e:
            logger.error(f"Whiteboard session failed: {e}")
            session_results["session_success"] = False
            session_results["error"] = str(e)
            return session_results
    
    def _consult_lessons_learned(self, charter: ProjectCharter) -> Dict[str, Any]:
        """
        Consult lessons learned for relevant experience.
        
        Args:
            charter: Project charter
            
        Returns:
            Dictionary containing relevant lessons and insights
        """
        try:
            # Build context for lesson retrieval
            context = f"{charter.goal} {charter.description}"
            
            # Get relevant lessons
            relevant_lessons = self.lessons_manager.get_relevant_lessons(context)
            
            # Get high-confidence lessons
            high_confidence_lessons = self.lessons_manager.get_high_confidence_lessons()
            
            # Get best practices and anti-patterns
            best_practices = self.lessons_manager.get_lessons_by_type("best_practice")
            anti_patterns = self.lessons_manager.get_lessons_by_type("anti_pattern")
            
            consultation_result = {
                "relevant_lessons": relevant_lessons,
                "high_confidence_lessons": high_confidence_lessons[:5],
                "applicable_best_practices": [bp for bp in best_practices if self._is_lesson_applicable(bp, context)][:3],
                "relevant_anti_patterns": [ap for ap in anti_patterns if self._is_lesson_applicable(ap, context)][:3],
                "lesson_insights": self._extract_lesson_insights(relevant_lessons),
                "consultation_summary": self._create_consultation_summary(relevant_lessons, best_practices, anti_patterns)
            }
            
            logger.info(f"Consulted {len(relevant_lessons)} relevant lessons")
            return consultation_result
            
        except Exception as e:
            logger.error(f"Lessons consultation failed: {e}")
            return {
                "relevant_lessons": [],
                "error": str(e)
            }
    
    def _build_problem_context(self, charter: ProjectCharter, lessons_consultation: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build problem context for strategic debate.
        
        Args:
            charter: Project charter
            lessons_consultation: Results from lessons consultation
            
        Returns:
            Dictionary containing problem context
        """
        context = {
            "problem": charter.goal,
            "description": charter.description,
            "goals": [charter.goal],
            "constraints": getattr(charter, 'constraints', []),
            "context": charter.description,
            "lessons_insights": lessons_consultation.get("lesson_insights", []),
            "known_anti_patterns": [ap.get("description", "") for ap in lessons_consultation.get("relevant_anti_patterns", [])],
            "proven_approaches": [bp.get("description", "") for bp in lessons_consultation.get("applicable_best_practices", [])]
        }
        
        return context
    
    def _conduct_socratic_review(self, strategic_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Conduct Socratic review of strategic debate results.
        
        Args:
            strategic_results: Results from strategic debate
            
        Returns:
            Dictionary containing Socratic review results
        """
        try:
            final_synthesis = strategic_results.get("final_synthesis", {})
            strategic_recommendation = strategic_results.get("strategic_recommendation", "")
            
            # Extract plan from strategic results
            plan = {
                "description": strategic_recommendation,
                "steps": self._extract_plan_steps(final_synthesis),
                "assumptions": self._extract_plan_assumptions(final_synthesis)
            }
            
            # Critique the plan
            critique_result = self.socratic_supervisor.critique_plan(plan)
            
            # Challenge key assumptions
            assumptions = plan.get("assumptions", [])
            if assumptions:
                assumption_challenges = self.socratic_supervisor.challenge_assumptions(
                    assumptions, 
                    domain_context=strategic_recommendation
                )
            else:
                assumption_challenges = {"assumption_challenges": []}
            
            socratic_review = {
                "plan_critique": critique_result,
                "assumption_challenges": assumption_challenges,
                "critical_questions": critique_result.get("critique_questions", []),
                "identified_risks": critique_result.get("hidden_assumptions", []),
                "improvement_suggestions": critique_result.get("improvement_suggestions", []),
                "review_summary": self._create_socratic_summary(critique_result, assumption_challenges)
            }
            
            logger.info("Completed Socratic review of strategic plan")
            return socratic_review
            
        except Exception as e:
            logger.error(f"Socratic review failed: {e}")
            return {
                "plan_critique": {"success": False, "error": str(e)},
                "assumption_challenges": {"success": False, "error": str(e)}
            }
    
    def _synthesize_execution_strategy(self, session_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Synthesize final execution strategy from all session phases.
        
        Args:
            session_results: Complete session results
            
        Returns:
            Dictionary containing execution strategy
        """
        try:
            # Extract key elements from each phase
            lessons = session_results.get("phase_1_lessons", {})
            strategic_debate = session_results.get("phase_2_strategic_debate", {})
            socratic_review = session_results.get("phase_3_socratic_review", {})
            
            # Build execution strategy
            execution_strategy = {
                "strategic_approach": self._extract_strategic_approach(strategic_debate),
                "risk_mitigation": self._compile_risk_mitigation(strategic_debate, socratic_review),
                "success_criteria": self._define_success_criteria(strategic_debate),
                "implementation_phases": self._define_implementation_phases(strategic_debate),
                "critical_assumptions": self._compile_critical_assumptions(socratic_review),
                "lessons_to_apply": self._select_lessons_to_apply(lessons),
                "monitoring_points": self._define_monitoring_points(socratic_review),
                "fallback_strategies": self._define_fallback_strategies(strategic_debate),
                "execution_confidence": self._calculate_execution_confidence(session_results),
                "strategy_summary": self._create_strategy_summary(session_results)
            }
            
            logger.info("Synthesized comprehensive execution strategy")
            return execution_strategy
            
        except Exception as e:
            logger.error(f"Execution strategy synthesis failed: {e}")
            return {
                "strategic_approach": "Unknown",
                "error": str(e)
            }
    
    def _is_lesson_applicable(self, lesson: Dict[str, Any], context: str) -> bool:
        """Check if a lesson is applicable to the current context."""
        lesson_contexts = lesson.get("contexts", [])
        context_words = set(context.lower().split())
        
        # Simple keyword matching
        for lesson_context in lesson_contexts:
            if lesson_context.lower() in context.lower():
                return True
        
        return False
    
    def _extract_lesson_insights(self, lessons: List[Dict[str, Any]]) -> List[str]:
        """Extract key insights from relevant lessons."""
        insights = []
        
        for lesson in lessons[:5]:  # Top 5 lessons
            description = lesson.get("description", "")
            if description:
                insights.append(description)
        
        return insights
    
    def _create_consultation_summary(self, relevant_lessons: List[Dict], best_practices: List[Dict], anti_patterns: List[Dict]) -> str:
        """Create summary of lessons consultation."""
        summary = f"""
        Lessons Consultation Summary:
        - {len(relevant_lessons)} relevant lessons found
        - {len(best_practices)} best practices available
        - {len(anti_patterns)} anti-patterns to avoid
        
        Key insights: Apply proven approaches while avoiding known failure patterns.
        """
        
        return summary.strip()
    
    def _extract_plan_steps(self, synthesis: Dict[str, Any]) -> List[str]:
        """Extract plan steps from strategic synthesis."""
        steps = []
        
        # Look for implementation guidance
        implementation_guidance = synthesis.get("implementation_guidance", {})
        if implementation_guidance.get("key_success_factors"):
            steps.extend(implementation_guidance["key_success_factors"])
        
        # Look for balanced recommendation
        balanced_recommendation = synthesis.get("balanced_recommendation", "")
        if balanced_recommendation:
            steps.append(balanced_recommendation)
        
        return steps[:5]  # Limit to 5 steps
    
    def _extract_plan_assumptions(self, synthesis: Dict[str, Any]) -> List[str]:
        """Extract plan assumptions from strategic synthesis."""
        assumptions = []
        
        # Look for assumptions in various parts of synthesis
        if synthesis.get("wisdom_synthesis", {}).get("governance_principles"):
            assumptions.extend(synthesis["wisdom_synthesis"]["governance_principles"])
        
        return assumptions[:3]  # Limit to 3 key assumptions

    def _create_socratic_summary(self, critique_result: Dict[str, Any], assumption_challenges: Dict[str, Any]) -> str:
        """Create summary of Socratic review."""
        questions_count = len(critique_result.get("critique_questions", []))
        assumptions_count = len(assumption_challenges.get("assumption_challenges", []))

        summary = f"""
        Socratic Review Summary:
        - {questions_count} critical questions raised
        - {assumptions_count} assumptions challenged
        - Key concerns identified and improvement suggestions provided
        """

        return summary.strip()

    def _extract_strategic_approach(self, strategic_debate: Dict[str, Any]) -> str:
        """Extract strategic approach from debate results."""
        if strategic_debate.get("success"):
            return strategic_debate.get("strategic_recommendation", "Proceed with strategic analysis")
        else:
            return "Strategic approach unclear - requires further analysis"

    def _compile_risk_mitigation(self, strategic_debate: Dict[str, Any], socratic_review: Dict[str, Any]) -> List[str]:
        """Compile risk mitigation strategies from debate and review."""
        mitigations = []

        # From strategic debate
        final_synthesis = strategic_debate.get("final_synthesis", {})
        if final_synthesis.get("risk_mitigation_strategy", {}).get("mitigation_strategies"):
            mitigations.extend(final_synthesis["risk_mitigation_strategy"]["mitigation_strategies"])

        # From Socratic review
        if socratic_review.get("improvement_suggestions"):
            mitigations.extend(socratic_review["improvement_suggestions"])

        return mitigations[:5]  # Top 5 mitigations

    def _define_success_criteria(self, strategic_debate: Dict[str, Any]) -> List[str]:
        """Define success criteria based on strategic debate."""
        criteria = []

        final_synthesis = strategic_debate.get("final_synthesis", {})

        # Look for implementation guidance
        implementation_guidance = final_synthesis.get("implementation_guidance", {})
        if implementation_guidance.get("key_success_factors"):
            criteria.extend(implementation_guidance["key_success_factors"])

        # Default criteria if none found
        if not criteria:
            criteria = [
                "Solution addresses core problem",
                "Implementation is feasible",
                "Risks are adequately mitigated"
            ]

        return criteria[:3]  # Top 3 criteria

    def _define_implementation_phases(self, strategic_debate: Dict[str, Any]) -> List[Dict[str, str]]:
        """Define implementation phases based on strategic results."""
        phases = []

        final_synthesis = strategic_debate.get("final_synthesis", {})
        implementation_guidance = final_synthesis.get("implementation_guidance", {})

        approach = implementation_guidance.get("recommended_approach", "incremental")

        if approach == "pilot_first":
            phases = [
                {"phase": "Pilot", "description": "Small-scale implementation to validate approach"},
                {"phase": "Evaluation", "description": "Assess pilot results and refine approach"},
                {"phase": "Full Implementation", "description": "Scale successful pilot to full solution"}
            ]
        elif approach == "incremental":
            phases = [
                {"phase": "Foundation", "description": "Establish core components and infrastructure"},
                {"phase": "Core Features", "description": "Implement primary functionality"},
                {"phase": "Enhancement", "description": "Add advanced features and optimizations"}
            ]
        else:  # full_implementation
            phases = [
                {"phase": "Planning", "description": "Detailed planning and preparation"},
                {"phase": "Implementation", "description": "Full solution implementation"},
                {"phase": "Validation", "description": "Testing and validation of complete solution"}
            ]

        return phases

    def _compile_critical_assumptions(self, socratic_review: Dict[str, Any]) -> List[str]:
        """Compile critical assumptions from Socratic review."""
        assumptions = []

        if socratic_review.get("identified_risks"):
            assumptions.extend(socratic_review["identified_risks"])

        assumption_challenges = socratic_review.get("assumption_challenges", {})
        if assumption_challenges.get("assumption_challenges"):
            for challenge in assumption_challenges["assumption_challenges"]:
                assumptions.append(challenge.get("assumption", ""))

        return assumptions[:5]  # Top 5 assumptions

    def _select_lessons_to_apply(self, lessons: Dict[str, Any]) -> List[Dict[str, str]]:
        """Select key lessons to apply during execution."""
        lessons_to_apply = []

        # Best practices to apply
        best_practices = lessons.get("applicable_best_practices", [])
        for bp in best_practices[:3]:
            lessons_to_apply.append({
                "type": "best_practice",
                "lesson": bp.get("description", ""),
                "confidence": str(bp.get("confidence", 0))
            })

        # Anti-patterns to avoid
        anti_patterns = lessons.get("relevant_anti_patterns", [])
        for ap in anti_patterns[:2]:
            lessons_to_apply.append({
                "type": "anti_pattern",
                "lesson": ap.get("description", ""),
                "confidence": str(ap.get("confidence", 0))
            })

        return lessons_to_apply

    def _define_monitoring_points(self, socratic_review: Dict[str, Any]) -> List[str]:
        """Define key monitoring points based on Socratic review."""
        monitoring_points = []

        critical_questions = socratic_review.get("critical_questions", [])
        for question in critical_questions[:3]:
            monitoring_points.append(f"Monitor: {question}")

        if not monitoring_points:
            monitoring_points = [
                "Monitor progress against success criteria",
                "Watch for identified risk factors",
                "Validate key assumptions regularly"
            ]

        return monitoring_points

    def _define_fallback_strategies(self, strategic_debate: Dict[str, Any]) -> List[str]:
        """Define fallback strategies from strategic debate."""
        fallbacks = []

        final_synthesis = strategic_debate.get("final_synthesis", {})
        implementation_guidance = final_synthesis.get("implementation_guidance", {})

        if implementation_guidance.get("fallback_strategies"):
            fallbacks.extend(implementation_guidance["fallback_strategies"])

        # Default fallbacks if none found
        if not fallbacks:
            fallbacks = [
                "Revert to previous working approach",
                "Implement minimal viable solution",
                "Seek additional expert consultation"
            ]

        return fallbacks[:3]  # Top 3 fallbacks

    def _calculate_execution_confidence(self, session_results: Dict[str, Any]) -> float:
        """Calculate confidence score for execution strategy."""
        confidence_factors = []

        # Factor 1: Strategic debate success
        strategic_debate = session_results.get("phase_2_strategic_debate", {})
        if strategic_debate.get("success"):
            confidence_factors.append(0.3)

        # Factor 2: Number of relevant lessons
        lessons = session_results.get("phase_1_lessons", {})
        relevant_lessons_count = len(lessons.get("relevant_lessons", []))
        confidence_factors.append(min(relevant_lessons_count / 10, 0.2))  # Max 0.2 for lessons

        # Factor 3: Socratic review completeness
        socratic_review = session_results.get("phase_3_socratic_review", {})
        if socratic_review.get("plan_critique", {}).get("success"):
            confidence_factors.append(0.3)

        # Factor 4: Risk mitigation coverage
        execution_strategy = session_results.get("execution_strategy", {})
        risk_mitigations = len(execution_strategy.get("risk_mitigation", []))
        confidence_factors.append(min(risk_mitigations / 5, 0.2))  # Max 0.2 for risk coverage

        return sum(confidence_factors)

    def _create_strategy_summary(self, session_results: Dict[str, Any]) -> str:
        """Create comprehensive strategy summary."""
        strategic_debate = session_results.get("phase_2_strategic_debate", {})
        lessons = session_results.get("phase_1_lessons", {})
        socratic_review = session_results.get("phase_3_socratic_review", {})

        lessons_count = len(lessons.get("relevant_lessons", []))
        debate_rounds = len(strategic_debate.get("debate_rounds", []))
        questions_count = len(socratic_review.get("critical_questions", []))

        summary = f"""
        Whiteboard Session Strategy Summary:

        📚 Lessons Consulted: {lessons_count} relevant lessons from past experience
        🎯 Strategic Debate: {debate_rounds} rounds of Muse vs Oracle dialogue
        🤔 Socratic Review: {questions_count} critical questions and challenges raised

        Final Recommendation: {strategic_debate.get('strategic_recommendation', 'Continue analysis')}

        The strategy balances creative innovation with risk awareness, informed by
        past experience and challenged through critical questioning.
        """

        return summary.strip()
