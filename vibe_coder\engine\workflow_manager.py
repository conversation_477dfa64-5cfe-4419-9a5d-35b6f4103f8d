"""
Workflow Manager: Orchestrates LangGraph workflows for strategic and execution phases.
Replaces the old Driver with sophisticated workflow management capabilities.
"""

import json
import logging
from typing import Dict, Any, Optional, List

from langgraph.graph import StateGraph

from vibe_coder.data_models.task_models import TaskGraphDefinition, TaskNode, TaskStatus, TaskIntent
from vibe_coder.engine.state import AgentState
from vibe_coder.workflows.strategic_workflow import StrategicWorkflow
from vibe_coder.workflows.execution_workflow import ExecutionWorkflow
from vibe_coder.memory.lessons_learned import LessonsLearnedManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - WORKFLOW_MANAGER - %(message)s')
logger = logging.getLogger(__name__)

class WorkflowManager:
    """
    Orchestrates LangGraph workflows for strategic planning and task execution.

    This manager coordinates the full sequence:
    1. Strategic Planning (via StrategicWorkflow)
    2. Task Decomposition (via TaskDecomposer)
    3. Task Execution (via ExecutionWorkflow)
    """

    def __init__(self,
                 config: Dict[str, Any],
                 lessons_manager: Lessons<PERSON>earnedManager,
                 signals_controller: Optional[Any] = None,
                 project_state_file: str = "./data/project_state.json"):
        """
        Initialize the Workflow Manager.

        Args:
            config: Configuration dictionary
            lessons_manager: Lessons learned manager for procedural memory
            signals_controller: Optional UI controller for signals
            project_state_file: Path to project state persistence
        """
        self.config = config
        self.lessons_manager = lessons_manager
        self.signals_controller = signals_controller
        self.project_state_file = project_state_file

        # Initialize workflows
        self.strategic_workflow = StrategicWorkflow(config, lessons_manager)
        self.execution_workflow = ExecutionWorkflow(config)

        # Current execution state
        self.current_task_graph: Optional[TaskGraphDefinition] = None
        self.current_task_id: Optional[str] = None
        self._task_status_map: Dict[str, TaskStatus] = {}
        self._stop_requested: bool = False

        logger.info("WorkflowManager initialized with strategic and execution workflows")

    def execute_goal(self, charter: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a goal using the full strategic workflow sequence.

        Args:
            charter: Project charter containing goal and context

        Returns:
            Dictionary containing execution results
        """
        try:
            goal = charter.get('goal', 'Unknown goal')
            logger.info(f"Starting goal execution: {goal}")

            # Phase 1: Strategic Planning
            self._log_to_ui("🎯 Starting strategic planning...")
            self._log_to_ui(f"📋 Your goal: {goal}")
            self._log_to_ui("🧠 Looking at what we've learned from similar projects...")

            strategic_results = self.strategic_workflow.execute_strategic_planning(charter)

            if not strategic_results.get("success"):
                self._log_to_ui("❌ Something went wrong during planning. Let me try a different approach...")
                return {
                    "success": False,
                    "error": "Strategic planning failed",
                    "phase": "strategic_planning",
                    "details": strategic_results
                }

            self._log_to_ui("✅ Great! I've figured out the best approach for your project.")

            # Phase 2: Task Decomposition (will be integrated with TaskDecomposer)
            self._log_to_ui("🎯 Now breaking down the work into manageable steps...")
            self._log_to_ui("📊 Organizing tasks based on what I learned...")
            execution_strategy = strategic_results.get("execution_strategy", {})

            # For now, create a simple task graph - this will be enhanced with TaskDecomposer
            self._log_to_ui("🔧 Creating your project plan...")
            task_graph = self._create_simple_task_graph(charter, execution_strategy)
            self.current_task_graph = task_graph
            self._task_status_map = {node.id: node.status for node in task_graph.nodes}

            self._log_to_ui(f"📋 I've identified {len(task_graph.nodes)} key tasks to complete your project")

            # Phase 2.5: Open-Source Discovery (NEW)
            self._log_to_ui("🔍 Let me check if there are existing open-source projects that could help...")
            discovery_results = self._execute_open_source_discovery(charter, task_graph)

            if discovery_results.get("candidates_found"):
                # Human-in-the-loop decision required
                approval_result = self._request_discovery_approval(discovery_results)
                if approval_result.get("approved"):
                    # Adapt task graph for selected project
                    self._log_to_ui(f"✅ Great choice! Adapting plan to work with {approval_result['selected_project']['name']}")
                    task_graph = self._adapt_task_graph_for_project(task_graph, approval_result['selected_project'])
                    self.current_task_graph = task_graph
                    self._task_status_map = {node.id: node.status for node in task_graph.nodes}
                else:
                    self._log_to_ui("🔨 No problem! Proceeding with the original build-from-scratch plan.")
            else:
                self._log_to_ui("🔍 No suitable open-source projects found. Building from scratch will give us full control!")

            # Phase 3: Task Execution
            self._log_to_ui("🎯 Time to start building! Let me work through each task...")
            self._log_to_ui("🚀 I'll think through each step carefully before taking action...")
            execution_results = self._execute_task_graph()

            # Compile final results
            results = {
                "success": True,
                "strategic_results": strategic_results,
                "execution_results": execution_results,
                "task_graph": task_graph.model_dump() if task_graph else None,
                "phases_completed": ["strategic_planning", "task_decomposition", "task_execution"]
            }

            self._log_to_ui("🎉 Fantastic! Your project is complete!")
            completed_tasks = execution_results.get("completed_tasks", 0)
            total_tasks = execution_results.get("total_tasks", 0)
            self._log_to_ui(f"📊 Successfully finished {completed_tasks} out of {total_tasks} tasks")

            logger.info("Goal execution completed successfully")
            return results

        except Exception as e:
            logger.error(f"Goal execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "phase": "workflow_management"
            }

    def _log_to_ui(self, message: str):
        """Log message to UI and logger."""
        logger.info(message)
        if self.signals_controller and hasattr(self.signals_controller, 'log_signal'):
            self.signals_controller.log_signal.emit(f"🔄 {message}")
        else:
            print(f"UI_LOG (WorkflowManager): {message}")

    def _create_simple_task_graph(self, charter: Dict[str, Any], execution_strategy: Dict[str, Any]) -> TaskGraphDefinition:
        """
        Create a simple task graph for demonstration.
        This will be replaced with proper TaskDecomposer integration.
        """
        # Create a simple task based on the goal
        goal = charter.get("goal", "Complete the task")

        task_node = TaskNode(
            id="main_task",
            description=goal,
            intent=TaskIntent.EXECUTE_TOOL,
            assigned_agent="coder",
            dependencies=[],
            status=TaskStatus.PENDING
        )

        return TaskGraphDefinition(
            start_node_id="main_task",
            nodes=[task_node]
        )

    def _execute_task_graph(self) -> Dict[str, Any]:
        """
        Execute the current task graph using the ExecutionWorkflow.

        Returns:
            Dictionary containing execution results
        """
        if not self.current_task_graph:
            return {"success": False, "error": "No task graph to execute"}

        execution_results = []
        total_tasks = len(self.current_task_graph.nodes)
        completed_count = 0

        while True:
            # Check for stop request
            if self._stop_requested:
                self._log_to_ui("🛑 Execution stopped by user request.")
                break

            next_task = self._get_next_task()
            if not next_task:
                self._log_to_ui("✅ All done! Every task has been completed successfully.")
                break

            self.current_task_id = next_task.id
            self._update_task_status(next_task.id, TaskStatus.IN_PROGRESS)

            completed_count += 1
            self._log_to_ui(f"🔄 Working on task {completed_count} of {total_tasks}: {next_task.description}")
            self._log_to_ui(f"🤔 Let me think about the best way to approach this...")

            # Add a small delay to make progress visible
            import time
            time.sleep(0.5)

            # Execute task using ExecutionWorkflow
            task_data = {
                "id": next_task.id,
                "description": next_task.description,
                "intent": next_task.intent.value,
                "assigned_agent": next_task.assigned_agent,
                "tool_name": next_task.tool_name,
                "tool_arguments": next_task.tool_arguments
            }

            self._log_to_ui(f"⚡ Now taking action to complete this task...")
            task_result = self.execution_workflow.execute_task(task_data)
            execution_results.append(task_result)

            self._log_to_ui(f"🔍 Checking that everything worked correctly...")
            time.sleep(0.3)
            self._log_to_ui(f"💭 Learning from this experience for future tasks...")

            if task_result.get("success"):
                self._log_to_ui(f"✅ Perfect! This task is now complete.")
                self._update_task_status(next_task.id, TaskStatus.COMPLETED, result=task_result)
            else:
                self._log_to_ui(f"❌ This task hit a snag, but I'll learn from it: {task_result.get('error', 'Unknown error')}")
                self._update_task_status(next_task.id, TaskStatus.FAILED,
                                       error_message=task_result.get("error", "Unknown error"),
                                       result=task_result)

            self.current_task_id = None

        return {
            "success": True,
            "task_results": execution_results,
            "total_tasks": len(self.current_task_graph.nodes),
            "completed_tasks": len([r for r in execution_results if r.get("success")]),
            "failed_tasks": len([r for r in execution_results if not r.get("success")])
        }

    def _get_next_task(self) -> Optional[TaskNode]:
        """Get the next task ready for execution."""
        if not self.current_task_graph:
            return None

        # Start with the designated start_node_id if no task is current
        if not self.current_task_id and self.current_task_graph.start_node_id:
            start_node = self.current_task_graph.get_node_by_id(self.current_task_graph.start_node_id)
            if start_node and \
               self._task_status_map.get(start_node.id) == TaskStatus.PENDING and \
               self._are_dependencies_met(start_node):
                return start_node

        for task_node in self.current_task_graph.nodes:
            if self._task_status_map.get(task_node.id) == TaskStatus.PENDING:
                if self._are_dependencies_met(task_node):
                    return task_node
        return None

    def _are_dependencies_met(self, task_node: TaskNode) -> bool:
        """Check if all dependencies for a task are completed."""
        for dep_id in task_node.dependencies:
            if self._task_status_map.get(dep_id) != TaskStatus.COMPLETED:
                return False
        return True

    def _update_task_status(self, task_id: str, status: TaskStatus, error_message: Optional[str] = None, result: Optional[Any] = None):
        """Update the status of a task."""
        if not self.current_task_graph:
            return

        task_node = self.current_task_graph.get_node_by_id(task_id)
        if task_node:
            task_node.status = status
            self._task_status_map[task_id] = status
            if error_message:
                task_node.error_message = error_message
            if result:
                task_node.result = result
            self._log_to_ui(f"Task '{task_id}' status updated to {status}")
        else:
            self._log_to_ui(f"Warning: Could not find task '{task_id}' to update status")

    def get_execution_status(self) -> Dict[str, Any]:
        """Get current execution status."""
        if not self.current_task_graph:
            return {"status": "no_task_graph", "message": "No task graph loaded"}

        total_tasks = len(self.current_task_graph.nodes)
        completed_tasks = len([t for t in self._task_status_map.values() if t == TaskStatus.COMPLETED])
        failed_tasks = len([t for t in self._task_status_map.values() if t == TaskStatus.FAILED])
        pending_tasks = len([t for t in self._task_status_map.values() if t == TaskStatus.PENDING])
        in_progress_tasks = len([t for t in self._task_status_map.values() if t == TaskStatus.IN_PROGRESS])

        return {
            "status": "active" if self.current_task_id else "idle",
            "current_task_id": self.current_task_id,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "pending_tasks": pending_tasks,
            "in_progress_tasks": in_progress_tasks,
            "progress_percentage": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        }

    def save_project_state(self):
        """Save the current workflow state to a JSON file."""
        try:
            state_data = {
                "task_graph_definition": self.current_task_graph.model_dump(mode='json') if self.current_task_graph else None,
                "current_task_id": self.current_task_id,
                "task_statuses": {k: v.value for k, v in self._task_status_map.items()},
                "workflow_manager_version": "2.0"
            }

            import os
            os.makedirs("./data", exist_ok=True)

            with open(self.project_state_file, 'w') as f:
                json.dump(state_data, f, indent=2)
            self._log_to_ui(f"Project state saved to {self.project_state_file}")
        except Exception as e:
            logger.error(f"Failed to save project state: {e}")
            self._log_to_ui(f"Error: Failed to save project state: {e}")

    def load_project_state(self):
        """Load workflow state from a JSON file."""
        try:
            with open(self.project_state_file, 'r') as f:
                state_data = json.load(f)

            # Load task graph if present
            if state_data.get("task_graph_definition"):
                loaded_nodes_data = state_data["task_graph_definition"].get("nodes", [])
                loaded_nodes = [TaskNode(**node_data) for node_data in loaded_nodes_data]

                self.current_task_graph = TaskGraphDefinition(
                    start_node_id=state_data["task_graph_definition"].get("start_node_id"),
                    nodes=loaded_nodes
                )

            self.current_task_id = state_data.get("current_task_id")

            # Load task statuses
            loaded_statuses = state_data.get("task_statuses", {})
            self._task_status_map = {
                task_id: TaskStatus(status_str)
                for task_id, status_str in loaded_statuses.items()
            }

            self._log_to_ui(f"Project state loaded from {self.project_state_file}")
        except FileNotFoundError:
            self._log_to_ui(f"No project state file found. Starting fresh.")
        except Exception as e:
            logger.error(f"Failed to load project state: {e}")
            self._log_to_ui(f"Error: Failed to load project state: {e}. Starting fresh.")

    def stop_execution(self):
        """Request to stop the current execution."""
        self._stop_requested = True
        self._log_to_ui("🛑 Stop requested - will halt after current task completes.")

    def _execute_open_source_discovery(self, charter: Dict[str, Any], task_graph: TaskGraphDefinition) -> Dict[str, Any]:
        """Execute open-source discovery phase."""
        try:
            from vibe_coder.core.planning.open_source_scout import OpenSourceScout

            # Initialize scout
            scout = OpenSourceScout(self.config)

            # Discover candidates
            candidates = scout.discover_candidates(charter, task_graph)

            if candidates:
                self._log_to_ui(f"🎉 Found {len(candidates)} promising open-source projects!")
                return {
                    "success": True,
                    "candidates_found": True,
                    "candidates": candidates,
                    "message": f"Discovered {len(candidates)} high-quality candidates"
                }
            else:
                self._log_to_ui("🔍 Searched thoroughly but no suitable projects found.")
                return {
                    "success": True,
                    "candidates_found": False,
                    "candidates": [],
                    "message": "No suitable open-source projects found"
                }

        except Exception as e:
            logger.error(f"Open-source discovery failed: {e}")
            self._log_to_ui("⚠️ Discovery search encountered an issue, proceeding with original plan.")
            return {
                "success": False,
                "candidates_found": False,
                "candidates": [],
                "error": str(e)
            }

    def _request_discovery_approval(self, discovery_results: Dict[str, Any]) -> Dict[str, Any]:
        """Request human approval for discovered candidates."""
        try:
            candidates = discovery_results.get("candidates", [])

            if not candidates:
                return {
                    "approved": False,
                    "selected_project": None,
                    "message": "No candidates to approve"
                }

            self._log_to_ui("🤔 Presenting discovery results for your decision...")

            # Check if we have UI signals controller for interactive dialog
            if self.signals_controller and hasattr(self.signals_controller, 'request_discovery_approval'):
                # Request approval through UI
                approval_result = self.signals_controller.request_discovery_approval(candidates, discovery_results.get("project_goal", ""))
                return approval_result
            else:
                # Fallback: emit signal for UI to handle
                if self.signals_controller and hasattr(self.signals_controller, 'discovery_approval_requested'):
                    self.signals_controller.discovery_approval_requested.emit(candidates)

                # For now, default to build from scratch when no UI integration
                self._log_to_ui("⚠️ UI integration not available, proceeding with build from scratch.")
                return {
                    "approved": False,
                    "selected_project": None,
                    "message": "UI integration not available - defaulting to build from scratch"
                }

        except Exception as e:
            logger.error(f"Discovery approval failed: {e}")
            return {
                "approved": False,
                "selected_project": None,
                "error": str(e)
            }

    def _adapt_task_graph_for_project(self, original_graph: TaskGraphDefinition, selected_project: Dict[str, Any]) -> TaskGraphDefinition:
        """Adapt task graph to work with selected open-source project."""
        try:
            from vibe_coder.models.task_graph import TaskNode, TaskIntent, TaskStatus

            project_name = selected_project.get("name", "selected project")
            project_url = selected_project.get("url", "")
            covered_tasks = selected_project.get("covered_tasks", [])

            self._log_to_ui(f"🔧 Analyzing {project_name} structure...")
            self._log_to_ui("📝 Creating customization-focused task plan...")

            # Create new adaptation-focused tasks
            adaptation_tasks = []

            # Phase 1: Project Setup and Analysis
            setup_task = TaskNode(
                id="setup_project",
                description=f"Clone and set up {project_name} development environment",
                intent=TaskIntent.EXECUTE_TOOL,
                assigned_agent="coder",
                dependencies=[],
                status=TaskStatus.PENDING,
                tool_name="git_clone",
                tool_arguments={"url": project_url}
            )
            adaptation_tasks.append(setup_task)

            analysis_task = TaskNode(
                id="analyze_codebase",
                description=f"Analyze {project_name} codebase structure and architecture",
                intent=TaskIntent.ANALYZE,
                assigned_agent="coder",
                dependencies=["setup_project"],
                status=TaskStatus.PENDING,
                tool_name="code_analysis",
                tool_arguments={"target": "project_root"}
            )
            adaptation_tasks.append(analysis_task)

            # Phase 2: Customization Tasks
            customization_task = TaskNode(
                id="identify_customization_points",
                description="Identify key areas for customization and extension",
                intent=TaskIntent.ANALYZE,
                assigned_agent="coder",
                dependencies=["analyze_codebase"],
                status=TaskStatus.PENDING
            )
            adaptation_tasks.append(customization_task)

            # Phase 3: Implementation of uncovered tasks
            uncovered_tasks = [task for task in original_graph.nodes if task.id not in covered_tasks]
            for i, original_task in enumerate(uncovered_tasks[:3]):  # Limit to 3 additional tasks
                adapted_task = TaskNode(
                    id=f"implement_{original_task.id}",
                    description=f"Implement {original_task.description} as extension to {project_name}",
                    intent=TaskIntent.EXECUTE_TOOL,
                    assigned_agent="coder",
                    dependencies=["identify_customization_points"],
                    status=TaskStatus.PENDING,
                    tool_name=original_task.tool_name,
                    tool_arguments=original_task.tool_arguments
                )
                adaptation_tasks.append(adapted_task)

            # Phase 4: Integration and Testing
            integration_task = TaskNode(
                id="integrate_changes",
                description="Integrate custom features and test the adapted application",
                intent=TaskIntent.EXECUTE_TOOL,
                assigned_agent="coder",
                dependencies=[task.id for task in adaptation_tasks if task.id.startswith("implement_")],
                status=TaskStatus.PENDING,
                tool_name="test_runner",
                tool_arguments={"test_type": "integration"}
            )
            adaptation_tasks.append(integration_task)

            # Documentation task
            docs_task = TaskNode(
                id="update_documentation",
                description="Update documentation to reflect customizations and usage",
                intent=TaskIntent.EXECUTE_TOOL,
                assigned_agent="coder",
                dependencies=["integrate_changes"],
                status=TaskStatus.PENDING,
                tool_name="write_file",
                tool_arguments={"filename": "README_CUSTOMIZED.md"}
            )
            adaptation_tasks.append(docs_task)

            # Create new task graph
            adapted_graph = TaskGraphDefinition(
                start_node_id="setup_project",
                nodes=adaptation_tasks
            )

            self._log_to_ui(f"✅ Created adaptation plan with {len(adaptation_tasks)} tasks")
            self._log_to_ui(f"📋 Focus: Setup → Analysis → Customization → Integration → Documentation")

            return adapted_graph

        except Exception as e:
            logger.error(f"Task graph adaptation failed: {e}")
            self._log_to_ui("⚠️ Adaptation failed, using original task plan")
            return original_graph


# WorkflowManager replaces the old Driver with sophisticated workflow orchestration
# It coordinates Strategic Planning -> Task Decomposition -> Task Execution
