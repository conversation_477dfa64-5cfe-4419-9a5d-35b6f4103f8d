"""
Workflow Manager: Orchestrates LangGraph workflows for strategic and execution phases.
Replaces the old Driver with sophisticated workflow management capabilities.
"""

import json
import logging
from typing import Dict, Any, Optional, List

from langgraph.graph import StateGraph

from vibe_coder.data_models.task_models import TaskGraphDefinition, TaskNode, TaskStatus, TaskIntent
from vibe_coder.engine.state import AgentState
from vibe_coder.workflows.strategic_workflow import StrategicWorkflow
from vibe_coder.workflows.execution_workflow import ExecutionWorkflow
from vibe_coder.memory.lessons_learned import LessonsLearnedManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - WORKFLOW_MANAGER - %(message)s')
logger = logging.getLogger(__name__)

class WorkflowManager:
    """
    Orchestrates LangGraph workflows for strategic planning and task execution.

    This manager coordinates the full sequence:
    1. Strategic Planning (via StrategicWorkflow)
    2. Task Decomposition (via TaskDecomposer)
    3. Task Execution (via ExecutionWorkflow)
    """

    def __init__(self,
                 config: Dict[str, Any],
                 lessons_manager: Lessons<PERSON>earnedManager,
                 signals_controller: Optional[Any] = None,
                 project_state_file: str = "./data/project_state.json"):
        """
        Initialize the Workflow Manager.

        Args:
            config: Configuration dictionary
            lessons_manager: Lessons learned manager for procedural memory
            signals_controller: Optional UI controller for signals
            project_state_file: Path to project state persistence
        """
        self.config = config
        self.lessons_manager = lessons_manager
        self.signals_controller = signals_controller
        self.project_state_file = project_state_file

        # Initialize workflows
        self.strategic_workflow = StrategicWorkflow(config, lessons_manager)
        self.execution_workflow = ExecutionWorkflow(config)

        # Current execution state
        self.current_task_graph: Optional[TaskGraphDefinition] = None
        self.current_task_id: Optional[str] = None
        self._task_status_map: Dict[str, TaskStatus] = {}

        logger.info("WorkflowManager initialized with strategic and execution workflows")

    def execute_goal(self, charter: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a goal using the full strategic workflow sequence.

        Args:
            charter: Project charter containing goal and context

        Returns:
            Dictionary containing execution results
        """
        try:
            logger.info(f"Starting goal execution: {charter.get('goal', 'Unknown goal')}")

            # Phase 1: Strategic Planning
            self._log_to_ui("Phase 1: Strategic Planning")
            strategic_results = self.strategic_workflow.execute_strategic_planning(charter)

            if not strategic_results.get("success"):
                return {
                    "success": False,
                    "error": "Strategic planning failed",
                    "phase": "strategic_planning",
                    "details": strategic_results
                }

            # Phase 2: Task Decomposition (will be integrated with TaskDecomposer)
            self._log_to_ui("Phase 2: Task Decomposition")
            execution_strategy = strategic_results.get("execution_strategy", {})

            # For now, create a simple task graph - this will be enhanced with TaskDecomposer
            task_graph = self._create_simple_task_graph(charter, execution_strategy)
            self.current_task_graph = task_graph
            self._task_status_map = {node.id: node.status for node in task_graph.nodes}

            # Phase 3: Task Execution
            self._log_to_ui("Phase 3: Task Execution")
            execution_results = self._execute_task_graph()

            # Compile final results
            results = {
                "success": True,
                "strategic_results": strategic_results,
                "execution_results": execution_results,
                "task_graph": task_graph.model_dump() if task_graph else None,
                "phases_completed": ["strategic_planning", "task_decomposition", "task_execution"]
            }

            logger.info("Goal execution completed successfully")
            return results

        except Exception as e:
            logger.error(f"Goal execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "phase": "workflow_management"
            }

    def _log_to_ui(self, message: str):
        """Log message to UI and logger."""
        logger.info(message)
        if self.signals_controller and hasattr(self.signals_controller, 'log_signal'):
            self.signals_controller.log_signal.emit(f"WORKFLOW_MANAGER: {message}")
        else:
            print(f"UI_LOG (WorkflowManager): {message}")

    def _create_simple_task_graph(self, charter: Dict[str, Any], execution_strategy: Dict[str, Any]) -> TaskGraphDefinition:
        """
        Create a simple task graph for demonstration.
        This will be replaced with proper TaskDecomposer integration.
        """
        # Create a simple task based on the goal
        goal = charter.get("goal", "Complete the task")

        task_node = TaskNode(
            id="main_task",
            description=goal,
            intent=TaskIntent.EXECUTE_TOOL,
            assigned_agent="coder",
            dependencies=[],
            status=TaskStatus.PENDING
        )

        return TaskGraphDefinition(
            start_node_id="main_task",
            nodes=[task_node]
        )

    def _execute_task_graph(self) -> Dict[str, Any]:
        """
        Execute the current task graph using the ExecutionWorkflow.

        Returns:
            Dictionary containing execution results
        """
        if not self.current_task_graph:
            return {"success": False, "error": "No task graph to execute"}

        execution_results = []

        while True:
            next_task = self._get_next_task()
            if not next_task:
                self._log_to_ui("No more pending tasks. Execution complete.")
                break

            self.current_task_id = next_task.id
            self._update_task_status(next_task.id, TaskStatus.IN_PROGRESS)
            self._log_to_ui(f"Executing task: {next_task.id} - {next_task.description}")

            # Execute task using ExecutionWorkflow
            task_data = {
                "id": next_task.id,
                "description": next_task.description,
                "intent": next_task.intent.value,
                "assigned_agent": next_task.assigned_agent,
                "tool_name": next_task.tool_name,
                "tool_arguments": next_task.tool_arguments
            }

            task_result = self.execution_workflow.execute_task(task_data)
            execution_results.append(task_result)

            if task_result.get("success"):
                self._update_task_status(next_task.id, TaskStatus.COMPLETED, result=task_result)
            else:
                self._update_task_status(next_task.id, TaskStatus.FAILED,
                                       error_message=task_result.get("error", "Unknown error"),
                                       result=task_result)

            self.current_task_id = None

        return {
            "success": True,
            "task_results": execution_results,
            "total_tasks": len(self.current_task_graph.nodes),
            "completed_tasks": len([r for r in execution_results if r.get("success")]),
            "failed_tasks": len([r for r in execution_results if not r.get("success")])
        }

    def _get_next_task(self) -> Optional[TaskNode]:
        """Get the next task ready for execution."""
        if not self.current_task_graph:
            return None

        # Start with the designated start_node_id if no task is current
        if not self.current_task_id and self.current_task_graph.start_node_id:
            start_node = self.current_task_graph.get_node_by_id(self.current_task_graph.start_node_id)
            if start_node and \
               self._task_status_map.get(start_node.id) == TaskStatus.PENDING and \
               self._are_dependencies_met(start_node):
                return start_node

        for task_node in self.current_task_graph.nodes:
            if self._task_status_map.get(task_node.id) == TaskStatus.PENDING:
                if self._are_dependencies_met(task_node):
                    return task_node
        return None

    def _are_dependencies_met(self, task_node: TaskNode) -> bool:
        """Check if all dependencies for a task are completed."""
        for dep_id in task_node.dependencies:
            if self._task_status_map.get(dep_id) != TaskStatus.COMPLETED:
                return False
        return True

    def _update_task_status(self, task_id: str, status: TaskStatus, error_message: Optional[str] = None, result: Optional[Any] = None):
        """Update the status of a task."""
        if not self.current_task_graph:
            return

        task_node = self.current_task_graph.get_node_by_id(task_id)
        if task_node:
            task_node.status = status
            self._task_status_map[task_id] = status
            if error_message:
                task_node.error_message = error_message
            if result:
                task_node.result = result
            self._log_to_ui(f"Task '{task_id}' status updated to {status}")
        else:
            self._log_to_ui(f"Warning: Could not find task '{task_id}' to update status")

    def get_execution_status(self) -> Dict[str, Any]:
        """Get current execution status."""
        if not self.current_task_graph:
            return {"status": "no_task_graph", "message": "No task graph loaded"}

        total_tasks = len(self.current_task_graph.nodes)
        completed_tasks = len([t for t in self._task_status_map.values() if t == TaskStatus.COMPLETED])
        failed_tasks = len([t for t in self._task_status_map.values() if t == TaskStatus.FAILED])
        pending_tasks = len([t for t in self._task_status_map.values() if t == TaskStatus.PENDING])
        in_progress_tasks = len([t for t in self._task_status_map.values() if t == TaskStatus.IN_PROGRESS])

        return {
            "status": "active" if self.current_task_id else "idle",
            "current_task_id": self.current_task_id,
            "total_tasks": total_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "pending_tasks": pending_tasks,
            "in_progress_tasks": in_progress_tasks,
            "progress_percentage": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        }

    def save_project_state(self):
        """Save the current workflow state to a JSON file."""
        try:
            state_data = {
                "task_graph_definition": self.current_task_graph.model_dump(mode='json') if self.current_task_graph else None,
                "current_task_id": self.current_task_id,
                "task_statuses": {k: v.value for k, v in self._task_status_map.items()},
                "workflow_manager_version": "2.0"
            }

            import os
            os.makedirs("./data", exist_ok=True)

            with open(self.project_state_file, 'w') as f:
                json.dump(state_data, f, indent=2)
            self._log_to_ui(f"Project state saved to {self.project_state_file}")
        except Exception as e:
            logger.error(f"Failed to save project state: {e}")
            self._log_to_ui(f"Error: Failed to save project state: {e}")

    def load_project_state(self):
        """Load workflow state from a JSON file."""
        try:
            with open(self.project_state_file, 'r') as f:
                state_data = json.load(f)

            # Load task graph if present
            if state_data.get("task_graph_definition"):
                loaded_nodes_data = state_data["task_graph_definition"].get("nodes", [])
                loaded_nodes = [TaskNode(**node_data) for node_data in loaded_nodes_data]

                self.current_task_graph = TaskGraphDefinition(
                    start_node_id=state_data["task_graph_definition"].get("start_node_id"),
                    nodes=loaded_nodes
                )

            self.current_task_id = state_data.get("current_task_id")

            # Load task statuses
            loaded_statuses = state_data.get("task_statuses", {})
            self._task_status_map = {
                task_id: TaskStatus(status_str)
                for task_id, status_str in loaded_statuses.items()
            }

            self._log_to_ui(f"Project state loaded from {self.project_state_file}")
        except FileNotFoundError:
            self._log_to_ui(f"No project state file found. Starting fresh.")
        except Exception as e:
            logger.error(f"Failed to load project state: {e}")
            self._log_to_ui(f"Error: Failed to load project state: {e}. Starting fresh.")


# WorkflowManager replaces the old Driver with sophisticated workflow orchestration
# It coordinates Strategic Planning -> Task Decomposition -> Task Execution
