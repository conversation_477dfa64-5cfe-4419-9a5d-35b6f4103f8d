"""
Main entry point for the Vibe Coder system.
Contains the AgentBackend and main execution logic.
"""

import logging
import sys
from typing import Dict, Any, Optional, List
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QThread

from vibe_coder.config import init_config, get_config
from vibe_coder.engine.workflow_manager import WorkflowManager
from vibe_coder.memory.action_ledger import ActionHypothesisLedger
from vibe_coder.memory.vector_store import CodeVectorStore
from vibe_coder.memory.continuous_indexer import ContinuousIndexer
from vibe_coder.memory.lessons_learned import LessonsLearnedManager
from vibe_coder.tools.tool_registry import ToolRegistry
from vibe_coder.tools.code_analysis import CodeAnalysisTool
from vibe_coder.agents.coder_agent import CoderAgent
from vibe_coder.agents.task_decomposer import TaskDecomposerAgent
from vibe_coder.agents.meta_agent import MetaAgentCognoscente

logger = logging.getLogger(__name__)


class AgentBackend(QObject):
    """
    The main agent backend that orchestrates the entire Vibe Coder system.
    Designed to run in a background thread.
    """
    
    # Signals for communication with UI
    log_signal = pyqtSignal(str)
    status_signal = pyqtSignal(str)
    result_signal = pyqtSignal(dict)

    # Signals for receiving commands from UI
    goal_submitted = pyqtSignal(str, str)  # goal, description
    stop_requested = pyqtSignal()  # stop execution

    # Signals for open-source discovery
    discovery_approval_requested = pyqtSignal(list)  # candidates list

    # Signals for strategic consultation
    agent_consultation_requested = pyqtSignal(str, str, dict)  # agent_type, message, context
    team_brainstorm_requested = pyqtSignal(str, dict)  # topic, context
    consultation_response_ready = pyqtSignal(str, str, str)  # session_id, agent_type, response
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the AgentBackend.
        
        Args:
            config_path: Optional path to configuration file
        """
        super().__init__()
        
        # Initialize configuration
        self.config = init_config(config_path)
        
        # Initialize logging
        self._setup_logging()
        
        # Initialize core components
        self.tool_registry = None
        self.code_analyzer = None
        self.ledger = None
        self.lessons_manager = None
        self.workflow_manager = None

        # Initialize agents
        self.coder_agent = None
        self.task_decomposer = None
        self.meta_agent = None

        # Strategic consultation
        self.consultation_manager = None

        # Execution control
        self._stop_requested = False

        # Connect internal signals
        self.goal_submitted.connect(self.submit_goal)
        self.stop_requested.connect(self.stop_execution)

        logger.info("AgentBackend initialized")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = self.config.get("settings.log_level", "INFO")
        log_file = self.config.get("paths.log_file", "./logs/vibe_coder.log")
        
        # Ensure log directory exists
        import os
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger.info(f"Logging configured: level={log_level}, file={log_file}")
    
    def initialize_system(self):
        """Initialize all system components."""
        try:
            self.log_signal.emit("Initializing Vibe Coder system...")
            
            # Ensure required directories exist
            self.config.ensure_directories()
            
            # Initialize tool registry
            self.tool_registry = ToolRegistry()
            self.log_signal.emit(f"Loaded {len(self.tool_registry.list_tool_names())} tools")
            
            # Initialize code analyzer
            self.code_analyzer = CodeAnalysisTool()
            self.log_signal.emit("Code analysis tool initialized")
            
            # Initialize memory system
            ledger_path = self.config.get("paths.action_ledger_db")
            self.ledger = ActionHypothesisLedger(ledger_path)
            self.ledger.setup_database()

            # Initialize vector store for semantic memory
            vector_store_path = self.config.get("paths.vector_store_path", "./data/vector_store")
            self.vector_store = CodeVectorStore(vector_store_path)

            # Initialize lessons learned manager for procedural memory
            lessons_path = self.config.get("paths.lessons_learned_db", "./data/lessons_learned.json")
            self.lessons_manager = LessonsLearnedManager(lessons_path, self.ledger)

            # Initialize continuous indexer
            self.continuous_indexer = ContinuousIndexer(
                vector_store=self.vector_store,
                watch_paths=["."]  # Monitor current project directory
            )

            # Set up indexing callbacks
            self.continuous_indexer.on_file_indexed = self._on_file_indexed
            self.continuous_indexer.on_indexing_complete = self._on_indexing_complete

            self.log_signal.emit("Three-part memory system initialized")
            
            # Initialize agents
            self._initialize_agents()
            
            # Initialize execution engine
            self._initialize_engine()

            # Start continuous indexing
            self.continuous_indexer.start(initial_scan=True)
            self.log_signal.emit("Continuous indexing started")

            self.log_signal.emit("System initialization complete!")
            self.status_signal.emit("Ready")
            
        except Exception as e:
            error_msg = f"System initialization failed: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"ERROR: {error_msg}")
            self.status_signal.emit("Error")
            raise
    
    def _initialize_agents(self):
        """Initialize all agent instances."""
        config_dict = self.config.config

        # Initialize agents with specific names for LLM assignment
        self.coder_agent = CoderAgent(config_dict, self.tool_registry)
        self.coder_agent.agent_name = "coder_agent"

        self.task_decomposer = TaskDecomposerAgent(config_dict)
        self.task_decomposer.agent_name = "task_decomposer"

        self.meta_agent = MetaAgentCognoscente(config_dict, self.ledger)
        self.meta_agent.agent_name = "meta_agent"

        self.log_signal.emit("Agents initialized")
        logger.info("All agents initialized successfully")
    
    def _initialize_engine(self):
        """Initialize the workflow management engine."""
        # Initialize the sophisticated workflow manager
        self.workflow_manager = WorkflowManager(
            config=self.config.config,
            lessons_manager=self.lessons_manager,
            signals_controller=self,  # Pass self for UI signals
            project_state_file=self.config.get("paths.project_state_file", "./data/project_state.json")
        )

        # Initialize strategic consultation manager
        try:
            from vibe_coder.core.consultation.strategic_consultation import StrategicConsultationManager
            from vibe_coder.core.planning.open_source_scout import OpenSourceScout

            self.consultation_manager = StrategicConsultationManager(self.config.config)

            # Initialize Scout agent for research
            scout_agent = OpenSourceScout(self.config.config)

            # Connect agents to consultation manager (Scout will be connected when workflow is ready)
            self.scout_agent = scout_agent

            self.log_signal.emit("Strategic consultation system ready for brainstorming")
        except Exception as e:
            logger.warning(f"Failed to initialize consultation manager: {e}")
            self.consultation_manager = None
            self.scout_agent = None

        self.log_signal.emit("Strategic workflow manager initialized")
        logger.info("Workflow management engine ready with strategic capabilities")
    
    def run(self):
        """Main execution loop for the agent backend."""
        try:
            self.log_signal.emit("Starting Vibe Coder agent backend...")
            
            # Initialize the system
            self.initialize_system()
            
            # Connect strategic agents to consultation manager if available
            if self.consultation_manager and self.workflow_manager:
                try:
                    # Get strategic agents from workflow manager
                    strategic_workflow = getattr(self.workflow_manager, 'strategic_workflow', None)
                    if strategic_workflow:
                        whiteboard = getattr(strategic_workflow, 'whiteboard_session', None)
                        if whiteboard:
                            debate = getattr(whiteboard, 'strategic_debate', None)
                            if debate:
                                self.consultation_manager.initialize_agents(
                                    muse_agent=debate.muse,
                                    oracle_agent=debate.oracle,
                                    wisdom_layer=debate.wisdom_layer,
                                    scout_agent=self.scout_agent
                                )
                                self.log_signal.emit("All strategic agents connected to consultation system")
                except Exception as e:
                    logger.warning(f"Failed to connect strategic agents to consultation: {e}")

            # The system is now ready and waiting for tasks
            self.log_signal.emit("Agent backend is running and ready for tasks")
            
        except Exception as e:
            error_msg = f"Agent backend failed to start: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"CRITICAL ERROR: {error_msg}")
            self.status_signal.emit("Failed")
    
    @pyqtSlot(str, str)
    def submit_goal(self, goal: str, description: str = "", requirements: list = None):
        """
        Submit a new goal to the system using the strategic workflow sequence.

        This implements the architect's blueprint:
        1. Strategic Planning (WhiteboardSession via StrategicWorkflow)
        2. Task Decomposition (TaskDecomposer with strategic context)
        3. Task Execution (ExecutionWorkflow with Think→Act→Verify→Reflect)

        Args:
            goal: High-level goal description
            description: Detailed description
            requirements: List of specific requirements
        """
        try:
            self.log_signal.emit(f"🎯 Received new goal: {goal}")
            self.log_signal.emit("🏗️ Starting strategic workflow sequence...")

            # Create a project charter for strategic planning
            charter = {
                "goal": goal,
                "description": description or goal,
                "requirements": requirements or [],
                "constraints": [],
                "deliverables": [],
                "timestamp": "now"
            }

            # Execute the full strategic workflow sequence
            self.log_signal.emit("📋 Phase 1: Strategic Planning & Debate")
            result = self.workflow_manager.execute_goal(charter)

            # Emit the comprehensive result
            self.result_signal.emit(result)

            if result.get("success"):
                self.log_signal.emit("✅ Goal execution completed successfully!")
                phases = result.get("phases_completed", [])
                self.log_signal.emit(f"📊 Completed phases: {', '.join(phases)}")
            else:
                self.log_signal.emit(f"❌ Goal execution failed in {result.get('phase', 'unknown')} phase")

        except Exception as e:
            error_msg = f"Goal submission failed: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"🚨 ERROR: {error_msg}")
            self.result_signal.emit({
                "success": False,
                "error": error_msg,
                "phase": "goal_submission"
            })

    @pyqtSlot()
    def stop_execution(self):
        """Stop the current execution."""
        self._stop_requested = True
        self.log_signal.emit("🛑 Stop requested - halting execution...")

        # Signal workflow manager to stop if available
        if self.workflow_manager:
            self.workflow_manager.stop_execution()

    @pyqtSlot(str, str, dict)
    def handle_agent_consultation(self, agent_type: str, message: str, context: dict):
        """Handle agent consultation request."""
        try:
            if not self.consultation_manager:
                self.log_signal.emit("❌ Consultation system not available")
                return

            self.log_signal.emit(f"🤔 Consulting with {agent_type.title()} agent...")

            # Start consultation session
            session_id = self.consultation_manager.start_consultation(
                agent_type=agent_type,
                human_participant="User",
                initial_message=message,
                context=context
            )

            if session_id:
                # Get agent response
                response_result = self.consultation_manager.get_agent_response(session_id, message)

                if response_result.get("success"):
                    response = response_result.get("response", "")
                    self.consultation_response_ready.emit(session_id, agent_type, response)
                    self.log_signal.emit(f"💭 {agent_type.title()} agent responded")
                else:
                    self.log_signal.emit(f"❌ {agent_type.title()} consultation failed: {response_result.get('error', 'Unknown error')}")
            else:
                self.log_signal.emit(f"❌ Failed to start consultation with {agent_type}")

        except Exception as e:
            logger.error(f"Agent consultation failed: {e}")
            self.log_signal.emit(f"❌ Consultation error: {str(e)}")

    @pyqtSlot(str, dict)
    def handle_team_brainstorm(self, topic: str, context: dict):
        """Handle team brainstorm request."""
        try:
            if not self.consultation_manager:
                self.log_signal.emit("❌ Consultation system not available")
                return

            self.log_signal.emit(f"🎭 Starting team brainstorm on: {topic}")

            # Start team brainstorm
            result = self.consultation_manager.start_team_brainstorm(
                topic=topic,
                human_participant="User",
                context=context
            )

            if result.get("success"):
                responses = result.get("responses", [])
                session_id = result.get("session_id", "")

                for response_data in responses:
                    agent = response_data.get("agent", "")
                    response = response_data.get("response", "")
                    self.consultation_response_ready.emit(session_id, agent.lower(), response)

                self.log_signal.emit(f"🎉 Team brainstorm complete with {len(responses)} perspectives")
            else:
                self.log_signal.emit(f"❌ Team brainstorm failed: {result.get('error', 'Unknown error')}")

        except Exception as e:
            logger.error(f"Team brainstorm failed: {e}")
            self.log_signal.emit(f"❌ Brainstorm error: {str(e)}")

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status information."""
        status = {
            "initialized": self.workflow_manager is not None,
            "agents_count": 3 if self.coder_agent else 0,
            "tools_count": len(self.tool_registry.list_tool_names()) if self.tool_registry else 0,
            "config_loaded": self.config is not None,
            "memory_systems": {
                "episodic": self.ledger is not None,
                "semantic": hasattr(self, 'vector_store'),
                "procedural": self.lessons_manager is not None
            },
            "strategic_capabilities": {
                "strategic_workflow": hasattr(self, 'workflow_manager') and self.workflow_manager is not None,
                "lessons_learned": self.lessons_manager is not None,
                "whiteboard_session": True  # Built into workflow_manager
            }
        }

        # Add vector store status if available
        if hasattr(self, 'continuous_indexer'):
            status["indexer_status"] = self.continuous_indexer.get_status()

        # Add workflow manager status if available
        if self.workflow_manager:
            status["execution_status"] = self.workflow_manager.get_execution_status()

        return status

    def _on_file_indexed(self, file_path: str):
        """Callback when a file is indexed."""
        self.log_signal.emit(f"Indexed: {file_path}")
        logger.debug(f"File indexed: {file_path}")

    def _on_indexing_complete(self, stats: Dict[str, int]):
        """Callback when indexing batch is complete."""
        self.log_signal.emit(f"Indexing complete: {stats['indexed']} indexed, {stats['skipped']} skipped")
        logger.info(f"Indexing batch complete: {stats}")

    def search_code_context(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Search for code context using semantic similarity.

        Args:
            query: Search query
            k: Number of results to return

        Returns:
            List of matching code contexts
        """
        if not hasattr(self, 'vector_store'):
            return []

        try:
            results = self.vector_store.search_code(query, k=k)
            return [
                {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "file_path": doc.metadata.get("file_path", ""),
                    "file_name": doc.metadata.get("file_name", "")
                }
                for doc in results
            ]
        except Exception as e:
            logger.error(f"Code search failed: {e}")
            return []


def main():
    """Main entry point for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Vibe Coder - AI Coding Assistant")
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--goal", help="Goal to execute")
    parser.add_argument("--description", help="Detailed description of the goal")
    parser.add_argument("--gui", action="store_true", help="Launch GUI interface")
    
    args = parser.parse_args()
    
    if args.gui:
        # Launch GUI interface
        from vibe_coder.ui.main_window import launch_gui
        launch_gui(args.config)
    else:
        # Command-line interface
        backend = AgentBackend(args.config)
        backend.run()
        
        if args.goal:
            backend.submit_goal(args.goal, args.description or "")
            # In CLI mode, we would need to wait for completion
            # For now, just initialize and exit
            print("Goal submitted. Use --gui for interactive mode.")


if __name__ == "__main__":
    main()
