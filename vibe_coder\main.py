"""
Main entry point for the Vibe Coder system.
Contains the AgentBackend and main execution logic.
"""

import logging
import sys
from typing import Dict, Any, Optional, List
from PyQt5.QtCore import QObject, pyqtSignal, pyqtSlot, QThread

from vibe_coder.config import init_config, get_config
from vibe_coder.engine.workflow_manager import WorkflowManager
from vibe_coder.memory.action_ledger import ActionHypothesisLedger
from vibe_coder.memory.vector_store import CodeVectorStore
from vibe_coder.memory.continuous_indexer import ContinuousIndexer
from vibe_coder.memory.lessons_learned import LessonsLearnedManager
from vibe_coder.tools.tool_registry import ToolRegistry
from vibe_coder.tools.code_analysis import CodeAnalysisTool
from vibe_coder.agents.coder_agent import CoderAgent
from vibe_coder.agents.task_decomposer import TaskDecomposerAgent
from vibe_coder.agents.meta_agent import MetaAgentCognoscente

logger = logging.getLogger(__name__)


class AgentBackend(QObject):
    """
    The main agent backend that orchestrates the entire Vibe Coder system.
    Designed to run in a background thread.
    """
    
    # Signals for communication with UI
    log_signal = pyqtSignal(str)
    status_signal = pyqtSignal(str)
    result_signal = pyqtSignal(dict)

    # Signals for receiving commands from UI
    goal_submitted = pyqtSignal(str, str)  # goal, description
    stop_requested = pyqtSignal()  # stop execution

    # Signals for open-source discovery
    discovery_approval_requested = pyqtSignal(list)  # candidates list

    # Signals for strategic consultation
    agent_consultation_requested = pyqtSignal(str, str, dict)  # agent_type, message, context
    team_brainstorm_requested = pyqtSignal(str, dict)  # topic, context
    consultation_response_ready = pyqtSignal(str, str, str)  # session_id, agent_type, response
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the AgentBackend.
        
        Args:
            config_path: Optional path to configuration file
        """
        super().__init__()
        
        # Initialize configuration
        self.config = init_config(config_path)
        
        # Initialize logging
        self._setup_logging()
        
        # Initialize core components
        self.tool_registry = None
        self.code_analyzer = None
        self.ledger = None
        self.lessons_manager = None
        self.workflow_manager = None

        # Initialize agents
        self.coder_agent = None
        self.task_decomposer = None
        self.meta_agent = None

        # Strategic consultation
        self.consultation_manager = None

        # Brainstorming memory for task reflection
        self.brainstorm_memories = []

        # Execution control
        self._stop_requested = False

        # Connect internal signals
        self.goal_submitted.connect(self.submit_goal)
        self.stop_requested.connect(self.stop_execution)

        logger.info("AgentBackend initialized")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = self.config.get("settings.log_level", "INFO")
        log_file = self.config.get("paths.log_file", "./logs/vibe_coder.log")
        
        # Ensure log directory exists
        import os
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger.info(f"Logging configured: level={log_level}, file={log_file}")
    
    def initialize_system(self):
        """Initialize all system components."""
        try:
            self.log_signal.emit("Initializing Vibe Coder system...")
            
            # Ensure required directories exist
            self.config.ensure_directories()
            
            # Initialize tool registry
            self.tool_registry = ToolRegistry()
            self.log_signal.emit(f"Loaded {len(self.tool_registry.list_tool_names())} tools")
            
            # Initialize code analyzer
            self.code_analyzer = CodeAnalysisTool()
            self.log_signal.emit("Code analysis tool initialized")
            
            # Initialize memory system
            ledger_path = self.config.get("paths.action_ledger_db")
            self.ledger = ActionHypothesisLedger(ledger_path)
            self.ledger.setup_database()

            # Initialize vector store for semantic memory
            vector_store_path = self.config.get("paths.vector_store_path", "./data/vector_store")
            self.vector_store = CodeVectorStore(vector_store_path)

            # Initialize lessons learned manager for procedural memory
            lessons_path = self.config.get("paths.lessons_learned_db", "./data/lessons_learned.json")
            self.lessons_manager = LessonsLearnedManager(lessons_path, self.ledger)

            # Initialize continuous indexer
            self.continuous_indexer = ContinuousIndexer(
                vector_store=self.vector_store,
                watch_paths=["."]  # Monitor current project directory
            )

            # Set up indexing callbacks
            self.continuous_indexer.on_file_indexed = self._on_file_indexed
            self.continuous_indexer.on_indexing_complete = self._on_indexing_complete

            self.log_signal.emit("Three-part memory system initialized")
            
            # Initialize agents
            self._initialize_agents()
            
            # Initialize execution engine
            self._initialize_engine()

            # Start continuous indexing
            self.continuous_indexer.start(initial_scan=True)
            self.log_signal.emit("Continuous indexing started")

            self.log_signal.emit("System initialization complete!")
            self.status_signal.emit("Ready")
            
        except Exception as e:
            error_msg = f"System initialization failed: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"ERROR: {error_msg}")
            self.status_signal.emit("Error")
            raise
    
    def _initialize_agents(self):
        """Initialize all agent instances."""
        config_dict = self.config.config

        # Initialize agents with specific names for LLM assignment
        self.coder_agent = CoderAgent(config_dict, self.tool_registry)
        self.coder_agent.agent_name = "coder_agent"

        self.task_decomposer = TaskDecomposerAgent(config_dict)
        self.task_decomposer.agent_name = "task_decomposer"

        self.meta_agent = MetaAgentCognoscente(config_dict, self.ledger)
        self.meta_agent.agent_name = "meta_agent"

        self.log_signal.emit("Agents initialized")
        logger.info("All agents initialized successfully")
    
    def _initialize_engine(self):
        """Initialize the workflow management engine."""
        # Initialize the sophisticated workflow manager
        self.workflow_manager = WorkflowManager(
            config=self.config.config,
            lessons_manager=self.lessons_manager,
            signals_controller=self,  # Pass self for UI signals
            project_state_file=self.config.get("paths.project_state_file", "./data/project_state.json")
        )

        # Initialize strategic consultation manager
        try:
            from vibe_coder.core.consultation.strategic_consultation import StrategicConsultationManager

            self.consultation_manager = StrategicConsultationManager(self.config.config)

            # Initialize Scout agent for research (simplified without models dependency)
            try:
                from vibe_coder.core.planning.open_source_scout import OpenSourceScout
                scout_agent = OpenSourceScout(self.config.config)
                self.scout_agent = scout_agent
            except Exception as scout_error:
                logger.warning(f"Failed to initialize Scout agent: {scout_error}")
                self.scout_agent = None

            self.log_signal.emit("Strategic consultation system ready for brainstorming")
        except Exception as e:
            logger.warning(f"Failed to initialize consultation manager: {e}")
            # Create a simple fallback consultation system
            self.consultation_manager = self._create_fallback_consultation_manager()
            self.scout_agent = None
            if self.consultation_manager:
                self.log_signal.emit("Fallback consultation system ready for brainstorming")

        self.log_signal.emit("Strategic workflow manager initialized")
        logger.info("Workflow management engine ready with strategic capabilities")
    
    def run(self):
        """Main execution loop for the agent backend."""
        try:
            self.log_signal.emit("Starting Vibe Coder agent backend...")
            
            # Initialize the system
            self.initialize_system()
            
            # Connect strategic agents to consultation manager if available
            if self.consultation_manager and self.workflow_manager:
                try:
                    # Get strategic agents from workflow manager
                    strategic_workflow = getattr(self.workflow_manager, 'strategic_workflow', None)
                    if strategic_workflow:
                        whiteboard = getattr(strategic_workflow, 'whiteboard_session', None)
                        if whiteboard:
                            debate = getattr(whiteboard, 'strategic_debate', None)
                            if debate:
                                self.consultation_manager.initialize_agents(
                                    muse_agent=debate.muse,
                                    oracle_agent=debate.oracle,
                                    wisdom_layer=debate.wisdom_layer,
                                    scout_agent=self.scout_agent
                                )
                                self.log_signal.emit("✅ All strategic agents connected to consultation system")
                                self.log_signal.emit("🧠 Ready for team brainstorming with live LLM responses")
                            else:
                                self.log_signal.emit("⚠️ Strategic debate not found in whiteboard session")
                        else:
                            self.log_signal.emit("⚠️ Whiteboard session not found in strategic workflow")
                    else:
                        self.log_signal.emit("⚠️ Strategic workflow not found in workflow manager")
                except Exception as e:
                    logger.warning(f"Failed to connect strategic agents to consultation: {e}")
                    self.log_signal.emit(f"⚠️ Agent connection failed: {e}")
            else:
                self.log_signal.emit("⚠️ Consultation manager or workflow manager not available")

            # The system is now ready and waiting for tasks
            self.log_signal.emit("Agent backend is running and ready for tasks")
            
        except Exception as e:
            error_msg = f"Agent backend failed to start: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"CRITICAL ERROR: {error_msg}")
            self.status_signal.emit("Failed")
    
    @pyqtSlot(str, str)
    def submit_goal(self, goal: str, description: str = "", requirements: list = None):
        """
        Submit a new goal to the system using the strategic workflow sequence.

        This implements the architect's blueprint:
        1. Strategic Planning (WhiteboardSession via StrategicWorkflow)
        2. Task Decomposition (TaskDecomposer with strategic context)
        3. Task Execution (ExecutionWorkflow with Think→Act→Verify→Reflect)

        Args:
            goal: High-level goal description
            description: Detailed description
            requirements: List of specific requirements
        """
        try:
            self.log_signal.emit(f"🎯 Received new goal: {goal}")
            self.log_signal.emit("🏗️ Starting strategic workflow sequence...")

            # Get brainstorming context if available
            brainstorm_context = self.get_brainstorm_context_for_tasks()

            # Create a project charter for strategic planning
            charter = {
                "goal": goal,
                "description": description or goal,
                "requirements": requirements or [],
                "constraints": [],
                "deliverables": [],
                "timestamp": "now",
                "brainstorm_context": brainstorm_context
            }

            # Log brainstorm integration and set technical mode
            if brainstorm_context.get("has_brainstorm_context"):
                self.log_signal.emit("🧠 Integrating brainstorming insights into task planning...")
                insights = brainstorm_context.get("key_insights", [])
                if insights:
                    self.log_signal.emit(f"💡 Key insights: {', '.join(insights[:3])}")
                consensus = brainstorm_context.get("consensus_points", [])
                if consensus:
                    self.log_signal.emit(f"✅ Team consensus: {len(consensus)} points integrated")

                # Set strategic agents to technical mode for implementation planning
                self.log_signal.emit("🔧 Switching to technical implementation planning mode...")
                self._set_agents_technical_mode(True)
                self.log_signal.emit("✅ Strategic agents now focused on technical implementation decisions")
            else:
                # No brainstorm context - still use technical mode for implementation
                self.log_signal.emit("🔧 Setting technical implementation planning mode...")
                self._set_agents_technical_mode(True)
                self.log_signal.emit("✅ Strategic agents focused on technical implementation decisions")

            # Execute the full strategic workflow sequence
            self.log_signal.emit("📋 Phase 1: Strategic Planning & Debate")
            result = self.workflow_manager.execute_goal(charter)

            # Emit the comprehensive result
            self.result_signal.emit(result)

            if result.get("success"):
                self.log_signal.emit("✅ Goal execution completed successfully!")
                phases = result.get("phases_completed", [])
                self.log_signal.emit(f"📊 Completed phases: {', '.join(phases)}")
            else:
                self.log_signal.emit(f"❌ Goal execution failed in {result.get('phase', 'unknown')} phase")

        except Exception as e:
            error_msg = f"Goal submission failed: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"🚨 ERROR: {error_msg}")
            self.result_signal.emit({
                "success": False,
                "error": error_msg,
                "phase": "goal_submission"
            })

    @pyqtSlot()
    def stop_execution(self):
        """Stop the current execution."""
        self._stop_requested = True
        self.log_signal.emit("🛑 Stop requested - halting execution...")

        # Signal workflow manager to stop if available
        if self.workflow_manager:
            self.workflow_manager.stop_execution()

    @pyqtSlot(str, str, dict)
    def handle_agent_consultation(self, agent_type: str, message: str, context: dict):
        """Handle agent consultation request."""
        try:
            if not self.consultation_manager:
                self.log_signal.emit("❌ Consultation system not available")
                return

            self.log_signal.emit(f"🤔 Consulting with {agent_type.title()} agent...")

            # Start consultation session
            session_id = self.consultation_manager.start_consultation(
                agent_type=agent_type,
                human_participant="User",
                initial_message=message,
                context=context
            )

            if session_id:
                # Get agent response
                response_result = self.consultation_manager.get_agent_response(session_id, message)

                if response_result.get("success"):
                    response = response_result.get("response", "")
                    self.consultation_response_ready.emit(session_id, agent_type, response)
                    self.log_signal.emit(f"💭 {agent_type.title()} agent responded")
                else:
                    self.log_signal.emit(f"❌ {agent_type.title()} consultation failed: {response_result.get('error', 'Unknown error')}")
            else:
                self.log_signal.emit(f"❌ Failed to start consultation with {agent_type}")

        except Exception as e:
            logger.error(f"Agent consultation failed: {e}")
            self.log_signal.emit(f"❌ Consultation error: {str(e)}")

    @pyqtSlot(str, dict)
    def handle_team_brainstorm(self, message: str, context: dict):
        """Handle team brainstorm request."""
        try:
            if not self.consultation_manager:
                self.log_signal.emit("❌ Consultation system not available")
                return

            session_active = context.get("session_active", False)
            if session_active:
                self.log_signal.emit(f"💬 Team responding to: {message[:50]}...")
            else:
                self.log_signal.emit(f"🎭 Starting team brainstorm: {message[:50]}...")

            # Start team brainstorm
            result = self.consultation_manager.start_team_brainstorm(
                message=message,
                human_participant="User",
                context=context
            )

            if result.get("success"):
                responses = result.get("responses", [])
                session_id = result.get("session_id", "")

                for response_data in responses:
                    agent = response_data.get("agent", "")
                    response = response_data.get("response", "")
                    self.consultation_response_ready.emit(session_id, agent.lower(), response)

                if session_active:
                    self.log_signal.emit(f"💭 Team responded with {len(responses)} perspectives")
                else:
                    self.log_signal.emit(f"🎉 Team brainstorm started with {len(responses)} perspectives")
            else:
                self.log_signal.emit(f"❌ Team brainstorm failed: {result.get('error', 'Unknown error')}")

        except Exception as e:
            logger.error(f"Team brainstorm failed: {e}")
            self.log_signal.emit(f"❌ Brainstorm error: {str(e)}")

    def store_brainstorm_memory(self, brainstorm_memory: Dict[str, Any]):
        """Store brainstorming memory for task reflection."""
        try:
            self.brainstorm_memories.append(brainstorm_memory)

            # Also store in lessons learned for long-term memory
            if self.lessons_manager:
                lesson = {
                    "context": "brainstorming_session",
                    "action": f"Team brainstorm with {len(brainstorm_memory.get('participants', []))} agents",
                    "outcome": "successful",
                    "lesson": f"Brainstorming session generated {len(brainstorm_memory.get('key_topics', []))} key topics and {len(brainstorm_memory.get('consensus_points', []))} consensus points",
                    "confidence": 0.8,
                    "metadata": {
                        "session_id": brainstorm_memory.get("session_id", ""),
                        "participants": brainstorm_memory.get("participants", []),
                        "key_topics": brainstorm_memory.get("key_topics", [])
                    }
                }
                self.lessons_manager.add_lesson(lesson)

            self.log_signal.emit(f"💾 Brainstorm memory stored for task planning reflection")
            logger.info(f"Stored brainstorm memory: {brainstorm_memory.get('session_id', 'unknown')}")

        except Exception as e:
            logger.error(f"Failed to store brainstorm memory: {e}")

    def get_brainstorm_context_for_tasks(self) -> Dict[str, Any]:
        """Get brainstorming context for task planning."""
        if not self.brainstorm_memories:
            return {}

        # Get the most recent brainstorm memory
        recent_memory = self.brainstorm_memories[-1]

        return {
            "has_brainstorm_context": True,
            "recent_session": recent_memory.get("session_id", ""),
            "key_insights": recent_memory.get("key_topics", [])[:5],
            "consensus_points": recent_memory.get("consensus_points", [])[:3],
            "open_questions": recent_memory.get("open_questions", [])[:3],
            "participants": recent_memory.get("participants", [])
        }

    def _set_agents_technical_mode(self, technical: bool = True):
        """Set strategic agents to technical implementation mode."""
        try:
            if self.workflow_manager and hasattr(self.workflow_manager, 'strategic_workflow'):
                strategic_workflow = self.workflow_manager.strategic_workflow
                if hasattr(strategic_workflow, 'whiteboard_session'):
                    whiteboard = strategic_workflow.whiteboard_session
                    if hasattr(whiteboard, 'strategic_debate'):
                        whiteboard.strategic_debate.set_technical_mode(technical)
                        mode_name = "technical implementation" if technical else "conceptual ideation"
                        logger.info(f"Strategic agents set to {mode_name} mode")
                        return True

            logger.warning("Could not access strategic agents to set technical mode")
            return False

        except Exception as e:
            logger.error(f"Failed to set agents technical mode: {e}")
            return False

    def _create_fallback_consultation_manager(self):
        """Create a simple fallback consultation manager when the full system fails."""
        try:
            class FallbackConsultationManager:
                def __init__(self, backend):
                    self.backend = backend
                    self.active_sessions = {}
                    self.session_counter = 0

                def start_consultation(self, agent_type, human_participant, initial_message, context):
                    """Start a consultation session."""
                    self.session_counter += 1
                    session_id = f"fallback_session_{self.session_counter}"
                    self.active_sessions[session_id] = {
                        "agent_type": agent_type,
                        "participant": human_participant,
                        "messages": [initial_message]
                    }
                    return session_id

                def get_agent_response(self, session_id, message):
                    """Get agent response using strategic agents from workflow."""
                    try:
                        session = self.active_sessions.get(session_id)
                        if not session:
                            return {"success": False, "error": "Session not found"}

                        agent_type = session["agent_type"]

                        # Get strategic agents from workflow manager
                        if self.backend.workflow_manager:
                            strategic_workflow = getattr(self.backend.workflow_manager, 'strategic_workflow', None)
                            if strategic_workflow:
                                whiteboard = getattr(strategic_workflow, 'whiteboard_session', None)
                                if whiteboard:
                                    debate = getattr(whiteboard, 'strategic_debate', None)
                                    if debate:
                                        # Get the appropriate agent
                                        if agent_type == "muse":
                                            result = debate.muse.brainstorm_solutions(
                                                problem=message,
                                                constraints_to_ignore=["conventional approaches"]
                                            )
                                            if result.get("success") and result.get("solutions"):
                                                solutions = result["solutions"][:3]
                                                response = "Here are some creative ideas I'm excited about:\n\n"
                                                for i, solution in enumerate(solutions, 1):
                                                    response += f"{i}. {solution.get('title', 'Creative approach')}: {solution.get('description', 'Innovative solution')}\n\n"
                                                return {"success": True, "response": response}

                                        elif agent_type == "oracle":
                                            result = debate.oracle.assess_risks(
                                                proposal={"description": message},
                                                historical_data=[]
                                            )
                                            if result.get("success") and result.get("identified_risks"):
                                                risks = result["identified_risks"][:3]
                                                response = "Based on my analysis, here are the key considerations:\n\n"
                                                for i, risk in enumerate(risks, 1):
                                                    response += f"{i}. {risk.get('risk', 'Consideration')}: {risk.get('impact', 'Potential impact to consider')}\n\n"
                                                return {"success": True, "response": response}

                                        elif agent_type == "wisdom":
                                            result = debate.wisdom_layer.synthesize_wisdom(
                                                muse_proposal={"description": message},
                                                oracle_assessment={"considerations": "Strategic consultation request"}
                                            )
                                            if result.get("success") and result.get("final_recommendation"):
                                                return {"success": True, "response": result["final_recommendation"]}

                                        elif agent_type == "scout":
                                            response = f"""I've researched '{message}' and here's what I found:

🔍 **Research Insights:**
- This type of project is commonly implemented using modern frameworks
- Key considerations include user experience, scalability, and maintainability
- Popular approaches often involve modular architecture and clean separation of concerns

💡 **Recommendations:**
- Look into established patterns and best practices for this domain
- Consider existing libraries and frameworks that could accelerate development
- Plan for future extensibility and maintenance

🌐 **Market Context:**
- Similar solutions exist but there's always room for innovation
- Focus on unique value propositions and user needs
- Consider both technical excellence and user experience"""
                                            return {"success": True, "response": response}

                        # Fallback response
                        return {"success": True, "response": f"I'm thinking about your message: '{message}'. Let me provide some insights..."}

                    except Exception as e:
                        return {"success": False, "error": str(e)}

                def start_team_brainstorm(self, message, human_participant, context):
                    """Start team brainstorm with all agents."""
                    try:
                        session_id = self.start_consultation("team", human_participant, message, context)
                        responses = []

                        # Get responses from all agents
                        for agent_type in ["muse", "oracle", "wisdom", "scout"]:
                            result = self.get_agent_response(session_id, message)
                            if result.get("success"):
                                responses.append({
                                    "agent": agent_type,
                                    "response": result["response"]
                                })

                        return {
                            "success": True,
                            "session_id": session_id,
                            "responses": responses
                        }

                    except Exception as e:
                        return {"success": False, "error": str(e)}

                def initialize_agents(self, **kwargs):
                    """Initialize agents (no-op for fallback)."""
                    pass

            return FallbackConsultationManager(self)

        except Exception as e:
            logger.error(f"Failed to create fallback consultation manager: {e}")
            return None

    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status information."""
        status = {
            "initialized": self.workflow_manager is not None,
            "agents_count": 3 if self.coder_agent else 0,
            "tools_count": len(self.tool_registry.list_tool_names()) if self.tool_registry else 0,
            "config_loaded": self.config is not None,
            "memory_systems": {
                "episodic": self.ledger is not None,
                "semantic": hasattr(self, 'vector_store'),
                "procedural": self.lessons_manager is not None
            },
            "strategic_capabilities": {
                "strategic_workflow": hasattr(self, 'workflow_manager') and self.workflow_manager is not None,
                "lessons_learned": self.lessons_manager is not None,
                "whiteboard_session": True  # Built into workflow_manager
            }
        }

        # Add vector store status if available
        if hasattr(self, 'continuous_indexer'):
            status["indexer_status"] = self.continuous_indexer.get_status()

        # Add workflow manager status if available
        if self.workflow_manager:
            status["execution_status"] = self.workflow_manager.get_execution_status()

        return status

    def _on_file_indexed(self, file_path: str):
        """Callback when a file is indexed."""
        self.log_signal.emit(f"Indexed: {file_path}")
        logger.debug(f"File indexed: {file_path}")

    def _on_indexing_complete(self, stats: Dict[str, int]):
        """Callback when indexing batch is complete."""
        self.log_signal.emit(f"Indexing complete: {stats['indexed']} indexed, {stats['skipped']} skipped")
        logger.info(f"Indexing batch complete: {stats}")

    def search_code_context(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Search for code context using semantic similarity.

        Args:
            query: Search query
            k: Number of results to return

        Returns:
            List of matching code contexts
        """
        if not hasattr(self, 'vector_store'):
            return []

        try:
            results = self.vector_store.search_code(query, k=k)
            return [
                {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "file_path": doc.metadata.get("file_path", ""),
                    "file_name": doc.metadata.get("file_name", "")
                }
                for doc in results
            ]
        except Exception as e:
            logger.error(f"Code search failed: {e}")
            return []


def main():
    """Main entry point for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Vibe Coder - AI Coding Assistant")
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--goal", help="Goal to execute")
    parser.add_argument("--description", help="Detailed description of the goal")
    parser.add_argument("--gui", action="store_true", help="Launch GUI interface")
    
    args = parser.parse_args()
    
    if args.gui:
        # Launch GUI interface
        from vibe_coder.ui.main_window import launch_gui
        launch_gui(args.config)
    else:
        # Command-line interface
        backend = AgentBackend(args.config)
        backend.run()
        
        if args.goal:
            backend.submit_goal(args.goal, args.description or "")
            # In CLI mode, we would need to wait for completion
            # For now, just initialize and exit
            print("Goal submitted. Use --gui for interactive mode.")


if __name__ == "__main__":
    main()
