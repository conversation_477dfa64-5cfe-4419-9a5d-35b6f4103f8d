"""
Main entry point for the Vibe Coder system.
Contains the AgentBackend and main execution logic.
"""

import logging
import sys
from typing import Dict, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QThread

from vibe_coder.config import init_config, get_config
from vibe_coder.engine.simple_driver import SimpleDriver
from vibe_coder.memory.action_ledger import ActionHypothesisLedger
from vibe_coder.tools.tool_registry import ToolRegistry
from vibe_coder.tools.code_analysis import CodeAnalysisTool
from vibe_coder.agents.coder_agent import CoderAgent
from vibe_coder.agents.task_decomposer import TaskDecomposerAgent
from vibe_coder.agents.meta_agent import MetaAgentCognoscente

logger = logging.getLogger(__name__)


class AgentBackend(QObject):
    """
    The main agent backend that orchestrates the entire Vibe Coder system.
    Designed to run in a background thread.
    """
    
    # Signals for communication with UI
    log_signal = pyqtSignal(str)
    status_signal = pyqtSignal(str)
    result_signal = pyqtSignal(dict)
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the AgentBackend.
        
        Args:
            config_path: Optional path to configuration file
        """
        super().__init__()
        
        # Initialize configuration
        self.config = init_config(config_path)
        
        # Initialize logging
        self._setup_logging()
        
        # Initialize core components
        self.tool_registry = None
        self.code_analyzer = None
        self.ledger = None
        self.driver = None
        
        # Initialize agents
        self.coder_agent = None
        self.task_decomposer = None
        self.meta_agent = None
        
        logger.info("AgentBackend initialized")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = self.config.get("settings.log_level", "INFO")
        log_file = self.config.get("paths.log_file", "./logs/vibe_coder.log")
        
        # Ensure log directory exists
        import os
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger.info(f"Logging configured: level={log_level}, file={log_file}")
    
    def initialize_system(self):
        """Initialize all system components."""
        try:
            self.log_signal.emit("Initializing Vibe Coder system...")
            
            # Ensure required directories exist
            self.config.ensure_directories()
            
            # Initialize tool registry
            self.tool_registry = ToolRegistry()
            self.log_signal.emit(f"Loaded {len(self.tool_registry.list_tool_names())} tools")
            
            # Initialize code analyzer
            self.code_analyzer = CodeAnalysisTool()
            self.log_signal.emit("Code analysis tool initialized")
            
            # Initialize memory system
            ledger_path = self.config.get("paths.action_ledger_db")
            self.ledger = ActionHypothesisLedger(ledger_path)
            self.ledger.setup_database()
            self.log_signal.emit("Memory system initialized")
            
            # Initialize agents
            self._initialize_agents()
            
            # Initialize execution engine
            self._initialize_engine()
            
            self.log_signal.emit("System initialization complete!")
            self.status_signal.emit("Ready")
            
        except Exception as e:
            error_msg = f"System initialization failed: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"ERROR: {error_msg}")
            self.status_signal.emit("Error")
            raise
    
    def _initialize_agents(self):
        """Initialize all agent instances."""
        config_dict = self.config.config

        # Initialize agents with specific names for LLM assignment
        self.coder_agent = CoderAgent(config_dict, self.tool_registry)
        self.coder_agent.agent_name = "coder_agent"

        self.task_decomposer = TaskDecomposerAgent(config_dict)
        self.task_decomposer.agent_name = "task_decomposer"

        self.meta_agent = MetaAgentCognoscente(config_dict, self.ledger)
        self.meta_agent.agent_name = "meta_agent"

        self.log_signal.emit("Agents initialized")
        logger.info("All agents initialized successfully")
    
    def _initialize_engine(self):
        """Initialize the execution engine."""
        # Create agent definitions for the simple driver
        agent_definitions = {
            "coder_node": self.coder_agent.execute_task,
            "decomposer_node": self.task_decomposer.execute_task,
            "meta_node": self.meta_agent.execute_task,
        }

        # Initialize simple driver
        self.driver = SimpleDriver(agent_definitions, self.ledger)

        self.log_signal.emit("Execution engine initialized")
        logger.info("Execution engine ready")
    
    def run(self):
        """Main execution loop for the agent backend."""
        try:
            self.log_signal.emit("Starting Vibe Coder agent backend...")
            
            # Initialize the system
            self.initialize_system()
            
            # The system is now ready and waiting for tasks
            self.log_signal.emit("Agent backend is running and ready for tasks")
            
        except Exception as e:
            error_msg = f"Agent backend failed to start: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"CRITICAL ERROR: {error_msg}")
            self.status_signal.emit("Failed")
    
    def submit_goal(self, goal: str, description: str = "", requirements: list = None):
        """
        Submit a new goal to the system.
        
        Args:
            goal: High-level goal description
            description: Detailed description
            requirements: List of specific requirements
        """
        try:
            self.log_signal.emit(f"Received new goal: {goal}")
            
            # Create a project charter
            from vibe_coder.data_models.project_models import ProjectCharter
            
            charter = ProjectCharter(
                goal=goal,
                description=description or goal,
                requirements=requirements or [],
                constraints=[],
                deliverables=[]
            )
            
            # Submit to the driver for execution
            result = self.driver.execute_project(charter)
            
            # Emit the result
            self.result_signal.emit(result)
            
        except Exception as e:
            error_msg = f"Goal submission failed: {str(e)}"
            logger.error(error_msg)
            self.log_signal.emit(f"ERROR: {error_msg}")
            self.result_signal.emit({
                "success": False,
                "error": error_msg
            })
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status information."""
        return {
            "initialized": self.driver is not None,
            "agents_count": 3 if self.coder_agent else 0,
            "tools_count": len(self.tool_registry.list_tool_names()) if self.tool_registry else 0,
            "config_loaded": self.config is not None,
            "memory_ready": self.ledger is not None
        }


def main():
    """Main entry point for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Vibe Coder - AI Coding Assistant")
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--goal", help="Goal to execute")
    parser.add_argument("--description", help="Detailed description of the goal")
    parser.add_argument("--gui", action="store_true", help="Launch GUI interface")
    
    args = parser.parse_args()
    
    if args.gui:
        # Launch GUI interface
        from vibe_coder.ui.main_window import launch_gui
        launch_gui(args.config)
    else:
        # Command-line interface
        backend = AgentBackend(args.config)
        backend.run()
        
        if args.goal:
            backend.submit_goal(args.goal, args.description or "")
            # In CLI mode, we would need to wait for completion
            # For now, just initialize and exit
            print("Goal submitted. Use --gui for interactive mode.")


if __name__ == "__main__":
    main()
