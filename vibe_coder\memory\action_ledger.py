import sqlite3
import json
import logging
from typing import List, Optional, Any, Dict, Union
from pathlib import Path

# Assuming HypothesisLogEntry is defined in cognitive_models
# We need to ensure it can be easily serialized/deserialized if complex objects are stored.
# For SQLite, we'll store most fields as TEXT or INTEGER.
from vibe_coder.data_models.cognitive_models import HypothesisLogEntry
import datetime # Required for HypothesisLogEntry

logger = logging.getLogger(__name__)

class ActionHypothesisLedger:
    """
    Manages the SQLite database for the episodic memory (Action-Hypothesis Ledger).
    Stores and retrieves HypothesisLogEntry objects.
    """
    def __init__(self, db_path: Union[str, Path]):
        """
        Initializes the ledger with the path to the SQLite database.

        Args:
            db_path: Path to the SQLite database file.
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True) # Ensure directory exists
        self._db_connection: Optional[sqlite3.Connection] = None
        self.setup_database()

    def _get_connection(self) -> sqlite3.Connection:
        """Establishes and returns a database connection."""
        if self._db_connection is None:
            try:
                self._db_connection = sqlite3.connect(self.db_path, detect_types=sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES)
                self._db_connection.row_factory = sqlite3.Row # Access columns by name
                logger.info(f"Database connection established to {self.db_path}")
            except sqlite3.Error as e:
                logger.error(f"Error connecting to database {self.db_path}: {e}")
                raise
        return self._db_connection

    def close_connection(self):
        """Closes the database connection if it's open."""
        if self._db_connection:
            self._db_connection.close()
            self._db_connection = None
            logger.info(f"Database connection to {self.db_path} closed.")

    def setup_database(self):
        """
        Creates the 'hypothesis_log' table if it doesn't exist.
        The schema is based on the HypothesisLogEntry Pydantic model.
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS hypothesis_log (
                    entry_id TEXT PRIMARY KEY,
                    task_id TEXT NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    agent_name TEXT NOT NULL,
                    hypothesis_text TEXT NOT NULL,
                    action_taken TEXT NOT NULL,       -- Could be JSON string for complex actions
                    action_result_summary TEXT,
                    is_success INTEGER                -- 0 for False, 1 for True, NULL if not set
                )
            """)
            conn.commit()
            logger.info("Database setup complete. 'hypothesis_log' table ensured.")
        except sqlite3.Error as e:
            logger.error(f"Error setting up database table: {e}")
            raise
        # No finally block to close cursor here, as _get_connection manages the connection lifecycle
        # for the instance. Connection is closed explicitly via close_connection().

    def add_entry(self, entry: HypothesisLogEntry) -> None:
        """
        Inserts a new HypothesisLogEntry into the database.

        Args:
            entry: The HypothesisLogEntry object to add.
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        
        # Convert boolean is_success to INTEGER for SQLite
        is_success_int: Optional[int] = None
        if entry.is_success is not None:
            is_success_int = 1 if entry.is_success else 0

        sql = """
            INSERT INTO hypothesis_log (
                entry_id, task_id, timestamp, agent_name, 
                hypothesis_text, action_taken, action_result_summary, is_success
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        try:
            cursor.execute(sql, (
                entry.entry_id,
                entry.task_id,
                entry.timestamp, # Pydantic datetime should be compatible
                entry.agent_name,
                entry.hypothesis_text,
                entry.action_taken, # Assuming this is a string or simple serializable
                entry.action_result_summary,
                is_success_int
            ))
            conn.commit()
            logger.debug(f"Added log entry with ID: {entry.entry_id} for task: {entry.task_id}")
        except sqlite3.IntegrityError as e: # E.g. PRIMARY KEY constraint failed
            logger.error(f"Failed to add log entry {entry.entry_id} due to integrity error (possibly duplicate ID): {e}")
            raise
        except sqlite3.Error as e:
            logger.error(f"Database error adding log entry {entry.entry_id}: {e}")
            raise

    def get_history_for_task(self, task_id: str) -> List[HypothesisLogEntry]:
        """
        Queries the database for all entries related to a specific task_id,
        ordered by timestamp.

        Args:
            task_id: The ID of the task to retrieve history for.

        Returns:
            A list of HypothesisLogEntry objects.
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        sql = "SELECT * FROM hypothesis_log WHERE task_id = ? ORDER BY timestamp ASC"
        
        entries: List[HypothesisLogEntry] = []
        try:
            cursor.execute(sql, (task_id,))
            rows = cursor.fetchall()
            for row in rows:
                # Convert row (sqlite3.Row) to a dictionary
                row_dict = dict(row)
                
                # Convert is_success from INTEGER back to bool or None
                if 'is_success' in row_dict and row_dict['is_success'] is not None:
                    row_dict['is_success'] = bool(row_dict['is_success'])
                else:
                    row_dict['is_success'] = None # Ensure it's None if NULL in DB

                # Pydantic will handle timestamp conversion if it's a string from DB,
                # but sqlite3.PARSE_DECLTYPES should handle it.
                # Ensure timestamp is datetime object
                if isinstance(row_dict.get('timestamp'), str):
                     row_dict['timestamp'] = datetime.datetime.fromisoformat(row_dict['timestamp'])
                
                entries.append(HypothesisLogEntry(**row_dict))
            logger.debug(f"Retrieved {len(entries)} log entries for task ID: {task_id}")
        except sqlite3.Error as e:
            logger.error(f"Database error retrieving history for task {task_id}: {e}")
            raise
        return entries

    def __enter__(self):
        self._get_connection()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close_connection()


# This module is designed to be imported and used by other components.
# For testing, use the test files in the tests/ directory.

