"""
Lessons Learned: Procedural memory system for synthesizing experience into wisdom.
Transforms raw action-hypothesis entries into reusable patterns and insights.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
from vibe_coder.memory.action_ledger import ActionHypothesisLedger, HypothesisLogEntry

logger = logging.getLogger(__name__)


class LessonsLearnedManager:
    """
    Manages procedural memory through lessons learned synthesis.
    
    This is the third part of the three-part memory architecture:
    - Semantic Memory: Vector database for code context
    - Episodic Memory: Action-Hypothesis Ledger for experience tracking
    - Procedural Memory: Lessons Learned for wisdom synthesis
    """
    
    def __init__(self, lessons_db_path: str, action_ledger: ActionHypothesisLedger):
        """
        Initialize the Lessons Learned Manager.
        
        Args:
            lessons_db_path: Path to the lessons database file
            action_ledger: Reference to the action-hypothesis ledger
        """
        self.lessons_db_path = Path(lessons_db_path)
        self.action_ledger = action_ledger
        self.lessons_db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.lessons = self._load_lessons()
        logger.info(f"LessonsLearnedManager initialized with {len(self.lessons)} lessons")
    
    def _load_lessons(self) -> List[Dict[str, Any]]:
        """Load existing lessons from disk."""
        if self.lessons_db_path.exists():
            try:
                with open(self.lessons_db_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load lessons: {e}")
        return []
    
    def _save_lessons(self):
        """Save lessons to disk."""
        try:
            with open(self.lessons_db_path, 'w') as f:
                json.dump(self.lessons, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save lessons: {e}")
    
    def synthesize_new_lessons(self, min_pattern_frequency: int = 3) -> List[Dict[str, Any]]:
        """
        Synthesize new lessons from recent action-hypothesis entries.
        
        Args:
            min_pattern_frequency: Minimum frequency for a pattern to become a lesson
            
        Returns:
            List of newly synthesized lessons
        """
        logger.info("Starting lesson synthesis from action-hypothesis ledger")
        
        # Get recent entries from action ledger
        recent_entries = self.action_ledger.get_recent_entries(limit=100)
        
        if not recent_entries:
            logger.info("No recent entries found for lesson synthesis")
            return []
        
        # Analyze patterns
        patterns = self._analyze_patterns(recent_entries)
        
        # Generate lessons from patterns
        new_lessons = []
        for pattern in patterns:
            if pattern['frequency'] >= min_pattern_frequency:
                lesson = self._generate_lesson_from_pattern(pattern)
                if lesson:
                    new_lessons.append(lesson)
        
        # Add to lessons database
        self.lessons.extend(new_lessons)
        self._save_lessons()
        
        logger.info(f"Synthesized {len(new_lessons)} new lessons from {len(recent_entries)} entries")
        return new_lessons
    
    def _analyze_patterns(self, entries: List[HypothesisLogEntry]) -> List[Dict[str, Any]]:
        """
        Analyze patterns in action-hypothesis entries.
        
        Args:
            entries: List of hypothesis log entries to analyze
            
        Returns:
            List of identified patterns
        """
        # Group by action types
        action_patterns = {}
        failure_patterns = {}
        
        for entry in entries:
            action = entry.action_taken
            success = entry.is_success
            
            # Track action patterns
            if action not in action_patterns:
                action_patterns[action] = {'success': 0, 'failure': 0, 'contexts': []}
            
            if success:
                action_patterns[action]['success'] += 1
            else:
                action_patterns[action]['failure'] += 1
                # Track failure contexts
                if action not in failure_patterns:
                    failure_patterns[action] = []
                failure_patterns[action].append(entry.result_data)
            
            action_patterns[action]['contexts'].append(entry.hypothesis_text)
        
        # Convert to pattern objects
        patterns = []
        for action, data in action_patterns.items():
            total = data['success'] + data['failure']
            success_rate = data['success'] / total if total > 0 else 0
            
            patterns.append({
                'action': action,
                'frequency': total,
                'success_rate': success_rate,
                'failure_contexts': failure_patterns.get(action, []),
                'common_contexts': self._find_common_contexts(data['contexts'])
            })
        
        return patterns
    
    def _find_common_contexts(self, contexts: List[str]) -> List[str]:
        """
        Find common themes in contexts using simple keyword extraction.
        
        Args:
            contexts: List of context strings
            
        Returns:
            List of common keywords/themes
        """
        word_freq = {}
        for context in contexts:
            words = context.lower().split()
            for word in words:
                if len(word) > 3:  # Filter short words
                    word_freq[word] = word_freq.get(word, 0) + 1
        
        # Return most common words
        return sorted(word_freq.keys(), key=lambda x: word_freq[x], reverse=True)[:5]
    
    def _generate_lesson_from_pattern(self, pattern: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Generate a lesson from an analyzed pattern.
        
        Args:
            pattern: Pattern dictionary containing analysis results
            
        Returns:
            Lesson dictionary or None if pattern doesn't warrant a lesson
        """
        if pattern['frequency'] < 3:
            return None
        
        lesson_type = "best_practice" if pattern['success_rate'] > 0.8 else "anti_pattern"
        
        lesson = {
            "id": f"lesson_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{pattern['action']}",
            "type": lesson_type,
            "action": pattern['action'],
            "success_rate": pattern['success_rate'],
            "frequency": pattern['frequency'],
            "description": self._generate_lesson_description(pattern),
            "contexts": pattern['common_contexts'],
            "created_at": datetime.now().isoformat(),
            "confidence": self._calculate_confidence(pattern),
            "failure_examples": pattern['failure_contexts'][:3] if pattern['failure_contexts'] else []
        }
        
        return lesson
    
    def _generate_lesson_description(self, pattern: Dict[str, Any]) -> str:
        """Generate human-readable lesson description."""
        action = pattern['action']
        success_rate = pattern['success_rate']
        frequency = pattern['frequency']
        
        if success_rate > 0.8:
            return f"Action '{action}' has high success rate ({success_rate:.1%}) across {frequency} attempts. Consider this a reliable approach."
        elif success_rate < 0.3:
            return f"Action '{action}' has low success rate ({success_rate:.1%}) across {frequency} attempts. Avoid or modify this approach."
        else:
            return f"Action '{action}' has mixed results ({success_rate:.1%}) across {frequency} attempts. Context-dependent effectiveness."
    
    def _calculate_confidence(self, pattern: Dict[str, Any]) -> float:
        """
        Calculate confidence score for a lesson.
        
        Args:
            pattern: Pattern data
            
        Returns:
            Confidence score between 0 and 1
        """
        frequency_score = min(pattern['frequency'] / 10, 1.0)  # Max at 10 occurrences
        
        # Higher confidence for extreme success rates
        success_rate = pattern['success_rate']
        extremity_score = abs(success_rate - 0.5) * 2  # 0 at 50%, 1 at 0% or 100%
        
        return (frequency_score + extremity_score) / 2
    
    def get_relevant_lessons(self, context: str, action: str = None) -> List[Dict[str, Any]]:
        """
        Get lessons relevant to current context and action.
        
        Args:
            context: Current context description
            action: Optional specific action being considered
            
        Returns:
            List of relevant lessons sorted by relevance
        """
        relevant_lessons = []
        
        context_words = set(context.lower().split())
        
        for lesson in self.lessons:
            relevance_score = 0
            
            # Check action match
            if action and lesson['action'] == action:
                relevance_score += 0.5
            
            # Check context overlap
            lesson_contexts = set(lesson.get('contexts', []))
            overlap = len(context_words.intersection(lesson_contexts))
            if overlap > 0:
                relevance_score += overlap / len(lesson_contexts)
            
            if relevance_score > 0.3:  # Threshold for relevance
                lesson_copy = lesson.copy()
                lesson_copy['relevance_score'] = relevance_score
                relevant_lessons.append(lesson_copy)
        
        # Sort by relevance and confidence
        relevant_lessons.sort(key=lambda x: x['relevance_score'] * x['confidence'], reverse=True)
        
        return relevant_lessons[:5]  # Return top 5 most relevant
    
    def get_lessons_by_type(self, lesson_type: str) -> List[Dict[str, Any]]:
        """
        Get lessons by type (best_practice or anti_pattern).
        
        Args:
            lesson_type: Type of lessons to retrieve
            
        Returns:
            List of lessons of the specified type
        """
        return [lesson for lesson in self.lessons if lesson.get('type') == lesson_type]
    
    def get_high_confidence_lessons(self, min_confidence: float = 0.8) -> List[Dict[str, Any]]:
        """
        Get lessons with high confidence scores.
        
        Args:
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of high-confidence lessons
        """
        return [lesson for lesson in self.lessons if lesson.get('confidence', 0) >= min_confidence]

    def get_stats(self) -> Dict[str, Any]:
        """
        Get statistics about lessons learned.

        Returns:
            Dictionary containing lesson statistics
        """
        if not self.lessons:
            return {"total_lessons": 0}

        lesson_types = {}
        confidence_scores = []

        for lesson in self.lessons:
            lesson_type = lesson.get('type', 'unknown')
            lesson_types[lesson_type] = lesson_types.get(lesson_type, 0) + 1
            confidence_scores.append(lesson.get('confidence', 0))

        return {
            "total_lessons": len(self.lessons),
            "lesson_types": lesson_types,
            "average_confidence": sum(confidence_scores) / len(confidence_scores),
            "high_confidence_lessons": len([l for l in self.lessons if l.get('confidence', 0) > 0.8]),
            "best_practices": len(self.get_lessons_by_type("best_practice")),
            "anti_patterns": len(self.get_lessons_by_type("anti_pattern"))
        }

    def export_lessons(self, export_path: str) -> bool:
        """
        Export lessons to a file for backup or sharing.

        Args:
            export_path: Path to export the lessons

        Returns:
            True if export successful, False otherwise
        """
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "total_lessons": len(self.lessons),
                "lessons": self.lessons,
                "stats": self.get_stats()
            }

            with open(export_path, 'w') as f:
                json.dump(export_data, f, indent=2)

            logger.info(f"Exported {len(self.lessons)} lessons to {export_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to export lessons: {e}")
            return False

    def import_lessons(self, import_path: str, merge: bool = True) -> bool:
        """
        Import lessons from a file.

        Args:
            import_path: Path to import lessons from
            merge: If True, merge with existing lessons; if False, replace

        Returns:
            True if import successful, False otherwise
        """
        try:
            with open(import_path, 'r') as f:
                import_data = json.load(f)

            imported_lessons = import_data.get('lessons', [])

            if merge:
                # Merge with existing lessons, avoiding duplicates
                existing_ids = {lesson.get('id') for lesson in self.lessons}
                new_lessons = [lesson for lesson in imported_lessons
                              if lesson.get('id') not in existing_ids]
                self.lessons.extend(new_lessons)
                logger.info(f"Merged {len(new_lessons)} new lessons from {import_path}")
            else:
                # Replace existing lessons
                self.lessons = imported_lessons
                logger.info(f"Replaced lessons with {len(imported_lessons)} lessons from {import_path}")

            self._save_lessons()
            return True

        except Exception as e:
            logger.error(f"Failed to import lessons: {e}")
            return False

    def cleanup_old_lessons(self, max_age_days: int = 90) -> int:
        """
        Clean up old lessons that are no longer relevant.

        Args:
            max_age_days: Maximum age in days for lessons to keep

        Returns:
            Number of lessons removed
        """
        from datetime import timedelta

        cutoff_date = datetime.now() - timedelta(days=max_age_days)

        original_count = len(self.lessons)

        # Keep lessons that are either recent or high-confidence
        self.lessons = [
            lesson for lesson in self.lessons
            if (datetime.fromisoformat(lesson.get('created_at', '1970-01-01')) > cutoff_date
                or lesson.get('confidence', 0) > 0.8)
        ]

        removed_count = original_count - len(self.lessons)

        if removed_count > 0:
            self._save_lessons()
            logger.info(f"Cleaned up {removed_count} old lessons")

        return removed_count
