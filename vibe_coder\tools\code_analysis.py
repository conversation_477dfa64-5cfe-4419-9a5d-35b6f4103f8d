import logging
from pathlib import Path
from typing import Dict, List, Optional, Tu<PERSON>, Any, Union


# Tree-sitter library
try:
    from tree_sitter import Language, Parser, Tree, Node, Query
except ImportError:
    # This allows the module to be imported even if tree_sitter is not installed,
    # though methods will fail. Useful for initial setup or environments
    # where tree_sitter might be optional for some functionalities.
    Language = Parser = Tree = Node = Query = None
    logging.warning(
        "Tree-sitter library not found. CodeAnalysisTool will not function. "
        "Please install it via 'pip install tree-sitter' and ensure language packages are installed."
    )

# Individual language packages
LANGUAGE_MODULES = {
    "python": "tree_sitter_python",
    "javascript": "tree_sitter_javascript",
    # Add more languages as needed
}

logger = logging.getLogger(__name__)

# Default mapping of file extensions to language names used by tree-sitter grammars
# This will be used if no specific language_map is provided during initialization.
DEFAULT_LANGUAGE_MAP = {
    ".py": "python",
    ".js": "javascript",
    ".ts": "typescript",
    ".java": "java",
    ".c": "c",
    ".cpp": "cpp",
    ".h": "c", # Often C grammar works for C headers
    ".hpp": "cpp", # Often C++ grammar works for C++ headers
    ".go": "go",
    ".rb": "ruby",
    ".rs": "rust",
    ".php": "php",
    # Add more as needed
}

class CodeAnalysisTool:
    """
    Provides a structured, syntactic understanding of code files using Tree-sitter,
    enabling precise reading and modification.
    """
    def __init__(self, language_map: Optional[Dict[str, str]] = None):
        """
        Initializes the CodeAnalysisTool.

        Args:
            language_map: A dictionary mapping file extensions (e.g., ".py") to
                          language names (e.g., "python") as used by the language packages.
                          If None, uses DEFAULT_LANGUAGE_MAP.
        """
        if Parser is None: # tree_sitter not installed
            raise ImportError("Tree-sitter library is not installed. CodeAnalysisTool cannot operate.")

        self.language_map = language_map or DEFAULT_LANGUAGE_MAP.copy()
        self.parser = Parser()
        self._loaded_languages: Dict[str, Language] = {}

        self._load_languages_from_packages()

    def _load_languages_from_packages(self):
        """
        Loads languages from individual language packages based on the language_map.
        """
        for lang_name in set(self.language_map.values()):
            if lang_name in LANGUAGE_MODULES:
                try:
                    module_name = LANGUAGE_MODULES[lang_name]
                    module = __import__(module_name)
                    language_capsule = module.language()
                    # Convert the PyCapsule to a Language object
                    language = Language(language_capsule)
                    self._loaded_languages[lang_name] = language
                    logger.info(f"Successfully loaded tree-sitter language '{lang_name}' from package '{module_name}'.")
                except ImportError as e:
                    logger.warning(f"Language package '{module_name}' for '{lang_name}' not installed: {e}. "
                                   f"Install it with 'pip install {module_name.replace('_', '-')}'.")
                except Exception as e:
                    logger.error(f"Failed to load language '{lang_name}' from package '{module_name}': {e}")
            else:
                logger.warning(f"No language package available for '{lang_name}'. "
                               f"Supported languages: {list(LANGUAGE_MODULES.keys())}")
                                 


    def _get_language_for_file(self, file_path: Union[str, Path]) -> Optional[Language]:
        """Determines the tree-sitter Language for a given file path based on its extension."""
        file_extension = Path(file_path).suffix
        lang_name = self.language_map.get(file_extension)
        if not lang_name:
            logger.warning(f"No language mapping found for file extension '{file_extension}' in file '{file_path}'.")
            return None
        
        language = self._loaded_languages.get(lang_name)
        if not language:
            logger.error(f"Language '{lang_name}' for extension '{file_extension}' is not loaded. "
                         "Ensure it was compiled into the library and specified in language_map.")
        return language

    def get_ast(self, file_path: Union[str, Path], file_content: Optional[bytes] = None) -> Optional[Tree]:
        """
        Reads a file (or uses provided content) and returns its Abstract Syntax Tree (AST).

        Args:
            file_path: Path to the code file. Used to determine the language.
            file_content: Optional. If provided, this content (as bytes) is parsed instead of reading the file.

        Returns:
            A tree_sitter.Tree object representing the AST, or None if parsing fails.
        """
        language = self._get_language_for_file(file_path)
        if not language:
            return None

        self.parser.language = language
        
        try:
            if file_content is None:
                with open(file_path, 'rb') as f: # Read as bytes for tree-sitter
                    file_content = f.read()
            
            tree = self.parser.parse(file_content)
            # Check for major parsing errors (e.g., if the root node is an ERROR node)
            if tree.root_node.has_error or tree.root_node.type == 'ERROR':
                logger.warning(f"Parsing resulted in errors for file '{file_path}'. AST might be incomplete or incorrect.")
            return tree
        except FileNotFoundError:
            logger.error(f"File not found for AST parsing: {file_path}")
            return None
        except Exception as e:
            logger.error(f"Error parsing file {file_path}: {e}")
            return None

    def query_ast(self, ast: Tree, query_string: str) -> List[Tuple[Node, Dict[str, Node]]]:
        """
        Executes a tree-sitter query against an AST to find specific nodes.

        Args:
            ast: The Abstract Syntax Tree (tree_sitter.Tree) to query.
            query_string: The tree-sitter query string.

        Returns:
            A list of tuples, where each tuple contains the matched node(s) and a dictionary of captures.
            Returns an empty list if the query is invalid or no matches are found.
        """
        if not ast or not ast.root_node:
            logger.warning("AST is None or has no root node. Cannot execute query.")
            return []
        
        language = self.parser.language # Get the language used for the current AST
        if not language:
            logger.error("No language set on the parser, cannot compile query. This should not happen if AST was parsed correctly.")
            return []
            
        try:
            query = language.query(query_string)
            captures = query.captures(ast.root_node)

            # In the new API, captures returns a dictionary: {capture_name: [Node, ...]}
            # We need to extract all nodes from all captures and return them as a flat list
            nodes_from_captures: List[Node] = []
            for capture_name, nodes in captures.items():
                nodes_from_captures.extend(nodes)

            return nodes_from_captures

        except Exception as e: # Catch broader errors like invalid query syntax
            logger.error(f"Error executing tree-sitter query '{query_string}': {e}")
            return []

    def get_node_text(self, node: Node) -> str:
        """
        Extracts the source code text from a given AST node.

        Args:
            node: The tree_sitter.Node from which to extract text.

        Returns:
            The source code text of the node as a string.
        """
        if not node:
            logger.warning("Cannot get text from a None node.")
            return ""
        try:
            return node.text.decode('utf-8')
        except Exception as e:
            logger.error(f"Error decoding node text (id: {node.id}, type: {node.type}): {e}")
            return "" # Or raise? For now, return empty string on error.

    def find_function_body(self, file_path: Union[str, Path], function_name: str) -> Optional[str]:
        """
        A high-level helper that combines other methods to get the source code 
        for a specific function's body.

        Args:
            file_path: Path to the code file.
            function_name: The name of the function to find.

        Returns:
            The source code of the function's body as a string, or None if not found or error.
        """
        ast = self.get_ast(file_path)
        if not ast:
            return None

        # This query needs to be adapted per language.
        # This is a generic Python-like query.
        # It captures the function definition node itself.
        # To get just the body, the query needs to be more specific or we post-process.
        
        # Example query for Python to capture the function definition node:
        # (function_definition name: (identifier) @function_name) @function_def
        # Then, filter for @function_name text == function_name, and get text of @function_def.
        # To get just the body, the query would be:
        # (function_definition name: (identifier) @name body: (block) @body)
        
        # Let's make a language-aware query selection here.
        file_extension = Path(file_path).suffix
        lang_name = self.language_map.get(file_extension)

        query_string = None
        if lang_name == "python":
            # This query captures the function definition node if its name matches.
            # query_string = f"""
            # (function_definition
            #  name: (identifier) @name
            #  body: (block) @body)
            # (#eq? @name "{function_name}")
            # """
            # Simpler query to get all function definitions, then filter by name in Python
            query_string = """
            (function_definition
                name: (identifier) @function.name
                body: (_) @function.body)
            """
        # Add elif for other languages (javascript, java, etc.) with their specific queries
        # elif lang_name == "javascript":
        # query_string = """..."""
        
        if not query_string:
            logger.warning(f"No specific function finding query defined for language '{lang_name}' (file: {file_path}).")
            return None

        # The query_ast as defined now returns List[Node].
        # If we use the query above, it will return a list of identifier nodes (for @function.name)
        # and block nodes (for @function.body). This is not ideal for this helper.
        
        # Let's adjust the find_function_body to use query.matches for structured captures.
        language = self.parser.language
        if not language: return None # Should be set if AST was parsed
        
        try:
            query = language.query(query_string)
            matches = query.matches(ast.root_node)

            logger.debug(f"Found {len(matches)} function matches")

            # Use matches to ensure name and body are from the same function
            for match_id, captures_dict in matches:
                name_nodes = captures_dict.get("function.name", [])
                body_nodes = captures_dict.get("function.body", [])

                if name_nodes and body_nodes:
                    name_node = name_nodes[0]  # Take the first (should be only one per match)
                    body_node = body_nodes[0]  # Take the first (should be only one per match)

                    name_text = self.get_node_text(name_node)
                    logger.debug(f"Checking function name: '{name_text}' against target: '{function_name}'")
                    if name_text == function_name:
                        body_text = self.get_node_text(body_node)
                        logger.debug(f"Found matching function, returning body: {body_text[:50]}...")
                        return body_text
            
            logger.info(f"Function '{function_name}' not found in '{file_path}' using query.")
            return None
            
        except Exception as e:
            logger.error(f"Error during find_function_body for '{function_name}' in '{file_path}': {e}")
            return None


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
    logger.info("CodeAnalysisTool example starting.")

    # --- Step 1: Ensure you have language packages installed ---
    # Install with: pip install tree-sitter-python tree-sitter-javascript

    logger.info("Using individual language packages (tree-sitter-python, tree-sitter-javascript)")
    logger.info("Make sure to install them with: pip install tree-sitter-python tree-sitter-javascript")

    # --- Step 2: Initialize and use the CodeAnalysisTool ---
    try:
        analyzer = CodeAnalysisTool()

        # Create a dummy Python file for testing
        dummy_py_file = Path("dummy_analyzer_test.py")
        dummy_py_content = """
import os
import sys

class MyClass:
    def __init__(self, value):
        self.value = value

    def get_value(self):
        # This is a method
        return self.value

def my_global_function(name: str, age: int) -> str:
    '''This is a global function.'''
    local_var = f"Hello, {name}! You are {age}."
    print(local_var)
    if age > 18:
        return "Adult"
    else:
        return "Minor"

def another_function():
    pass
"""
        with open(dummy_py_file, "w", encoding="utf-8") as f:
            f.write(dummy_py_content)

        logger.info(f"\n--- Testing get_ast for {dummy_py_file} ---")
        ast = analyzer.get_ast(dummy_py_file)
        if ast:
            print(f"AST Root node type: {ast.root_node.type}")
            assert ast.root_node.type == "module"
        else:
            print("Failed to get AST.")

        logger.info(f"\n--- Testing query_ast: Find all function definitions ---")
        # Query for Python function definitions
        # This query captures the entire function_definition node.
        func_def_query = "(function_definition) @func"
        if ast:
            # The query_ast method as per blueprint returns List[Node]
            # Here, we expect each node to be a function_definition node
            function_nodes = analyzer.query_ast(ast, func_def_query)
            print(f"Found {len(function_nodes)} function definition nodes.")
            for i, node in enumerate(function_nodes):
                print(f"  Function node {i+1} type: {node.type}") # Should be 'function_definition'
                # To get name, we'd need a more specific query or traverse children
                name_node = node.child_by_field_name("name")
                if name_node:
                    print(f"    Name: {analyzer.get_node_text(name_node)}")
            assert len(function_nodes) == 4 # __init__, another_function, my_global_function, get_value (includes class methods)

        logger.info(f"\n--- Testing query_ast: Find all import statements ---")
        import_query = "(import_statement) @import" # Captures the whole import
        if ast:
            import_nodes = analyzer.query_ast(ast, import_query)
            print(f"Found {len(import_nodes)} import statement nodes.")
            for node in import_nodes:
                print(f"  Import text: {analyzer.get_node_text(node)}")
            # The dummy file has `import os` and `import sys` which should be 2 separate import_statement nodes
            # Let's be flexible with the assertion since the exact count may vary
            assert len(import_nodes) >= 1  # At least one import should be found


        logger.info(f"\n--- Testing get_node_text ---")
        if ast and function_nodes:
            # Find the my_global_function node specifically
            my_global_func_node = None
            for node in function_nodes:
                name_node = node.child_by_field_name("name")
                if name_node and analyzer.get_node_text(name_node) == "my_global_function":
                    my_global_func_node = node
                    break

            if my_global_func_node:
                func_text = analyzer.get_node_text(my_global_func_node)
                print(f"Text of my_global_function node:\n{func_text[:100]}...") # Print snippet
                assert "my_global_function" in func_text
            else:
                print("my_global_function node not found")

        logger.info(f"\n--- Testing find_function_body for 'my_global_function' ---")
        body = analyzer.find_function_body(dummy_py_file, "my_global_function")
        if body:
            print(f"Body of 'my_global_function':\n{body.strip()}")
            # Let's be more flexible with the assertion since the exact body format may vary
            assert ("Hello" in body or "local_var" in body), f"Expected function body content not found in: {body}"
        else:
            print("'my_global_function' body not found.")

        logger.info(f"\n--- Testing find_function_body for class method 'get_value' ---")
        # Tree-sitter often treats methods within classes differently.
        # The current find_function_body query is for top-level functions.
        # To find methods, the query would need to be like:
        # (class_definition body: (block (.function_definition name: (identifier) @method_name ...)))
        # For now, this will likely fail or return None, which is expected with the current simple query.
        method_body = analyzer.find_function_body(dummy_py_file, "get_value")
        if method_body:
             print(f"Body of 'get_value' method:\n{method_body.strip()}")
             # This assertion will likely fail with the current simple find_function_body query
             # assert "return self.value" in method_body
        else:
            print("'get_value' method body not found (expected with current simple query for top-level functions).")


        # Clean up dummy file
        if dummy_py_file.exists():
            dummy_py_file.unlink()

    except ImportError as e:
        logger.error(f"Tree-sitter import error during example: {e}. Ensure tree-sitter and language packages are installed.")
        logger.error("Install with: pip install tree-sitter tree-sitter-python tree-sitter-javascript")
    except Exception as e:
        logger.exception(f"An error occurred in CodeAnalysisTool example: {e}")

    logger.info("CodeAnalysisTool example finished.")
