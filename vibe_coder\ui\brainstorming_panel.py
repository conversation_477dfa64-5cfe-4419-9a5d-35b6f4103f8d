"""
Strategic Brainstorming Panel for Human-Agent Collaboration.

This panel provides a conversational interface for brainstorming with
individual strategic agents or the full strategic team.
"""

import logging
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
                             QLineEdit, QPushButton, QComboBox, QLabel,
                             QSplitter, QTabWidget, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QDateTime
from PyQt5.QtGui import QFont, QTextCursor

logger = logging.getLogger(__name__)


class ConversationMessage(QFrame):
    """Widget to display a single conversation message."""
    
    def __init__(self, sender: str, message: str, message_type: str = "human", parent=None):
        super().__init__(parent)
        self.sender = sender
        self.message = message
        self.message_type = message_type
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the message display UI."""
        layout = QVBoxLayout(self)
        
        # Sender label
        sender_label = QLabel(f"💭 {self.sender}")
        sender_label.setFont(QFont("Arial", 9, QFont.Bold))
        
        # Style based on message type
        if self.message_type == "human":
            sender_label.setStyleSheet("color: #2c3e50; background-color: #ecf0f1; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #3498db; margin: 5px; padding: 5px;")
        elif self.message_type == "muse":
            sender_label.setStyleSheet("color: #8e44ad; background-color: #f4ecf7; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #9b59b6; margin: 5px; padding: 5px;")
        elif self.message_type == "oracle":
            sender_label.setStyleSheet("color: #c0392b; background-color: #fadbd8; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #e74c3c; margin: 5px; padding: 5px;")
        elif self.message_type == "wisdom":
            sender_label.setStyleSheet("color: #d68910; background-color: #fef9e7; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #f39c12; margin: 5px; padding: 5px;")
        
        layout.addWidget(sender_label)
        
        # Message content
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("color: #2c3e50; padding: 5px;")
        layout.addWidget(message_label)


class AgentConversationTab(QWidget):
    """Tab for conversation with a specific agent."""
    
    # Signals
    message_sent = pyqtSignal(str, str)  # agent_type, message
    
    def __init__(self, agent_type: str, agent_name: str, agent_description: str, parent=None):
        super().__init__(parent)
        self.agent_type = agent_type
        self.agent_name = agent_name
        self.agent_description = agent_description
        self.conversation_history = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the conversation tab UI."""
        layout = QVBoxLayout(self)
        
        # Agent description
        desc_label = QLabel(f"🤖 {self.agent_name}: {self.agent_description}")
        desc_label.setFont(QFont("Arial", 10, QFont.Bold))
        desc_label.setStyleSheet("color: #34495e; padding: 10px; background-color: #ecf0f1; border-radius: 5px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Conversation area
        self.conversation_scroll = QScrollArea()
        self.conversation_widget = QWidget()
        self.conversation_layout = QVBoxLayout(self.conversation_widget)
        self.conversation_layout.addStretch()
        
        self.conversation_scroll.setWidget(self.conversation_widget)
        self.conversation_scroll.setWidgetResizable(True)
        self.conversation_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        layout.addWidget(self.conversation_scroll)
        
        # Enhanced input area with larger text box
        input_layout = QVBoxLayout()

        input_label = QLabel(f"💬 Chat with {self.agent_name}:")
        input_label.setFont(QFont("Arial", 9, QFont.Bold))
        input_layout.addWidget(input_label)

        # Larger text input with word wrap
        self.message_input = QTextEdit()
        self.message_input.setPlaceholderText(f"Ask {self.agent_name} anything... (Shift+Enter for new line, Enter to send)")
        self.message_input.setMaximumHeight(120)  # Taller input area
        self.message_input.setWordWrapMode(QTextEdit.WidgetWidth)
        input_layout.addWidget(self.message_input)

        # Send button
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.send_button = QPushButton("Send Message")
        self.send_button.clicked.connect(self.send_message)
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(self.send_button)
        input_layout.addLayout(button_layout)

        layout.addLayout(input_layout)
    
    def send_message(self):
        """Send a message to the agent."""
        message = self.message_input.toPlainText().strip()
        if not message:
            return

        # Add human message to conversation
        self.add_message("You", message, "human")

        # Clear input
        self.message_input.clear()

        # Emit signal for backend processing
        self.message_sent.emit(self.agent_type, message)

    def keyPressEvent(self, event):
        """Handle key press events for better UX."""
        if event.key() == Qt.Key_Return and not event.modifiers() & Qt.ShiftModifier:
            # Enter without Shift sends message
            self.send_message()
        else:
            # Pass other key events to parent
            super().keyPressEvent(event)
    
    def add_message(self, sender: str, message: str, message_type: str):
        """Add a message to the conversation."""
        message_widget = ConversationMessage(sender, message, message_type)
        
        # Insert before the stretch
        self.conversation_layout.insertWidget(
            self.conversation_layout.count() - 1, 
            message_widget
        )
        
        # Scroll to bottom
        self.conversation_scroll.verticalScrollBar().setValue(
            self.conversation_scroll.verticalScrollBar().maximum()
        )
        
        # Store in history
        self.conversation_history.append({
            "sender": sender,
            "message": message,
            "type": message_type
        })
    
    def add_agent_response(self, response: str):
        """Add an agent response to the conversation."""
        self.add_message(self.agent_name, response, self.agent_type)


class StrategicBrainstormingPanel(QWidget):
    """
    Main panel for strategic brainstorming with AI agents.
    """
    
    # Signals
    agent_consultation_requested = pyqtSignal(str, str, dict)  # agent_type, message, context
    team_brainstorm_requested = pyqtSignal(str, dict)  # topic, context
    project_transfer_requested = pyqtSignal(dict)  # brainstorm_summary
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.agent_tabs = {}
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the brainstorming panel UI."""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("🧠 Strategic Brainstorming")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        header_label.setStyleSheet("color: #2c3e50; margin: 10px; padding: 10px;")
        layout.addWidget(header_label)
        
        # Instructions
        instructions = QLabel("💡 Choose a tab to start: Chat with individual agents or collaborate with the full team on the whiteboard")
        instructions.setFont(QFont("Arial", 10))
        instructions.setStyleSheet("color: #6c757d; padding: 5px 10px; background-color: #f8f9fa; border-radius: 3px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Tab widget for different agents
        self.tab_widget = QTabWidget()
        
        # Individual agent tabs
        self.create_agent_tabs()
        
        # Team brainstorm tab
        self.create_team_tab()
        
        layout.addWidget(self.tab_widget)
    
    def create_agent_tabs(self):
        """Create tabs for individual agent conversations."""
        agents = [
            ("muse", "Muse", "Creative ideation and breakthrough thinking"),
            ("oracle", "Oracle", "Risk assessment and evidence-based analysis"),
            ("wisdom", "Wisdom Layer", "Ethical governance and strategic alignment"),
            ("scout", "Scout", "Research existing solutions and market analysis")
        ]
        
        for agent_type, agent_name, description in agents:
            tab = AgentConversationTab(agent_type, agent_name, description)
            tab.message_sent.connect(self.handle_agent_message)
            self.agent_tabs[agent_type] = tab
            self.tab_widget.addTab(tab, f"💭 {agent_name}")
    
    def create_team_tab(self):
        """Create tab for team whiteboard brainstorming."""
        team_tab = QWidget()
        layout = QVBoxLayout(team_tab)

        # Header with team info
        header_layout = QHBoxLayout()

        desc_label = QLabel("🎭 Strategic Team Whiteboard")
        desc_label.setFont(QFont("Arial", 14, QFont.Bold))
        desc_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        header_layout.addWidget(desc_label)

        header_layout.addStretch()

        # Transfer to project button
        self.transfer_button = QPushButton("📋 Transfer to Project")
        self.transfer_button.clicked.connect(self.transfer_to_project)
        self.transfer_button.setEnabled(False)  # Enabled after discussion
        self.transfer_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        header_layout.addWidget(self.transfer_button)

        layout.addLayout(header_layout)

        # Team members info
        team_info = QLabel("👥 Team: You, Muse (Creative), Oracle (Analytical), Wisdom (Strategic), Scout (Research)")
        team_info.setFont(QFont("Arial", 9))
        team_info.setStyleSheet("color: #6c757d; padding: 5px 10px; background-color: #f8f9fa; border-radius: 3px;")
        layout.addWidget(team_info)

        # Whiteboard conversation area with horizontal scroll
        self.team_conversation = QTextEdit()
        self.team_conversation.setReadOnly(True)
        self.team_conversation.setStyleSheet("""
            QTextEdit {
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.4;
            }
        """)
        self.team_conversation.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        layout.addWidget(self.team_conversation)

        # Enhanced input area
        input_layout = QVBoxLayout()

        input_label = QLabel("💬 Your message to the team:")
        input_label.setFont(QFont("Arial", 10, QFont.Bold))
        input_layout.addWidget(input_label)

        # Larger text input for verbose discussions
        self.team_input = QTextEdit()
        self.team_input.setPlaceholderText("Share your ideas, questions, or thoughts with the strategic team...\n\nExample: 'I want to build a task management app but I'm not sure if I should focus on individual productivity or team collaboration. What are the pros and cons of each approach?'\n\n(Shift+Enter for new line, Enter to send)")
        self.team_input.setMaximumHeight(120)
        self.team_input.setWordWrapMode(QTextEdit.WidgetWidth)
        input_layout.addWidget(self.team_input)

        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.team_send_button = QPushButton("💭 Send to Team")
        self.team_send_button.clicked.connect(self.start_team_brainstorm)
        self.team_send_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 6px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(self.team_send_button)

        input_layout.addLayout(button_layout)
        layout.addLayout(input_layout)

        # Store reference for transfer functionality
        self.team_tab_widget = team_tab

        self.tab_widget.addTab(team_tab, "🎭 Team Whiteboard")
    
    @pyqtSlot(str, str)
    def handle_agent_message(self, agent_type: str, message: str):
        """Handle message sent to an individual agent."""
        # Get current context (could include current project, goals, etc.)
        context = self.get_current_context()
        
        # Emit signal for backend processing
        self.agent_consultation_requested.emit(agent_type, message, context)
    
    def start_team_brainstorm(self):
        """Start a team brainstorming session."""
        topic = self.team_input.toPlainText().strip()
        if not topic:
            return

        # Add human message to whiteboard
        self.add_team_message("You", topic, "human")
        self.team_input.clear()

        # Enable transfer button after first message
        self.transfer_button.setEnabled(True)

        # Get context and start team brainstorm
        context = self.get_current_context()
        self.team_brainstorm_requested.emit(topic, context)

    def add_team_message(self, sender: str, message: str, sender_type: str):
        """Add a message to the team whiteboard."""
        # Format message with timestamp and styling
        timestamp = QDateTime.currentDateTime().toString("hh:mm")

        # Style based on sender type
        if sender_type == "human":
            color = "#2c3e50"
            icon = "👤"
        elif sender_type == "muse":
            color = "#8e44ad"
            icon = "🎨"
        elif sender_type == "oracle":
            color = "#c0392b"
            icon = "🔍"
        elif sender_type == "wisdom":
            color = "#d68910"
            icon = "🏛️"
        elif sender_type == "scout":
            color = "#27ae60"
            icon = "🌐"
        else:
            color = "#34495e"
            icon = "🤖"

        # Add formatted message
        formatted_message = f"""
        <div style="margin: 10px 0; padding: 10px; border-left: 4px solid {color}; background-color: #f8f9fa;">
            <div style="font-weight: bold; color: {color}; margin-bottom: 5px;">
                {icon} <span style="font-size: 11pt;">{sender}</span>
                <span style="font-size: 9pt; color: #6c757d; float: right;">{timestamp}</span>
            </div>
            <div style="color: #2c3e50; line-height: 1.4;">
                {message.replace('\n', '<br>')}
            </div>
        </div>
        """

        self.team_conversation.append(formatted_message)

        # Scroll to bottom
        cursor = self.team_conversation.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.team_conversation.setTextCursor(cursor)

    def transfer_to_project(self):
        """Transfer brainstorm results to project development tab."""
        # This will be connected to the main window to transfer content
        self.project_transfer_requested.emit(self.get_brainstorm_summary())
    
    def get_current_context(self) -> Dict[str, Any]:
        """Get current context for brainstorming."""
        # This would be populated with current project info, goals, etc.
        return {
            "current_project": "Active project context",
            "conversation_history": "Recent conversation context",
            "project_phase": "Current phase of development"
        }
    
    def add_agent_response(self, agent_type: str, response: str):
        """Add an agent response to the appropriate conversation."""
        if agent_type in self.agent_tabs:
            self.agent_tabs[agent_type].add_agent_response(response)
    
    def add_team_response(self, agent_name: str, response: str):
        """Add a team brainstorm response."""
        # Map agent names to types for styling
        agent_type_map = {
            "Muse": "muse",
            "Oracle": "oracle",
            "Wisdom Layer": "wisdom",
            "Scout": "scout"
        }

        agent_type = agent_type_map.get(agent_name, "agent")
        self.add_team_message(agent_name, response, agent_type)

    def get_brainstorm_summary(self) -> Dict[str, Any]:
        """Generate a summary of the brainstorm for project transfer."""
        # Extract text content from the conversation
        conversation_text = self.team_conversation.toPlainText()

        # Simple extraction of key points (in a real implementation, this could use AI)
        lines = conversation_text.split('\n')
        user_messages = [line for line in lines if line.strip().startswith('You:')]

        # Generate summary
        if user_messages:
            # Use the first substantial user message as the goal
            first_message = user_messages[0].replace('You:', '').strip()

            # Create a comprehensive description from all messages
            description_parts = []
            description_parts.append("Based on strategic team brainstorming session:")
            description_parts.append("")

            # Add key insights from each agent type
            if "Muse" in conversation_text:
                description_parts.append("Creative insights from Muse agent included.")
            if "Oracle" in conversation_text:
                description_parts.append("Risk analysis from Oracle agent considered.")
            if "Wisdom" in conversation_text:
                description_parts.append("Strategic guidance from Wisdom Layer incorporated.")
            if "Scout" in conversation_text:
                description_parts.append("Market research from Scout agent reviewed.")

            description_parts.append("")
            description_parts.append("Full brainstorm conversation available in team whiteboard.")

            return {
                "goal": first_message[:200] + "..." if len(first_message) > 200 else first_message,
                "description": "\n".join(description_parts),
                "source": "team_brainstorm",
                "conversation_length": len(lines),
                "agents_participated": self._count_participating_agents(conversation_text)
            }

        return {
            "goal": "",
            "description": "No brainstorm content to transfer",
            "source": "team_brainstorm"
        }

    def _count_participating_agents(self, conversation_text: str) -> List[str]:
        """Count which agents participated in the conversation."""
        agents = []
        if "Muse" in conversation_text:
            agents.append("Muse")
        if "Oracle" in conversation_text:
            agents.append("Oracle")
        if "Wisdom" in conversation_text:
            agents.append("Wisdom Layer")
        if "Scout" in conversation_text:
            agents.append("Scout")
        return agents
