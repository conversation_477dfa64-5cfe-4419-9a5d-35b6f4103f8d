"""
Strategic Brainstorming Panel for Human-Agent Collaboration.

This panel provides a conversational interface for brainstorming with
individual strategic agents or the full strategic team.
"""

import logging
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit,
                             QLineEdit, QPushButton, QComboBox, QLabel,
                             QSplitter, QTabWidget, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QDateTime
from PyQt5.QtGui import QFont, QTextCursor, QTextOption

logger = logging.getLogger(__name__)


class ConversationMessage(QFrame):
    """Widget to display a single conversation message."""
    
    def __init__(self, sender: str, message: str, message_type: str = "human", parent=None):
        super().__init__(parent)
        self.sender = sender
        self.message = message
        self.message_type = message_type
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the message display UI."""
        layout = QVBoxLayout(self)
        
        # Sender label
        sender_label = QLabel(f"💭 {self.sender}")
        sender_label.setFont(QFont("Arial", 9, QFont.Bold))
        
        # Style based on message type
        if self.message_type == "human":
            sender_label.setStyleSheet("color: #2c3e50; background-color: #ecf0f1; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #3498db; margin: 5px; padding: 5px;")
        elif self.message_type == "muse":
            sender_label.setStyleSheet("color: #8e44ad; background-color: #f4ecf7; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #9b59b6; margin: 5px; padding: 5px;")
        elif self.message_type == "oracle":
            sender_label.setStyleSheet("color: #c0392b; background-color: #fadbd8; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #e74c3c; margin: 5px; padding: 5px;")
        elif self.message_type == "wisdom":
            sender_label.setStyleSheet("color: #d68910; background-color: #fef9e7; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #f39c12; margin: 5px; padding: 5px;")
        
        layout.addWidget(sender_label)
        
        # Message content
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("color: #2c3e50; padding: 5px;")
        layout.addWidget(message_label)


class AgentConversationTab(QWidget):
    """Tab for conversation with a specific agent."""
    
    # Signals
    message_sent = pyqtSignal(str, str)  # agent_type, message
    
    def __init__(self, agent_type: str, agent_name: str, agent_description: str, parent=None):
        super().__init__(parent)
        self.agent_type = agent_type
        self.agent_name = agent_name
        self.agent_description = agent_description
        self.conversation_history = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the conversation tab UI."""
        layout = QVBoxLayout(self)
        
        # Agent description
        desc_label = QLabel(f"🤖 {self.agent_name}: {self.agent_description}")
        desc_label.setFont(QFont("Arial", 10, QFont.Bold))
        desc_label.setStyleSheet("color: #34495e; padding: 10px; background-color: #ecf0f1; border-radius: 5px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Conversation area
        self.conversation_scroll = QScrollArea()
        self.conversation_widget = QWidget()
        self.conversation_layout = QVBoxLayout(self.conversation_widget)
        self.conversation_layout.addStretch()
        
        self.conversation_scroll.setWidget(self.conversation_widget)
        self.conversation_scroll.setWidgetResizable(True)
        self.conversation_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        layout.addWidget(self.conversation_scroll)
        
        # Enhanced input area with larger text box
        input_layout = QVBoxLayout()

        input_label = QLabel(f"💬 Chat with {self.agent_name}:")
        input_label.setFont(QFont("Arial", 9, QFont.Bold))
        input_layout.addWidget(input_label)

        # Larger text input with word wrap
        self.message_input = QTextEdit()
        self.message_input.setPlaceholderText(f"Ask {self.agent_name} anything... (Shift+Enter for new line, Enter to send)")
        self.message_input.setMaximumHeight(120)  # Taller input area
        self.message_input.setWordWrapMode(QTextOption.WordWrap)
        input_layout.addWidget(self.message_input)

        # Send button
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.send_button = QPushButton("Send Message")
        self.send_button.clicked.connect(self.send_message)
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        button_layout.addWidget(self.send_button)
        input_layout.addLayout(button_layout)

        layout.addLayout(input_layout)
    
    def send_message(self):
        """Send a message to the agent."""
        message = self.message_input.toPlainText().strip()
        if not message:
            return

        # Add human message to conversation
        self.add_message("You", message, "human")

        # Clear input
        self.message_input.clear()

        # Emit signal for backend processing
        self.message_sent.emit(self.agent_type, message)

    def keyPressEvent(self, event):
        """Handle key press events for better UX."""
        if event.key() == Qt.Key_Return and not event.modifiers() & Qt.ShiftModifier:
            # Enter without Shift sends message
            self.send_message()
        else:
            # Pass other key events to parent
            super().keyPressEvent(event)
    
    def add_message(self, sender: str, message: str, message_type: str):
        """Add a message to the conversation."""
        message_widget = ConversationMessage(sender, message, message_type)
        
        # Insert before the stretch
        self.conversation_layout.insertWidget(
            self.conversation_layout.count() - 1, 
            message_widget
        )
        
        # Scroll to bottom
        self.conversation_scroll.verticalScrollBar().setValue(
            self.conversation_scroll.verticalScrollBar().maximum()
        )
        
        # Store in history
        self.conversation_history.append({
            "sender": sender,
            "message": message,
            "type": message_type
        })
    
    def add_agent_response(self, response: str):
        """Add an agent response to the conversation."""
        self.add_message(self.agent_name, response, self.agent_type)


class StrategicBrainstormingPanel(QWidget):
    """
    Main panel for strategic brainstorming with AI agents.
    """
    
    # Signals
    agent_consultation_requested = pyqtSignal(str, str, dict)  # agent_type, message, context
    team_brainstorm_requested = pyqtSignal(str, dict)  # topic, context
    project_transfer_requested = pyqtSignal(dict)  # brainstorm_summary
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.agent_tabs = {}
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the brainstorming panel UI."""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("🧠 Concept Brainstorming")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        header_label.setStyleSheet("color: #2c3e50; margin: 10px; padding: 10px;")
        layout.addWidget(header_label)
        
        # Instructions
        instructions = QLabel("💡 Explore WHAT to build: Chat with individual agents or collaborate with the full team to define your project concept")
        instructions.setFont(QFont("Arial", 10))
        instructions.setStyleSheet("color: #6c757d; padding: 5px 10px; background-color: #f8f9fa; border-radius: 3px;")
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Tab widget for different agents
        self.tab_widget = QTabWidget()
        
        # Individual agent tabs
        self.create_agent_tabs()
        
        # Team brainstorm tab
        self.create_team_tab()
        
        layout.addWidget(self.tab_widget)
    
    def create_agent_tabs(self):
        """Create tabs for individual agent conversations."""
        agents = [
            ("muse", "Muse", "Creative ideation and breakthrough thinking"),
            ("oracle", "Oracle", "Risk assessment and evidence-based analysis"),
            ("wisdom", "Wisdom Layer", "Ethical governance and strategic alignment"),
            ("scout", "Scout", "Research existing solutions and market analysis")
        ]
        
        for agent_type, agent_name, description in agents:
            tab = AgentConversationTab(agent_type, agent_name, description)
            tab.message_sent.connect(self.handle_agent_message)
            self.agent_tabs[agent_type] = tab
            self.tab_widget.addTab(tab, f"💭 {agent_name}")
    
    def create_team_tab(self):
        """Create tab for team whiteboard brainstorming."""
        team_tab = QWidget()
        layout = QVBoxLayout(team_tab)

        # Header with team info
        header_layout = QHBoxLayout()

        desc_label = QLabel("🎭 Concept Team Whiteboard")
        desc_label.setFont(QFont("Arial", 14, QFont.Bold))
        desc_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        header_layout.addWidget(desc_label)

        header_layout.addStretch()

        # Transfer to project button
        self.transfer_button = QPushButton("📋 Transfer to Project")
        self.transfer_button.clicked.connect(self.transfer_to_project)
        self.transfer_button.setEnabled(False)  # Enabled after discussion
        self.transfer_button.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        header_layout.addWidget(self.transfer_button)

        layout.addLayout(header_layout)

        # Team members info
        team_info = QLabel("👥 Concept Team: You, Muse (Creative Ideas), Oracle (Risk Analysis), Wisdom (Strategic Alignment), Scout (Market Research)")
        team_info.setFont(QFont("Arial", 9))
        team_info.setStyleSheet("color: #6c757d; padding: 5px 10px; background-color: #f8f9fa; border-radius: 3px;")
        layout.addWidget(team_info)

        # Whiteboard conversation area with horizontal scroll
        self.team_conversation = QTextEdit()
        self.team_conversation.setReadOnly(True)
        self.team_conversation.setStyleSheet("""
            QTextEdit {
                background-color: #ffffff;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11pt;
                line-height: 1.4;
            }
        """)
        self.team_conversation.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        layout.addWidget(self.team_conversation)

        # Enhanced input area
        input_layout = QVBoxLayout()

        input_label = QLabel("💬 Your concept ideas and questions:")
        input_label.setFont(QFont("Arial", 10, QFont.Bold))
        input_layout.addWidget(input_label)

        # Larger text input for verbose discussions
        self.team_input = QTextEdit()
        self.team_input.setPlaceholderText("Share your project ideas, requirements, and questions with the concept team...\n\nExample: 'I want to build a task management app but I'm not sure if I should focus on individual productivity or team collaboration. What are the pros and cons of each approach? What features would users find most valuable?'\n\n(Shift+Enter for new line, Enter to send)")
        self.team_input.setMaximumHeight(120)
        self.team_input.setWordWrapMode(QTextOption.WordWrap)
        input_layout.addWidget(self.team_input)

        # Button layout
        button_layout = QHBoxLayout()

        # Session status
        self.session_status = QLabel("💡 Start a conversation with the team")
        self.session_status.setStyleSheet("color: #6c757d; font-style: italic;")
        button_layout.addWidget(self.session_status)

        button_layout.addStretch()

        self.team_send_button = QPushButton("💭 Send to Team")
        self.team_send_button.clicked.connect(self.send_team_message)
        self.team_send_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 12px 25px;
                border: none;
                border-radius: 6px;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        button_layout.addWidget(self.team_send_button)

        input_layout.addLayout(button_layout)
        layout.addLayout(input_layout)

        # Store reference for transfer functionality and session state
        self.team_tab_widget = team_tab
        self.brainstorm_session_active = False
        self.conversation_messages = []  # Store all messages for memory

        self.tab_widget.addTab(team_tab, "🎭 Team Whiteboard")
    
    @pyqtSlot(str, str)
    def handle_agent_message(self, agent_type: str, message: str):
        """Handle message sent to an individual agent."""
        # Get current context (could include current project, goals, etc.)
        context = self.get_current_context()
        
        # Emit signal for backend processing
        self.agent_consultation_requested.emit(agent_type, message, context)
    
    def send_team_message(self):
        """Send a message to the team (ongoing conversation)."""
        message = self.team_input.toPlainText().strip()
        if not message:
            return

        # Add human message to whiteboard
        self.add_team_message("You", message, "human")
        self.team_input.clear()

        # Update session status
        if not self.brainstorm_session_active:
            self.brainstorm_session_active = True
            self.session_status.setText("🔄 Team conversation active")
            self.transfer_button.setEnabled(True)

        # Get context and send to team
        context = self.get_current_context()
        context["session_active"] = self.brainstorm_session_active
        context["conversation_history"] = self.conversation_messages[-10:]  # Last 10 messages

        self.team_brainstorm_requested.emit(message, context)

    def add_team_message(self, sender: str, message: str, sender_type: str):
        """Add a message to the team whiteboard."""
        # Format message with timestamp and styling
        timestamp = QDateTime.currentDateTime().toString("hh:mm")

        # Style based on sender type
        if sender_type == "human":
            color = "#2c3e50"
            icon = "👤"
        elif sender_type == "muse":
            color = "#8e44ad"
            icon = "🎨"
        elif sender_type == "oracle":
            color = "#c0392b"
            icon = "🔍"
        elif sender_type == "wisdom":
            color = "#d68910"
            icon = "🏛️"
        elif sender_type == "scout":
            color = "#27ae60"
            icon = "🌐"
        else:
            color = "#34495e"
            icon = "🤖"

        # Add formatted message (fix f-string backslash issue)
        message_html = message.replace('\n', '<br>')
        formatted_message = f"""
        <div style="margin: 10px 0; padding: 10px; border-left: 4px solid {color}; background-color: #f8f9fa;">
            <div style="font-weight: bold; color: {color}; margin-bottom: 5px;">
                {icon} <span style="font-size: 11pt;">{sender}</span>
                <span style="font-size: 9pt; color: #6c757d; float: right;">{timestamp}</span>
            </div>
            <div style="color: #2c3e50; line-height: 1.4;">
                {message_html}
            </div>
        </div>
        """

        self.team_conversation.append(formatted_message)

        # Store message in conversation memory
        self.conversation_messages.append({
            "sender": sender,
            "message": message,
            "sender_type": sender_type,
            "timestamp": timestamp
        })

        # Scroll to bottom
        cursor = self.team_conversation.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.team_conversation.setTextCursor(cursor)

    def transfer_to_project(self):
        """Transfer brainstorm results to project development tab."""
        # This will be connected to the main window to transfer content
        self.project_transfer_requested.emit(self.get_brainstorm_summary())
    
    def get_current_context(self) -> Dict[str, Any]:
        """Get current context for brainstorming."""
        # This would be populated with current project info, goals, etc.
        return {
            "current_project": "Active project context",
            "conversation_history": "Recent conversation context",
            "project_phase": "Current phase of development"
        }
    
    def add_agent_response(self, agent_type: str, response: str):
        """Add an agent response to the appropriate conversation."""
        if agent_type in self.agent_tabs:
            self.agent_tabs[agent_type].add_agent_response(response)
    
    def add_team_response(self, agent_name: str, response: str):
        """Add a team brainstorm response."""
        # Map agent names to types for styling
        agent_type_map = {
            "Muse": "muse",
            "Oracle": "oracle",
            "Wisdom Layer": "wisdom",
            "Scout": "scout"
        }

        agent_type = agent_type_map.get(agent_name, "agent")
        self.add_team_message(agent_name, response, agent_type)

    def get_brainstorm_summary(self) -> Dict[str, Any]:
        """Generate an intelligent summary of the brainstorm for project transfer."""
        if not self.conversation_messages:
            return {
                "goal": "",
                "description": "No brainstorm content to transfer",
                "source": "team_brainstorm"
            }

        # Extract user messages and agent insights
        user_messages = [msg for msg in self.conversation_messages if msg["sender_type"] == "human"]
        agent_messages = [msg for msg in self.conversation_messages if msg["sender_type"] != "human"]

        # Generate intelligent goal from user messages
        goal = self._extract_project_goal(user_messages)

        # Generate comprehensive description from agent insights
        description = self._generate_project_description(user_messages, agent_messages)

        # Create brainstorm memory for task reflection
        brainstorm_memory = self._create_brainstorm_memory()

        return {
            "goal": goal,
            "description": description,
            "source": "team_brainstorm",
            "conversation_length": len(self.conversation_messages),
            "agents_participated": self._count_participating_agents_from_messages(),
            "brainstorm_memory": brainstorm_memory,
            "key_insights": self._extract_key_insights(),
            "decisions_made": self._extract_decisions(),
            "timestamp": QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        }

    def _extract_project_goal(self, user_messages: List[Dict]) -> str:
        """Extract a clear project goal from user messages."""
        if not user_messages:
            return ""

        # Find the most substantial user message
        substantial_messages = [
            msg["message"] for msg in user_messages
            if len(msg["message"].strip()) > 20
        ]

        if substantial_messages:
            # Use the first substantial message, cleaned up
            goal = substantial_messages[0].strip()

            # Clean up common conversational elements
            goal = goal.replace("I want to", "").replace("I need to", "").replace("Can you help me", "")
            goal = goal.replace("Let's", "").replace("How about", "").strip()

            # Capitalize first letter
            if goal:
                goal = goal[0].upper() + goal[1:] if len(goal) > 1 else goal.upper()

            return goal[:200] + "..." if len(goal) > 200 else goal

        return "Project goal from brainstorming session"

    def _generate_project_description(self, user_messages: List[Dict], agent_messages: List[Dict]) -> str:
        """Generate a comprehensive project description from the conversation."""
        description_parts = []
        description_parts.append("🧠 Strategic Team Brainstorming Results")
        description_parts.append("=" * 40)
        description_parts.append("")

        # Add user context
        if user_messages:
            description_parts.append("💭 User Requirements & Ideas:")
            for i, msg in enumerate(user_messages[:3], 1):  # Top 3 user messages
                clean_msg = msg["message"].strip()[:150]
                description_parts.append(f"  {i}. {clean_msg}...")
            description_parts.append("")

        # Add agent insights by type
        agent_insights = {}
        for msg in agent_messages:
            agent_type = msg["sender"]
            if agent_type not in agent_insights:
                agent_insights[agent_type] = []
            agent_insights[agent_type].append(msg["message"][:100] + "...")

        if "Muse" in agent_insights:
            description_parts.append("🎨 Creative Insights (Muse):")
            description_parts.append(f"  - {agent_insights['Muse'][0]}")
            description_parts.append("")

        if "Oracle" in agent_insights:
            description_parts.append("🔍 Risk Analysis (Oracle):")
            description_parts.append(f"  - {agent_insights['Oracle'][0]}")
            description_parts.append("")

        if "Wisdom Layer" in agent_insights:
            description_parts.append("🏛️ Strategic Guidance (Wisdom):")
            description_parts.append(f"  - {agent_insights['Wisdom Layer'][0]}")
            description_parts.append("")

        if "Scout" in agent_insights:
            description_parts.append("🌐 Market Research (Scout):")
            description_parts.append(f"  - {agent_insights['Scout'][0]}")
            description_parts.append("")

        description_parts.append("📋 Implementation Notes:")
        description_parts.append("  - Full conversation history preserved for reference")
        description_parts.append("  - Strategic insights integrated into development approach")
        description_parts.append("  - Team consensus achieved through collaborative discussion")

        return "\n".join(description_parts)

    def _create_brainstorm_memory(self) -> Dict[str, Any]:
        """Create memory structure for task reflection."""
        return {
            "session_id": f"brainstorm_{QDateTime.currentDateTime().toString('yyyyMMdd_hhmmss')}",
            "participants": self._count_participating_agents_from_messages(),
            "key_topics": self._extract_key_topics(),
            "consensus_points": self._extract_consensus(),
            "open_questions": self._extract_questions(),
            "full_conversation": self.conversation_messages
        }

    def _extract_key_insights(self) -> List[str]:
        """Extract key insights from the conversation."""
        insights = []
        for msg in self.conversation_messages:
            if msg["sender_type"] != "human" and len(msg["message"]) > 50:
                # Extract first sentence as key insight
                first_sentence = msg["message"].split('.')[0] + '.'
                if len(first_sentence) > 20:
                    insights.append(f"{msg['sender']}: {first_sentence}")
        return insights[:5]  # Top 5 insights

    def _extract_decisions(self) -> List[str]:
        """Extract decisions made during the conversation."""
        decisions = []
        decision_keywords = ["decided", "agreed", "concluded", "will", "should", "must"]

        for msg in self.conversation_messages:
            message_lower = msg["message"].lower()
            if any(keyword in message_lower for keyword in decision_keywords):
                # Extract the sentence containing the decision
                sentences = msg["message"].split('.')
                for sentence in sentences:
                    if any(keyword in sentence.lower() for keyword in decision_keywords):
                        decisions.append(f"{msg['sender']}: {sentence.strip()}")
                        break

        return decisions[:3]  # Top 3 decisions

    def _extract_key_topics(self) -> List[str]:
        """Extract key topics discussed."""
        topics = set()
        for msg in self.conversation_messages:
            words = msg["message"].lower().split()
            # Look for important nouns (simple heuristic)
            important_words = [w for w in words if len(w) > 4 and w.isalpha()]
            topics.update(important_words[:3])  # Top 3 words per message
        return list(topics)[:10]  # Top 10 topics

    def _extract_consensus(self) -> List[str]:
        """Extract points of consensus."""
        consensus = []
        consensus_keywords = ["agree", "consensus", "all think", "everyone", "we should"]

        for msg in self.conversation_messages:
            message_lower = msg["message"].lower()
            if any(keyword in message_lower for keyword in consensus_keywords):
                consensus.append(msg["message"][:100] + "...")

        return consensus[:3]  # Top 3 consensus points

    def _extract_questions(self) -> List[str]:
        """Extract open questions from the conversation."""
        questions = []
        for msg in self.conversation_messages:
            if "?" in msg["message"]:
                question_sentences = [s.strip() + "?" for s in msg["message"].split("?") if s.strip()]
                questions.extend(question_sentences[:2])  # Max 2 per message

        return questions[:5]  # Top 5 questions

    def _count_participating_agents_from_messages(self) -> List[str]:
        """Count which agents participated in the conversation."""
        agents = set()
        for msg in self.conversation_messages:
            if msg["sender_type"] != "human":
                agents.add(msg["sender"])
        return list(agents)
