"""
Strategic Brainstorming Panel for Human-Agent Collaboration.

This panel provides a conversational interface for brainstorming with
individual strategic agents or the full strategic team.
"""

import logging
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, 
                             QLineEdit, QPushButton, QComboBox, QLabel,
                             QSplitter, QTabWidget, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QFont, QTextCursor

logger = logging.getLogger(__name__)


class ConversationMessage(QFrame):
    """Widget to display a single conversation message."""
    
    def __init__(self, sender: str, message: str, message_type: str = "human", parent=None):
        super().__init__(parent)
        self.sender = sender
        self.message = message
        self.message_type = message_type
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the message display UI."""
        layout = QVBoxLayout(self)
        
        # Sender label
        sender_label = QLabel(f"💭 {self.sender}")
        sender_label.setFont(QFont("Arial", 9, QFont.Bold))
        
        # Style based on message type
        if self.message_type == "human":
            sender_label.setStyleSheet("color: #2c3e50; background-color: #ecf0f1; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #3498db; margin: 5px; padding: 5px;")
        elif self.message_type == "muse":
            sender_label.setStyleSheet("color: #8e44ad; background-color: #f4ecf7; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #9b59b6; margin: 5px; padding: 5px;")
        elif self.message_type == "oracle":
            sender_label.setStyleSheet("color: #c0392b; background-color: #fadbd8; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #e74c3c; margin: 5px; padding: 5px;")
        elif self.message_type == "wisdom":
            sender_label.setStyleSheet("color: #d68910; background-color: #fef9e7; padding: 5px; border-radius: 3px;")
            self.setStyleSheet("border-left: 3px solid #f39c12; margin: 5px; padding: 5px;")
        
        layout.addWidget(sender_label)
        
        # Message content
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("color: #2c3e50; padding: 5px;")
        layout.addWidget(message_label)


class AgentConversationTab(QWidget):
    """Tab for conversation with a specific agent."""
    
    # Signals
    message_sent = pyqtSignal(str, str)  # agent_type, message
    
    def __init__(self, agent_type: str, agent_name: str, agent_description: str, parent=None):
        super().__init__(parent)
        self.agent_type = agent_type
        self.agent_name = agent_name
        self.agent_description = agent_description
        self.conversation_history = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the conversation tab UI."""
        layout = QVBoxLayout(self)
        
        # Agent description
        desc_label = QLabel(f"🤖 {self.agent_name}: {self.agent_description}")
        desc_label.setFont(QFont("Arial", 10, QFont.Bold))
        desc_label.setStyleSheet("color: #34495e; padding: 10px; background-color: #ecf0f1; border-radius: 5px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Conversation area
        self.conversation_scroll = QScrollArea()
        self.conversation_widget = QWidget()
        self.conversation_layout = QVBoxLayout(self.conversation_widget)
        self.conversation_layout.addStretch()
        
        self.conversation_scroll.setWidget(self.conversation_widget)
        self.conversation_scroll.setWidgetResizable(True)
        self.conversation_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        layout.addWidget(self.conversation_scroll)
        
        # Input area
        input_layout = QHBoxLayout()
        
        self.message_input = QLineEdit()
        self.message_input.setPlaceholderText(f"Ask {self.agent_name} anything...")
        self.message_input.returnPressed.connect(self.send_message)
        input_layout.addWidget(self.message_input)
        
        self.send_button = QPushButton("Send")
        self.send_button.clicked.connect(self.send_message)
        self.send_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        input_layout.addWidget(self.send_button)
        
        layout.addLayout(input_layout)
    
    def send_message(self):
        """Send a message to the agent."""
        message = self.message_input.text().strip()
        if not message:
            return
        
        # Add human message to conversation
        self.add_message("You", message, "human")
        
        # Clear input
        self.message_input.clear()
        
        # Emit signal for backend processing
        self.message_sent.emit(self.agent_type, message)
    
    def add_message(self, sender: str, message: str, message_type: str):
        """Add a message to the conversation."""
        message_widget = ConversationMessage(sender, message, message_type)
        
        # Insert before the stretch
        self.conversation_layout.insertWidget(
            self.conversation_layout.count() - 1, 
            message_widget
        )
        
        # Scroll to bottom
        self.conversation_scroll.verticalScrollBar().setValue(
            self.conversation_scroll.verticalScrollBar().maximum()
        )
        
        # Store in history
        self.conversation_history.append({
            "sender": sender,
            "message": message,
            "type": message_type
        })
    
    def add_agent_response(self, response: str):
        """Add an agent response to the conversation."""
        self.add_message(self.agent_name, response, self.agent_type)


class StrategicBrainstormingPanel(QWidget):
    """
    Main panel for strategic brainstorming with AI agents.
    """
    
    # Signals
    agent_consultation_requested = pyqtSignal(str, str, dict)  # agent_type, message, context
    team_brainstorm_requested = pyqtSignal(str, dict)  # topic, context
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.agent_tabs = {}
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the brainstorming panel UI."""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("🧠 Strategic Brainstorming")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        header_label.setStyleSheet("color: #2c3e50; margin: 10px; padding: 10px;")
        layout.addWidget(header_label)
        
        # Mode selector
        mode_layout = QHBoxLayout()
        mode_label = QLabel("Brainstorming Mode:")
        mode_label.setFont(QFont("Arial", 10, QFont.Bold))
        mode_layout.addWidget(mode_label)
        
        self.mode_selector = QComboBox()
        self.mode_selector.addItems([
            "Individual Agent Consultation",
            "Full Team Brainstorm",
            "Muse vs Oracle Debate",
            "Wisdom Layer Guidance"
        ])
        mode_layout.addWidget(self.mode_selector)
        mode_layout.addStretch()
        
        layout.addLayout(mode_layout)
        
        # Tab widget for different agents
        self.tab_widget = QTabWidget()
        
        # Individual agent tabs
        self.create_agent_tabs()
        
        # Team brainstorm tab
        self.create_team_tab()
        
        layout.addWidget(self.tab_widget)
    
    def create_agent_tabs(self):
        """Create tabs for individual agent conversations."""
        agents = [
            ("muse", "Muse", "Creative ideation and breakthrough thinking"),
            ("oracle", "Oracle", "Risk assessment and evidence-based analysis"),
            ("wisdom", "Wisdom Layer", "Ethical governance and strategic alignment")
        ]
        
        for agent_type, agent_name, description in agents:
            tab = AgentConversationTab(agent_type, agent_name, description)
            tab.message_sent.connect(self.handle_agent_message)
            self.agent_tabs[agent_type] = tab
            self.tab_widget.addTab(tab, f"💭 {agent_name}")
    
    def create_team_tab(self):
        """Create tab for team brainstorming."""
        team_tab = QWidget()
        layout = QVBoxLayout(team_tab)
        
        # Description
        desc_label = QLabel("🎭 Team Brainstorm: Engage the full strategic team in collaborative thinking")
        desc_label.setFont(QFont("Arial", 10, QFont.Bold))
        desc_label.setStyleSheet("color: #34495e; padding: 10px; background-color: #ecf0f1; border-radius: 5px;")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Team conversation area
        self.team_conversation = QTextEdit()
        self.team_conversation.setReadOnly(True)
        self.team_conversation.setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6;")
        layout.addWidget(self.team_conversation)
        
        # Team input
        team_input_layout = QHBoxLayout()
        
        self.team_input = QLineEdit()
        self.team_input.setPlaceholderText("Start a team brainstorming session...")
        team_input_layout.addWidget(self.team_input)
        
        self.team_send_button = QPushButton("Start Team Brainstorm")
        self.team_send_button.clicked.connect(self.start_team_brainstorm)
        self.team_send_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        team_input_layout.addWidget(self.team_send_button)
        
        layout.addLayout(team_input_layout)
        
        self.tab_widget.addTab(team_tab, "🎭 Team Brainstorm")
    
    @pyqtSlot(str, str)
    def handle_agent_message(self, agent_type: str, message: str):
        """Handle message sent to an individual agent."""
        # Get current context (could include current project, goals, etc.)
        context = self.get_current_context()
        
        # Emit signal for backend processing
        self.agent_consultation_requested.emit(agent_type, message, context)
    
    def start_team_brainstorm(self):
        """Start a team brainstorming session."""
        topic = self.team_input.text().strip()
        if not topic:
            return
        
        # Add to team conversation
        self.team_conversation.append(f"🧠 <b>You:</b> {topic}")
        self.team_input.clear()
        
        # Get context and start team brainstorm
        context = self.get_current_context()
        self.team_brainstorm_requested.emit(topic, context)
    
    def get_current_context(self) -> Dict[str, Any]:
        """Get current context for brainstorming."""
        # This would be populated with current project info, goals, etc.
        return {
            "current_project": "Active project context",
            "conversation_history": "Recent conversation context",
            "project_phase": "Current phase of development"
        }
    
    def add_agent_response(self, agent_type: str, response: str):
        """Add an agent response to the appropriate conversation."""
        if agent_type in self.agent_tabs:
            self.agent_tabs[agent_type].add_agent_response(response)
    
    def add_team_response(self, agent_name: str, response: str):
        """Add a team brainstorm response."""
        self.team_conversation.append(f"🤖 <b>{agent_name}:</b> {response}")
        
        # Scroll to bottom
        cursor = self.team_conversation.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.team_conversation.setTextCursor(cursor)
