"""
Human-in-the-Loop Dialog for Open-Source Discovery Approval.

This dialog presents discovered open-source candidates to the architect
for strategic decision-making.
"""

import logging
from typing import List, Optional
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTextEdit, QScrollArea, QWidget,
                             QFrame, QGroupBox, QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QDesktopServices
from PyQt5.QtCore import QUrl

from vibe_coder.core.planning.open_source_scout import OpenSourceCandidate

logger = logging.getLogger(__name__)


class CandidateWidget(QFrame):
    """Widget to display a single open-source candidate."""
    
    def __init__(self, candidate: OpenSourceCandidate, parent=None):
        super().__init__(parent)
        self.candidate = candidate
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the candidate display UI."""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            CandidateWidget {
                border: 2px solid #ddd;
                border-radius: 8px;
                margin: 5px;
                padding: 10px;
                background-color: #f9f9f9;
            }
            CandidateWidget:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Header with name and URL
        header_layout = QHBoxLayout()
        
        name_label = QLabel(self.candidate.name)
        name_label.setFont(QFont("Arial", 12, QFont.Bold))
        header_layout.addWidget(name_label)
        
        header_layout.addStretch()
        
        url_button = QPushButton("View on GitHub")
        url_button.setStyleSheet("QPushButton { color: #0366d6; text-decoration: underline; border: none; }")
        url_button.clicked.connect(lambda: QDesktopServices.openUrl(QUrl(self.candidate.url)))
        header_layout.addWidget(url_button)
        
        layout.addLayout(header_layout)
        
        # Coverage and score
        metrics_layout = QHBoxLayout()
        
        coverage_label = QLabel(f"📊 Coverage: {self.candidate.coverage_percentage:.0%}")
        coverage_label.setStyleSheet("color: #28a745; font-weight: bold;")
        metrics_layout.addWidget(coverage_label)
        
        score_label = QLabel(f"⭐ Score: {self.candidate.recommendation_score:.1f}/1.0")
        score_label.setStyleSheet("color: #fd7e14; font-weight: bold;")
        metrics_layout.addWidget(score_label)
        
        metrics_layout.addStretch()
        layout.addLayout(metrics_layout)
        
        # Description
        desc_label = QLabel("Description:")
        desc_label.setFont(QFont("Arial", 9, QFont.Bold))
        layout.addWidget(desc_label)
        
        desc_text = QLabel(self.candidate.description[:200] + "..." if len(self.candidate.description) > 200 else self.candidate.description)
        desc_text.setWordWrap(True)
        desc_text.setStyleSheet("color: #666; margin-left: 10px;")
        layout.addWidget(desc_text)
        
        # Analysis
        if self.candidate.analysis:
            analysis_label = QLabel("Analysis:")
            analysis_label.setFont(QFont("Arial", 9, QFont.Bold))
            layout.addWidget(analysis_label)
            
            analysis_text = QLabel(self.candidate.analysis)
            analysis_text.setWordWrap(True)
            analysis_text.setStyleSheet("color: #495057; margin-left: 10px; font-style: italic;")
            layout.addWidget(analysis_text)
        
        # Covered tasks
        if self.candidate.covered_tasks:
            tasks_label = QLabel(f"Covers {len(self.candidate.covered_tasks)} tasks:")
            tasks_label.setFont(QFont("Arial", 9, QFont.Bold))
            layout.addWidget(tasks_label)
            
            tasks_text = QLabel(", ".join(self.candidate.covered_tasks[:5]) + 
                              ("..." if len(self.candidate.covered_tasks) > 5 else ""))
            tasks_text.setWordWrap(True)
            tasks_text.setStyleSheet("color: #6c757d; margin-left: 10px; font-size: 8pt;")
            layout.addWidget(tasks_text)


class DiscoveryApprovalDialog(QDialog):
    """
    Dialog for architect approval of open-source discovery results.
    """
    
    # Signals
    candidate_approved = pyqtSignal(OpenSourceCandidate)
    discovery_declined = pyqtSignal()
    
    def __init__(self, candidates: List[OpenSourceCandidate], project_goal: str, parent=None):
        super().__init__(parent)
        self.candidates = candidates
        self.project_goal = project_goal
        self.selected_candidate = None
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the dialog UI."""
        self.setWindowTitle("🔍 Open-Source Discovery Results")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("🎯 Open-Source Discovery Results")
        header_label.setFont(QFont("Arial", 16, QFont.Bold))
        header_label.setAlignment(Qt.AlignCenter)
        header_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        layout.addWidget(header_label)
        
        # Project context
        context_label = QLabel(f"Project: {self.project_goal}")
        context_label.setFont(QFont("Arial", 10))
        context_label.setStyleSheet("color: #495057; margin: 5px; font-style: italic;")
        context_label.setWordWrap(True)
        layout.addWidget(context_label)
        
        # Discovery message
        if self.candidates:
            discovery_msg = QLabel(f"🎉 Great news! I found {len(self.candidates)} high-quality open-source projects that could accelerate your development:")
        else:
            discovery_msg = QLabel("🔍 I searched extensively but couldn't find any open-source projects that meet our quality and coverage thresholds.")
        
        discovery_msg.setWordWrap(True)
        discovery_msg.setStyleSheet("color: #28a745; margin: 10px; font-size: 11pt;")
        layout.addWidget(discovery_msg)
        
        if self.candidates:
            # Candidates scroll area
            scroll_area = QScrollArea()
            scroll_widget = QWidget()
            scroll_layout = QVBoxLayout(scroll_widget)
            
            # Radio button group for selection
            self.candidate_group = QButtonGroup()
            
            for i, candidate in enumerate(self.candidates):
                # Radio button for selection
                radio_button = QRadioButton(f"Select this project")
                radio_button.setStyleSheet("font-weight: bold; color: #007bff;")
                self.candidate_group.addButton(radio_button, i)
                
                # Candidate widget
                candidate_widget = CandidateWidget(candidate)
                
                # Container for radio + candidate
                container = QGroupBox()
                container_layout = QVBoxLayout(container)
                container_layout.addWidget(radio_button)
                container_layout.addWidget(candidate_widget)
                
                scroll_layout.addWidget(container)
            
            scroll_layout.addStretch()
            scroll_area.setWidget(scroll_widget)
            scroll_area.setWidgetResizable(True)
            layout.addWidget(scroll_area)
            
            # Decision buttons
            button_layout = QHBoxLayout()
            
            adopt_button = QPushButton("✅ Adopt Selected Project")
            adopt_button.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    font-weight: bold;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
            adopt_button.clicked.connect(self.approve_selection)
            button_layout.addWidget(adopt_button)
            
            build_scratch_button = QPushButton("🔨 Build from Scratch")
            build_scratch_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    font-weight: bold;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)
            build_scratch_button.clicked.connect(self.decline_discovery)
            button_layout.addWidget(build_scratch_button)
            
            layout.addLayout(button_layout)
        else:
            # No candidates found - only build from scratch option
            build_button = QPushButton("🔨 Proceed with Original Plan")
            build_button.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    font-weight: bold;
                    padding: 15px 30px;
                    border: none;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
            """)
            build_button.clicked.connect(self.decline_discovery)
            
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(build_button)
            button_layout.addStretch()
            layout.addLayout(button_layout)
        
        # Help text
        help_text = QLabel("""
        💡 Tip: Adopting an existing project can save 70-90% of development time. 
        The system will analyze the chosen codebase and create a new task plan focused on customization and enhancement.
        """)
        help_text.setWordWrap(True)
        help_text.setStyleSheet("color: #6c757d; font-size: 9pt; margin: 10px; font-style: italic;")
        layout.addWidget(help_text)
    
    def approve_selection(self):
        """Handle approval of selected candidate."""
        if hasattr(self, 'candidate_group'):
            selected_id = self.candidate_group.checkedId()
            if selected_id >= 0:
                self.selected_candidate = self.candidates[selected_id]
                self.candidate_approved.emit(self.selected_candidate)
                self.accept()
            else:
                # Show message to select a candidate
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "Selection Required", 
                                  "Please select a project before proceeding.")
    
    def decline_discovery(self):
        """Handle declining discovery results."""
        self.discovery_declined.emit()
        self.reject()
