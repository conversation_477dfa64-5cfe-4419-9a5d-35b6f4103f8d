"""
Main window for the Vibe Coder GUI interface.
"""

import sys
import logging
from typing import Op<PERSON>
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QTextEdit, QLineEdit, QPushButton, QLabel,
                             QSplitter, QGroupBox, QProgressBar)
from PyQt5.QtCore import QThread, pyqtSlot
from PyQt5.QtGui import QFont

from vibe_coder.main import AgentBackend

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """
    Main window for the Vibe Coder application.
    Provides a simple interface for interacting with the agent system.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the main window.
        
        Args:
            config_path: Optional path to configuration file
        """
        super().__init__()
        self.config_path = config_path
        self.agent_backend = None
        self.backend_thread = None
        
        self.init_ui()
        self.setup_backend()
        
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("Vibe Coder - AI Coding Assistant")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter()
        main_layout.addWidget(splitter)
        
        # Left panel - Input and controls
        left_panel = self.create_input_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - Output and logs
        right_panel = self.create_output_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 800])
        
        # Status bar
        self.statusBar().showMessage("Initializing Vibe Coder...")
        
        logger.info("UI initialized")
    
    def create_input_panel(self) -> QWidget:
        """Create the input panel with controls."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Goal input section
        goal_group = QGroupBox("Project Goal")
        goal_layout = QVBoxLayout(goal_group)
        
        self.goal_input = QTextEdit()
        self.goal_input.setPlaceholderText("Enter your project goal here...\n\nExample: Create a simple web scraper that extracts product prices from an e-commerce website")
        self.goal_input.setMaximumHeight(150)
        goal_layout.addWidget(self.goal_input)
        
        # Description input
        desc_label = QLabel("Description (optional):")
        goal_layout.addWidget(desc_label)
        
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("Additional details about your project...")
        self.description_input.setMaximumHeight(100)
        goal_layout.addWidget(self.description_input)
        
        layout.addWidget(goal_group)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("Start Project")
        self.start_button.clicked.connect(self.start_project)
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("Stop")
        self.stop_button.clicked.connect(self.stop_project)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; padding: 10px; }")
        button_layout.addWidget(self.stop_button)
        
        layout.addLayout(button_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # System status
        status_group = QGroupBox("System Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("Status: Initializing...")
        status_layout.addWidget(self.status_label)
        
        layout.addWidget(status_group)
        
        # Add stretch to push everything to the top
        layout.addStretch()
        
        return panel
    
    def create_output_panel(self) -> QWidget:
        """Create the output panel with logs and results."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Log output
        log_group = QGroupBox("System Log")
        log_layout = QVBoxLayout(log_group)
        
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        self.log_output.setFont(QFont("Consolas", 9))
        self.log_output.setStyleSheet("QTextEdit { background-color: #2b2b2b; color: #ffffff; }")
        log_layout.addWidget(self.log_output)
        
        layout.addWidget(log_group)
        
        # Results output
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_output = QTextEdit()
        self.results_output.setReadOnly(True)
        self.results_output.setFont(QFont("Consolas", 9))
        results_layout.addWidget(self.results_output)
        
        layout.addWidget(results_group)
        
        return panel
    
    def setup_backend(self):
        """Setup the agent backend in a separate thread."""
        try:
            # Create backend
            self.agent_backend = AgentBackend(self.config_path)
            
            # Create thread
            self.backend_thread = QThread()
            self.agent_backend.moveToThread(self.backend_thread)
            
            # Connect signals
            self.agent_backend.log_signal.connect(self.on_log_message)
            self.agent_backend.status_signal.connect(self.on_status_update)
            self.agent_backend.result_signal.connect(self.on_result_received)
            
            # Connect thread signals
            self.backend_thread.started.connect(self.agent_backend.run)
            
            # Start the backend thread
            self.backend_thread.start()
            
            logger.info("Backend setup complete")
            
        except Exception as e:
            logger.error(f"Failed to setup backend: {e}")
            self.log_output.append(f"ERROR: Failed to setup backend: {str(e)}")
    
    @pyqtSlot(str)
    def on_log_message(self, message: str):
        """Handle log messages from the backend."""
        self.log_output.append(message)
        self.log_output.ensureCursorVisible()
    
    @pyqtSlot(str)
    def on_status_update(self, status: str):
        """Handle status updates from the backend."""
        self.status_label.setText(f"Status: {status}")
        self.statusBar().showMessage(status)
        
        if status == "Ready":
            self.start_button.setEnabled(True)
            self.progress_bar.setVisible(False)
        elif status == "Error":
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.progress_bar.setVisible(False)
    
    @pyqtSlot(dict)
    def on_result_received(self, result: dict):
        """Handle results from the backend."""
        self.results_output.clear()
        
        if result.get("success"):
            self.results_output.append("✅ Project completed successfully!\n")
            
            results = result.get("results", [])
            self.results_output.append(f"Tasks completed: {len(results)}\n")
            
            for i, task_result in enumerate(results, 1):
                self.results_output.append(f"Task {i}:")
                self.results_output.append(f"  Success: {task_result.get('success', False)}")
                self.results_output.append(f"  Tool used: {task_result.get('tool_used', 'N/A')}")
                self.results_output.append(f"  Result: {task_result.get('result', 'N/A')[:200]}...")
                self.results_output.append("")
        else:
            self.results_output.append("❌ Project failed!\n")
            self.results_output.append(f"Error: {result.get('error', 'Unknown error')}")
        
        # Re-enable controls
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)
    
    def start_project(self):
        """Start a new project."""
        goal = self.goal_input.toPlainText().strip()
        if not goal:
            self.log_output.append("ERROR: Please enter a project goal")
            return
        
        description = self.description_input.toPlainText().strip()
        
        # Update UI state
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        self.results_output.clear()
        
        # Submit goal to backend
        if self.agent_backend:
            self.agent_backend.submit_goal(goal, description)
        else:
            self.log_output.append("ERROR: Backend not available")
    
    def stop_project(self):
        """Stop the current project."""
        self.log_output.append("🛑 Stopping project execution...")

        # Signal backend to stop
        if self.agent_backend:
            self.agent_backend.stop_execution()

        # Reset UI state
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.progress_bar.setVisible(False)

        self.log_output.append("✅ Project execution stopped.")
    
    def closeEvent(self, event):
        """Handle window close event."""
        if self.backend_thread and self.backend_thread.isRunning():
            self.backend_thread.quit()
            self.backend_thread.wait()
        event.accept()


def launch_gui(config_path: Optional[str] = None):
    """Launch the GUI application."""
    app = QApplication(sys.argv)
    app.setApplicationName("Vibe Coder")
    
    window = MainWindow(config_path)
    window.show()
    
    sys.exit(app.exec_())
