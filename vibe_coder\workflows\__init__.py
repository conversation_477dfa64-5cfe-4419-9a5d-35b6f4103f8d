"""
Workflows Module: LangGraph-based workflow definitions for the Vibe Coder system.

This module contains:
- StrategicWorkflow: Strategic debate and planning workflow
- ExecutionWorkflow: Enhanced execution workflow with cognitive loops
"""

from .strategic_workflow import StrategicWorkflow
from .execution_workflow import ExecutionWorkflow

__all__ = [
    'StrategicWorkflow',
    'ExecutionWorkflow'
]
