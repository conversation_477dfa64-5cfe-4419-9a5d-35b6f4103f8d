"""
Execution Workflow: Enhanced LangGraph workflow for task execution.
Implements the Think -> Act -> Verify -> Reflect cognitive cycle.
"""

import logging
from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from vibe_coder.engine.state import AgentState

logger = logging.getLogger(__name__)


class ExecutionWorkflow:
    """
    LangGraph workflow for enhanced task execution.
    
    This workflow implements the architect's vision of a cognitive cycle
    with Think -> Act -> Verify -> Reflect for each task node.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Execution Workflow.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        
        # Build the workflow graph
        self.workflow = self._build_workflow_graph()
        
        logger.info("ExecutionWorkflow initialized with LangGraph")
    
    def _build_workflow_graph(self) -> StateGraph:
        """
        Build the LangGraph workflow for task execution.
        
        Returns:
            Compiled StateGraph for execution workflow
        """
        # Create the graph
        workflow = StateGraph(AgentState)
        
        # Add nodes for cognitive cycle
        workflow.add_node("think", self._think_node)
        workflow.add_node("act", self._act_node)
        workflow.add_node("verify", self._verify_node)
        workflow.add_node("reflect", self._reflect_node)
        
        # Add edges for cognitive cycle
        workflow.set_entry_point("think")
        workflow.add_edge("think", "act")
        workflow.add_edge("act", "verify")
        workflow.add_edge("verify", "reflect")
        workflow.add_edge("reflect", END)
        
        return workflow.compile()
    
    def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task using the cognitive cycle.
        
        Args:
            task: Task dictionary to execute
            
        Returns:
            Dictionary containing execution results
        """
        try:
            logger.info(f"Starting task execution: {task.get('description', 'Unknown task')}")
            
            # Initialize state
            initial_state = AgentState(
                current_task=task,
                cognitive_cycle_stage="think",
                workflow_stage="execution",
                hypothesis_under_test=task.get('hypothesis', 'Task will complete successfully'),
                history=[]
            )
            
            # Execute workflow
            final_state = self.workflow.invoke(initial_state)
            
            # Extract results
            results = {
                "success": True,
                "task": final_state.current_task,
                "final_output": final_state.final_output,
                "cognitive_cycle_completed": True,
                "reflection_output": final_state.reflection_output,
                "verification_output": final_state.verification_output,
                "history": final_state.history
            }
            
            logger.info("Task execution completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "cognitive_cycle_completed": False
            }
    
    def _think_node(self, state: AgentState) -> AgentState:
        """
        Think node: Analyze task and plan approach.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            logger.info("Cognitive cycle: THINK phase")
            
            task = state.current_task
            
            # Simulate thinking process
            thought_process = f"""
            Analyzing task: {task.get('description', 'Unknown')}
            Task type: {task.get('type', 'general')}
            
            Planning approach:
            1. Understand requirements
            2. Identify tools needed
            3. Plan execution steps
            4. Consider potential issues
            """
            
            # Update state
            state.cognitive_cycle_stage = "think"
            state.thought_process = thought_process
            state.history.append("THINK: Analyzed task and planned approach")
            
            logger.info("Think phase completed")
            return state
            
        except Exception as e:
            logger.error(f"Think phase failed: {e}")
            state.error_message = str(e)
            return state
    
    def _act_node(self, state: AgentState) -> AgentState:
        """
        Act node: Execute the planned action.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            logger.info("Cognitive cycle: ACT phase")
            
            task = state.current_task
            
            # Simulate action execution
            action_result = f"Executed action for task: {task.get('description', 'Unknown')}"
            
            # Update state
            state.cognitive_cycle_stage = "act"
            state.tool_invocation_result = action_result
            state.history.append("ACT: Executed planned action")
            
            logger.info("Act phase completed")
            return state
            
        except Exception as e:
            logger.error(f"Act phase failed: {e}")
            state.error_message = str(e)
            return state
    
    def _verify_node(self, state: AgentState) -> AgentState:
        """
        Verify node: Validate the action results.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            logger.info("Cognitive cycle: VERIFY phase")
            
            # Simulate verification
            verification_result = "Action completed successfully - verification passed"
            
            # Update state
            state.cognitive_cycle_stage = "verify"
            state.verification_output = verification_result
            state.history.append("VERIFY: Validated action results")
            
            logger.info("Verify phase completed")
            return state
            
        except Exception as e:
            logger.error(f"Verify phase failed: {e}")
            state.error_message = str(e)
            return state
    
    def _reflect_node(self, state: AgentState) -> AgentState:
        """
        Reflect node: Learn from the execution.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            logger.info("Cognitive cycle: REFLECT phase")
            
            # Simulate reflection
            reflection_result = {
                "success": True,
                "lessons_learned": ["Task completed successfully", "Approach was effective"],
                "improvements": ["Could optimize execution time"],
                "confidence": 0.9
            }
            
            # Update state
            state.cognitive_cycle_stage = "reflect"
            state.reflection_output = reflection_result
            state.final_output = "Task execution completed with reflection"
            state.is_final_answer = True
            state.history.append("REFLECT: Learned from execution")
            
            logger.info("Reflect phase completed")
            return state
            
        except Exception as e:
            logger.error(f"Reflect phase failed: {e}")
            state.error_message = str(e)
            return state
