"""
Strategic Workflow: LangGraph workflow for strategic debate and planning.
Implements the pre-execution strategic loop with Muse vs Oracle dialectic.
"""

import logging
from typing import Dict, Any, List, Optional
from langgraph.graph import StateGraph, END
from vibe_coder.engine.state import AgentState
from vibe_coder.engine.whiteboard_session import WhiteboardSession
from vibe_coder.core.strategy.strategic_debate import StrategicDebate
from vibe_coder.core.strategy.wisdom_layer import Wisdom<PERSON>ayer
from vibe_coder.memory.lessons_learned import LessonsLearnedManager

logger = logging.getLogger(__name__)


class StrategicWorkflow:
    """
    LangGraph workflow for strategic planning and debate.
    
    This workflow implements the architect's vision of a pre-execution
    strategic loop that conducts Muse vs Oracle debate and synthesizes
    results through the Wisdom Layer.
    """
    
    def __init__(self, config: Dict[str, Any], lessons_manager: LessonsLearnedManager):
        """
        Initialize the Strategic Workflow.
        
        Args:
            config: Configuration dictionary
            lessons_manager: Lessons learned manager
        """
        self.config = config
        self.lessons_manager = lessons_manager
        self.whiteboard_session = WhiteboardSession(config, lessons_manager)
        self.strategic_debate = StrategicDebate(config)
        self.wisdom_layer = WisdomLayer(config)
        
        # Build the workflow graph
        self.workflow = self._build_workflow_graph()
        
        logger.info("StrategicWorkflow initialized with LangGraph")
    
    def _build_workflow_graph(self) -> StateGraph:
        """
        Build the LangGraph workflow for strategic planning.
        
        Returns:
            Compiled StateGraph for strategic workflow
        """
        # Create the graph
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("consult_lessons", self._consult_lessons_node)
        workflow.add_node("strategic_debate", self._strategic_debate_node)
        workflow.add_node("wisdom_layer_synthesis", self._wisdom_synthesis_node)
        workflow.add_node("finalize_strategy", self._finalize_strategy_node)
        
        # Add edges
        workflow.set_entry_point("consult_lessons")
        workflow.add_edge("consult_lessons", "strategic_debate")
        workflow.add_edge("strategic_debate", "wisdom_layer_synthesis")
        workflow.add_edge("wisdom_layer_synthesis", "finalize_strategy")
        workflow.add_edge("finalize_strategy", END)
        
        return workflow.compile()
    
    def execute_strategic_planning(self, charter: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the strategic planning workflow.
        
        Args:
            charter: Project charter dictionary
            
        Returns:
            Dictionary containing strategic planning results
        """
        try:
            logger.info("Starting strategic planning workflow")
            
            # Initialize state
            initial_state = AgentState(
                charter=charter,
                current_task={"description": "Strategic Planning", "type": "strategic"},
                strategic_context={},
                lessons_consulted=[],
                debate_results={},
                wisdom_synthesis={},
                execution_strategy={},
                workflow_stage="strategic_planning"
            )
            
            # Execute workflow
            final_state = self.workflow.invoke(initial_state)
            
            # Extract results
            results = {
                "success": True,
                "charter": final_state.charter,
                "lessons_consulted": final_state.lessons_consulted,
                "debate_results": final_state.debate_results,
                "wisdom_synthesis": final_state.wisdom_synthesis,
                "execution_strategy": final_state.execution_strategy,
                "strategic_recommendation": final_state.execution_strategy.get("strategic_approach", ""),
                "workflow_completed": True
            }
            
            logger.info("Strategic planning workflow completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Strategic planning workflow failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "workflow_completed": False
            }
    
    def _consult_lessons_node(self, state: AgentState) -> AgentState:
        """
        Node for consulting lessons learned.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            logger.info("Consulting lessons learned")
            
            # Build context for lesson retrieval
            charter = state.get('charter', {})
            context = f"{charter.get('goal', '')} {charter.get('description', '')}"
            
            # Get relevant lessons
            relevant_lessons = self.lessons_manager.get_relevant_lessons(context)
            best_practices = self.lessons_manager.get_lessons_by_type("best_practice")
            anti_patterns = self.lessons_manager.get_lessons_by_type("anti_pattern")
            
            # Update state
            state['lessons_consulted'] = {
                "relevant_lessons": relevant_lessons,
                "best_practices": best_practices[:3],
                "anti_patterns": anti_patterns[:3],
                "consultation_timestamp": "now"
            }

            if 'strategic_context' not in state:
                state['strategic_context'] = {}
            state['strategic_context']["lessons_insights"] = [
                lesson.get("description", "") for lesson in relevant_lessons[:5]
            ]
            
            logger.info(f"Consulted {len(relevant_lessons)} relevant lessons")
            return state
            
        except Exception as e:
            logger.error(f"Lessons consultation failed: {e}")
            state['lessons_consulted'] = {"error": str(e)}
            return state
    
    def _strategic_debate_node(self, state: AgentState) -> AgentState:
        """
        Node for conducting strategic debate.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            logger.info("Conducting strategic debate")
            
            # Build problem context
            charter = state.get('charter', {})
            strategic_context = state.get('strategic_context', {})
            problem_context = {
                "problem": charter.get("goal", ""),
                "description": charter.get("description", ""),
                "goals": [charter.get("goal", "")],
                "constraints": charter.get("constraints", []),
                "lessons_insights": strategic_context.get("lessons_insights", [])
            }
            
            # Conduct debate
            debate_results = self.strategic_debate.conduct_debate(problem_context, rounds=3)
            
            # Update state
            state['debate_results'] = debate_results
            if 'strategic_context' not in state:
                state['strategic_context'] = {}
            state['strategic_context']["debate_completed"] = True
            
            logger.info("Strategic debate completed")
            return state
            
        except Exception as e:
            logger.error(f"Strategic debate failed: {e}")
            state['debate_results'] = {"success": False, "error": str(e)}
            return state
    
    def _wisdom_synthesis_node(self, state: AgentState) -> AgentState:
        """
        Node for wisdom synthesis.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            logger.info("Conducting wisdom synthesis")
            
            debate_results = state.get('debate_results', {})

            if not debate_results.get("success"):
                logger.warning("Skipping wisdom synthesis due to debate failure")
                state['wisdom_synthesis'] = {"skipped": True, "reason": "debate_failed"}
                return state
            
            # Extract final proposals from debate
            final_synthesis = debate_results.get("final_synthesis", {})
            
            # Get Muse and Oracle perspectives
            debate_rounds = debate_results.get("debate_rounds", [])
            if debate_rounds:
                final_round = debate_rounds[-1]
                muse_proposal = final_round.get("muse_refinement", {})
                oracle_assessment = final_round.get("oracle_assessment", {})
                
                # Conduct wisdom synthesis
                wisdom_result = self.wisdom_layer.synthesize_wisdom(muse_proposal, oracle_assessment)
                
                # Evaluate alignment with project goals
                charter = state.get('charter', {})
                project_goals = {
                    "goals": [charter.get("goal", "")],
                    "vision": charter.get("description", "")
                }
                
                alignment_result = self.wisdom_layer.evaluate_alignment(
                    {"description": wisdom_result.get("wisdom_synthesis", "")},
                    project_goals
                )
                
                # Update state
                state['wisdom_synthesis'] = {
                    "synthesis_result": wisdom_result,
                    "alignment_evaluation": alignment_result,
                    "final_wisdom": wisdom_result.get("final_recommendation", ""),
                    "governance_principles": wisdom_result.get("governance_principles", [])
                }
            else:
                state['wisdom_synthesis'] = {"error": "No debate rounds found"}
            
            logger.info("Wisdom synthesis completed")
            return state
            
        except Exception as e:
            logger.error(f"Wisdom synthesis failed: {e}")
            state['wisdom_synthesis'] = {"error": str(e)}
            return state
    
    def _finalize_strategy_node(self, state: AgentState) -> AgentState:
        """
        Node for finalizing execution strategy.
        
        Args:
            state: Current agent state
            
        Returns:
            Updated agent state
        """
        try:
            logger.info("Finalizing execution strategy")
            
            # Compile execution strategy from all phases
            debate_results = state.get('debate_results', {})
            wisdom_synthesis = state.get('wisdom_synthesis', {})
            lessons_consulted = state.get('lessons_consulted', {})
            
            # Extract strategic approach
            strategic_approach = "Unknown"
            if debate_results.get("success"):
                strategic_approach = debate_results.get("strategic_recommendation", "Proceed with analysis")
            
            # Extract risk mitigation
            risk_mitigation = []
            if debate_results.get("final_synthesis", {}).get("risk_mitigation_strategy"):
                risk_mitigation = debate_results["final_synthesis"]["risk_mitigation_strategy"].get("mitigation_strategies", [])
            
            # Extract implementation guidance
            implementation_guidance = {}
            if debate_results.get("final_synthesis", {}).get("implementation_guidance"):
                implementation_guidance = debate_results["final_synthesis"]["implementation_guidance"]
            
            # Build final execution strategy
            execution_strategy = {
                "strategic_approach": strategic_approach,
                "risk_mitigation": risk_mitigation[:5],
                "implementation_approach": implementation_guidance.get("recommended_approach", "incremental"),
                "success_factors": implementation_guidance.get("key_success_factors", []),
                "critical_risks": implementation_guidance.get("critical_risks_to_monitor", []),
                "wisdom_guidance": wisdom_synthesis.get("final_wisdom", ""),
                "governance_principles": wisdom_synthesis.get("governance_principles", []),
                "lessons_to_apply": self._extract_applicable_lessons(lessons_consulted),
                "confidence_score": self._calculate_strategy_confidence(state),
                "strategy_summary": self._create_strategy_summary(state)
            }
            
            # Update state
            state['execution_strategy'] = execution_strategy
            state['workflow_stage'] = "strategy_complete"
            
            logger.info("Execution strategy finalized")
            return state
            
        except Exception as e:
            logger.error(f"Strategy finalization failed: {e}")
            state['execution_strategy'] = {"error": str(e)}
            return state
    
    def _extract_applicable_lessons(self, lessons_consulted: Dict[str, Any]) -> List[Dict[str, str]]:
        """Extract applicable lessons for execution."""
        applicable_lessons = []
        
        # Best practices
        best_practices = lessons_consulted.get("best_practices", [])
        for bp in best_practices:
            applicable_lessons.append({
                "type": "best_practice",
                "description": bp.get("description", ""),
                "confidence": str(bp.get("confidence", 0))
            })
        
        # Anti-patterns to avoid
        anti_patterns = lessons_consulted.get("anti_patterns", [])
        for ap in anti_patterns:
            applicable_lessons.append({
                "type": "anti_pattern",
                "description": ap.get("description", ""),
                "confidence": str(ap.get("confidence", 0))
            })
        
        return applicable_lessons[:5]
    
    def _calculate_strategy_confidence(self, state: AgentState) -> float:
        """Calculate confidence score for the strategy."""
        confidence_factors = []
        
        # Debate success
        if state.get('debate_results', {}).get("success"):
            confidence_factors.append(0.4)

        # Wisdom synthesis success
        if state.get('wisdom_synthesis', {}).get("synthesis_result", {}).get("success"):
            confidence_factors.append(0.3)

        # Lessons availability
        lessons_count = len(state.get('lessons_consulted', {}).get("relevant_lessons", []))
        confidence_factors.append(min(lessons_count / 10, 0.2))

        # Alignment score
        alignment_score = state.get('wisdom_synthesis', {}).get("alignment_evaluation", {}).get("alignment_score", 5)
        confidence_factors.append((alignment_score / 10) * 0.1)
        
        return sum(confidence_factors)
    
    def _create_strategy_summary(self, state: AgentState) -> str:
        """Create a summary of the strategic planning process."""
        debate_rounds = len(state.get('debate_results', {}).get("debate_rounds", []))
        lessons_count = len(state.get('lessons_consulted', {}).get("relevant_lessons", []))

        summary = f"""
        Strategic Planning Summary:
        - Consulted {lessons_count} relevant lessons from past experience
        - Conducted {debate_rounds} rounds of Muse vs Oracle debate
        - Applied Wisdom Layer synthesis for ethical governance
        - Generated comprehensive execution strategy

        Recommendation: {state.get('execution_strategy', {}).get('strategic_approach', 'Continue analysis')}
        """
        
        return summary.strip()
