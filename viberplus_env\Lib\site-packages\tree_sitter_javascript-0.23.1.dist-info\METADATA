Metadata-Version: 2.1
Name: tree-sitter-javascript
Version: 0.23.1
Summary: JavaScript grammar for tree-sitter
Author-email: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/tree-sitter/tree-sitter-javascript
Keywords: incremental,parsing,tree-sitter,javascript
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Topic :: Software Development :: Compilers
Classifier: Topic :: Text Processing :: Linguistic
Classifier: Typing :: Typed
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Provides-Extra: core
Requires-Dist: tree-sitter~=0.22; extra == "core"

# tree-sitter-javascript

[![CI][ci]](https://github.com/tree-sitter/tree-sitter-javascript/actions/workflows/ci.yml)
[![discord][discord]](https://discord.gg/w7nTvsVJhm)
[![matrix][matrix]](https://matrix.to/#/#tree-sitter-chat:matrix.org)
[![crates][crates]](https://crates.io/crates/tree-sitter-javascript)
[![npm][npm]](https://www.npmjs.com/package/tree-sitter-javascript)
[![pypi][pypi]](https://pypi.org/project/tree-sitter-javascript)

JavaScript and JSX grammar for [tree-sitter](https://github.com/tree-sitter/tree-sitter).

References

- [The ESTree Spec](https://github.com/estree/estree)
- [The ECMAScript 2015 Spec](http://www.ecma-international.org/ecma-262/6.0/)

[ci]: https://img.shields.io/github/actions/workflow/status/tree-sitter/tree-sitter-javascript/ci.yml?logo=github&label=CI
[discord]: https://img.shields.io/discord/1063097320771698699?logo=discord&label=discord
[matrix]: https://img.shields.io/matrix/tree-sitter-chat%3Amatrix.org?logo=matrix&label=matrix
[npm]: https://img.shields.io/npm/v/tree-sitter-javascript?logo=npm
[crates]: https://img.shields.io/crates/v/tree-sitter-javascript?logo=rust
[pypi]: https://img.shields.io/pypi/v/tree-sitter-javascript?logo=pypi&logoColor=ffd242
