Metadata-Version: 2.1
Name: tree-sitter-languages
Version: 1.10.2
Summary: Binary Python wheels for all tree sitter languages.
Home-page: https://github.com/grantjenks/py-tree-sitter-languages
Author: <PERSON>
Author-email: <EMAIL>
License: Apache 2.0
Project-URL: Documentation, https://github.com/grantjenks/py-tree-sitter-languages
Project-URL: Source, https://github.com/grantjenks/py-tree-sitter-languages
Project-URL: Tracker, https://github.com/grantjenks/py-tree-sitter-languages/issues
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: Implementation :: CPython
License-File: LICENSE
Requires-Dist: tree-sitter

==================================================
Python Bindings for Tree Sitter with All Languages
==================================================

Binary Python wheels for all tree sitter languages.

`py-tree-sitter`_ is a fantastic library that provides Python bindings for the
even more fantastic `tree-sitter`_ parsing library.

`py-tree-sitter-languages`_ provides binary Python wheels for all tree sitter
languages. The binary wheels remove the need to download and compile support
for individual languages.

.. _`py-tree-sitter-languages`: https://github.com/grantjenks/py-tree-sitter-languages


Install
=======

::

   pip install tree_sitter_languages

Source installs are not supported. To see how the binary wheels are built, look
at:

1. `setup.py` â€” Python package setup.

2. `repos.txt` â€” Text file that contains a list of included language repositories and their commit hashes.

3. `build.py` â€” Python script to download and build the language repositories.

4. `.github/workflows/release.yml` â€” GitHub action to invoke `cibuildwheel`_ and
   release to PyPI.

.. _`cibuildwheel`: https://github.com/pypa/cibuildwheel


Usage
=====

.. code:: python

   from tree_sitter_languages import get_language, get_parser

   language = get_language('python')
   parser = get_parser('python')

That's the whole API!

Refer to `py-tree-sitter`_ for the language and parser API. Notice the
``Language.build_library(...)`` step can be skipped! The binary wheel includes
the language binary.

.. _`py-tree-sitter`: https://github.com/tree-sitter/py-tree-sitter


Demo
====

Want to know something crazy? Python lacks multi-line comments. Whhaaa!?!

It's really not such a big deal. Instead of writing

.. code:: python

   """
   My awesome
   multi-line
   comment.
   """

Simply write

.. code:: python

   # My awesome
   # multi-line
   # comment.

So multi-line comments are made by putting multiple single-line comments in
sequence. Amazing!

Now, how to find all the strings being used as comments?

Start with some example Python code

.. code:: python

   example = """
   #!shebang
   # License blah blah (Apache 2.0)
   "This is a module docstring."

   a = 1

   '''This
   is
   not
   a
   multiline
   comment.'''

   b = 2

   class Test:
       "This is a class docstring."

       'This is bogus.'

       def test(self):
           "This is a function docstring."

           "Please, no."

           return 1

   c = 3
   """

Notice a couple things:

1. Python has module, class, and function docstrings that bare a striking
   resemblance to the phony string comments.

2. Python supports single-quoted, double-quoted, triple-single-quoted, and
   triple-double-quoted strings (not to mention prefixes for raw strings,
   unicode strings, and more).

Creating a regular expression to capture the phony string comments would be
exceedingly difficult!

Enter `tree-sitter`_

.. code:: python

   from tree_sitter_languages import get_language, get_parser

   language = get_language('python')
   parser = get_parser('python')

Tree-sitter creates an abstract syntax tree (actually, a `concrete syntax
tree`_) and supports queries

.. code:: python

   tree = parser.parse(example.encode())
   node = tree.root_node
   print(node.sexp())

.. _`concrete syntax tree`: https://stackoverflow.com/q/1888854/232571

Look for statements that are a single string expression

.. code:: python

   stmt_str_pattern = '(expression_statement (string)) @stmt_str'
   stmt_str_query = language.query(stmt_str_pattern)
   stmt_strs = stmt_str_query.captures(node)
   stmt_str_points = set(
       (node.start_point, node.end_point) for node, _ in stmt_strs
   )
   print(stmt_str_points)

Now, find those statement string expressions that are actually module, class,
or function docstrings

.. code:: python

   doc_str_pattern = """
       (module . (comment)* . (expression_statement (string)) @module_doc_str)

       (class_definition
           body: (block . (expression_statement (string)) @class_doc_str))

       (function_definition
           body: (block . (expression_statement (string)) @function_doc_str))
   """
   doc_str_query = language.query(doc_str_pattern)
   doc_strs = doc_str_query.captures(node)
   doc_str_points = set(
       (node.start_point, node.end_point) for node, _ in doc_strs
   )

With the set of string expression statements and the set of docstring
statements, the locations of all phony string comments is

.. code:: python

   comment_strs = stmt_str_points - doc_str_points
   print(sorted(comment_strs))


License
=======

Copyright 2022-2023 Grant Jenks

Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License.  You may obtain a copy of the
License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software distributed
under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.

The project also includes the following other projects distributed in binary
form:

* https://github.com/tree-sitter/tree-sitter â€” licensed under the MIT License.

* https://github.com/WhatsApp/tree-sitter-erlang â€” licensed under
  the Apache License, Version 2.0.

* https://github.com/Azganoth/tree-sitter-lua â€” licensed under the MIT
  License.

* https://github.com/Wilfred/tree-sitter-elisp â€” licensed under the MIT
  License.

* https://github.com/alemuller/tree-sitter-make â€” licensed under the MIT
  License.

* https://github.com/camdencheek/tree-sitter-dockerfile â€” licensed under the
  MIT License.

* https://github.com/camdencheek/tree-sitter-go-mod â€” licensed under the MIT
  License.

* https://github.com/elixir-lang/tree-sitter-elixir â€” licensed under the
  Apache License, Version 2.0.

* https://github.com/elm-tooling/tree-sitter-elm â€” licensed under the MIT
  License.

* https://github.com/fwcd/tree-sitter-kotlin â€” licensed under the MIT License.

* https://github.com/ganezdragon/tree-sitter-perl â€” licensed under the MIT
  License.

* https://github.com/ikatyang/tree-sitter-markdown â€” licensed under the MIT
  License.

* https://github.com/ikatyang/tree-sitter-yaml â€” licensed under the MIT
  License.

* https://github.com/jiyee/tree-sitter-objc â€” licensed under the MIT License.

* https://github.com/m-novikov/tree-sitter-sql â€” licensed under the MIT
  License.

* https://github.com/r-lib/tree-sitter-r â€” licensed under the MIT License.

* https://github.com/rydesun/tree-sitter-dot â€” licensed under the MIT License.

* https://github.com/slackhq/tree-sitter-hack â€” licensed under the MIT
  License.

* https://github.com/theHamsta/tree-sitter-commonlisp â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-bash â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-c â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-c-sharp â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-cpp â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-css â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-embedded-template â€” licensed
  under the MIT License.

* https://github.com/tree-sitter/tree-sitter-go â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-haskell â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-html â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-java â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-javascript â€” licensed under the
  MIT License.

* https://github.com/tree-sitter/tree-sitter-jsdoc â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-json â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-julia â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-ocaml â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-php â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-python â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-ql â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-regex â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-ruby â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-rust â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-scala â€” licensed under the MIT
  License.

* https://github.com/dhcmrlchtdj/tree-sitter-sqlite - licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-toml â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-tsq â€” licensed under the MIT
  License.

* https://github.com/tree-sitter/tree-sitter-typescript â€” licensed under the
  MIT License.

* https://github.com/stsewd/tree-sitter-rst - licensed under the MIT License.

* https://github.com/MichaHoffmann/tree-sitter-hcl - licensed under the
  Apache License, Version 2.0.

* https://github.com/stadelmanma/tree-sitter-fortran - licensed under the MIT
  License.

* https://github.com/stadelmanma/tree-sitter-fixed-form-fortran - licensed under
  the MIT License.

.. _`tree-sitter`: https://tree-sitter.github.io/
